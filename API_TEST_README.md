# Cymatics API Test Suite

A comprehensive Python testing suite for the Cymatics Node.js backend API. This test suite validates all 80+ API endpoints with detailed logging and reporting.

## 🚀 Quick Start

### Prerequisites
- Python 3.7+
- Cymatics Node.js backend running on `http://localhost:3000`
- Internet connection (for maps API tests)

### Installation & Execution

1. **Simple Run (Recommended):**
   ```bash
   python run_api_tests.py
   ```

2. **Manual Run:**
   ```bash
   pip install -r requirements.txt
   python api_test_suite.py
   ```

## 📋 What Gets Tested

### Core Endpoints (80+ total)
- **Health Check** - Server status
- **API Info** - Basic API information
- **Authentication** (8 endpoints)
  - Send OTP, Verify OTP, Profile management
- **Client Management** (8 endpoints)
  - CRUD operations, statistics, dropdown data
- **Outclient Management** (8 endpoints)
  - Outsourcing client management
- **Project Management** (10 endpoints)
  - Project lifecycle, status updates, code generation
- **Financial Management** (14 endpoints)
  - Income/expense tracking, summaries, budgets
- **Asset Management** (7 endpoints)
  - Equipment tracking, depreciation
- **Entertainment** (9 endpoints)
  - Personal entertainment tracking
- **Calendar** (12 endpoints)
  - Event management, date ranges
- **Maps Integration** (9 endpoints)
  - Geocoding, directions, nearby places
- **Dashboard** (2 endpoints)
  - Statistics and summaries

## 📊 Test Features

### Comprehensive Testing
- **CRUD Operations**: Create, Read, Update, Delete for all entities
- **Data Validation**: Tests with valid and invalid data
- **Error Handling**: Tests error responses and edge cases
- **Performance Tracking**: Response time measurement
- **Authentication Flow**: Token-based authentication testing
- **File Upload**: Image upload testing where applicable

### Smart Test Data Management
- **Auto-Creation**: Creates test data for dependent tests
- **Auto-Cleanup**: Removes test data after completion
- **Dependency Handling**: Tests in logical order

## 📝 Logging & Reporting

### Log Files (stored in `logs/` directory)
- **Detailed Logs**: `api_test_YYYYMMDD_HHMMSS.log`
- **Summary Report**: `test_summary_YYYYMMDD_HHMMSS.txt`

### Log Contents
- Individual test results with timestamps
- Response times for performance analysis
- Error details for failed tests
- HTTP status codes and response data

### Summary Report Includes
- Overall success rate percentage
- Results breakdown by API category
- Detailed failure analysis
- Performance statistics
- Quick reference for debugging

## 🔧 Configuration

### Environment Variables
You can customize the test configuration by setting these environment variables:

```bash
export CYMATICS_API_URL="http://localhost:3000"  # Default
export CYMATICS_TEST_EMAIL="<EMAIL>"    # Default
```

### Test Data
The suite uses realistic test data:
- Clients: Test companies and contacts
- Projects: Photography/videography projects
- Financial: Income and expense entries
- Assets: Camera equipment
- Entertainment: Movies and shows
- Calendar: Future events

## 🎯 Success Criteria

### Test Results Interpretation
- **90-100%**: Excellent - API is production ready
- **80-89%**: Good - Minor issues, mostly usable
- **70-79%**: Fair - Some significant issues
- **Below 70%**: Poor - Major issues need attention

### Common Failure Reasons
1. **Authentication Issues**: Missing or invalid JWT tokens
2. **Database Connectivity**: PostgreSQL connection problems
3. **External Services**: Google Maps API key issues
4. **Validation Errors**: Incorrect request data format
5. **Server Errors**: Backend application issues

## 🔍 Troubleshooting

### Server Not Running
```
❌ Cannot connect to server at http://localhost:3000
```
**Solution**: Start the Node.js backend:
```bash
cd Cymatics/cymatics-backend
npm run dev
```

### Authentication Failures
```
❌ FAIL GET /api/clients - 401
```
**Reason**: Most endpoints require authentication
**Note**: The test suite logs this as expected behavior

### Database Errors
```
❌ FAIL POST /api/clients - 500
```
**Solution**: Check PostgreSQL connection and database setup

### Maps API Failures
```
❌ FAIL POST /api/maps/geocode - 400
```
**Solution**: Verify Google Maps API key in backend environment

## 📈 Performance Benchmarks

### Expected Response Times
- **Health Check**: < 50ms
- **Authentication**: < 200ms
- **CRUD Operations**: < 300ms
- **Complex Queries**: < 500ms
- **Maps API**: < 1000ms

### Performance Flags
- **Warning**: > 1 second average response time
- **Critical**: > 3 seconds for any single request

## 🛠️ Advanced Usage

### Running Specific Test Categories
Modify `api_test_suite.py` to run only specific tests:

```python
# Run only client tests
tester = CymaticsAPITester()
tester.test_clients()
summary = tester.generate_summary_report()
```

### Custom Test Data
Modify the test data in each test method to match your specific requirements.

### Integration with CI/CD
The test suite returns appropriate exit codes:
- `0`: Success (80%+ pass rate)
- `1`: Failure (< 80% pass rate)

## 📞 Support

For issues with the test suite:
1. Check the detailed log files in `logs/`
2. Verify backend server is running and accessible
3. Ensure all environment variables are set correctly
4. Check database connectivity and API keys

The test suite is designed to be comprehensive yet easy to use, providing detailed insights into your API's health and performance.
