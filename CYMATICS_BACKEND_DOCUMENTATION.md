# Cymatics Django Backend - Complete Documentation

## Overview
Cymatics is a comprehensive project management system for creative professionals, specifically designed for photography/videography businesses. The Django backend manages clients, projects, finances, assets, entertainment tracking, and calendar functionality.

## Architecture Overview

### Technology Stack
- **Framework**: Django 5.1
- **Database**: PostgreSQL (port 5433)
- **Authentication**: Custom OTP-based email authentication
- **File Storage**: Local media storage
- **External APIs**: Google Maps API, Google Geocoding API
- **Email**: SMTP (Gmail)

### Database Models

#### 1. Client Model
```python
class Client(models.Model):
    name = Char<PERSON><PERSON>(max_length=100)
    company = CharField(max_length=100)
    number = Char<PERSON>ield(max_length=20)
    email = EmailField(max_length=100, blank=True)
    img = ImageField(upload_to='media', blank=True)
```

#### 2. Outclient Model (Outsourcing Clients)
```python
class Outclient(models.Model):
    name = Char<PERSON>ield(max_length=100)
    company = CharField(max_length=100)
    number = Char<PERSON>ield(max_length=20)
    email = EmailField(max_length=100, blank=True)
    img = ImageField(upload_to='media', blank=True)
```

#### 3. Project Model (Core Business Entity)
```python
class Project(models.Model):
    code = TextField(unique=True, blank=True, null=True)  # Auto-generated: CYM-{id}
    name = CharField(max_length=100, blank=True)
    company = CharField(max_length=100, blank=True)
    type = CharField(max_length=50, blank=True)
    status = CharField(max_length=50, blank=True)
    shoot_start_date = DateTimeField(blank=True, null=True)
    shoot_end_date = DateTimeField(blank=True, null=True)
    amount = IntegerField(default=0)
    location = CharField(max_length=200, blank=True)
    latitude = FloatField(default=0.0)
    longitude = FloatField(default=0.0)
    outsourcing = BooleanField(default=False)
    reference = TextField(blank=True)
    image = ImageField(upload_to='media', blank=True)
    pending_amt = IntegerField(default=0)
    received_amt = IntegerField(default=0)
    address = CharField(max_length=500, blank=True)
    map = CharField(max_length=200, blank=True)
    profit = IntegerField(default=0)
    rating = IntegerField(default=0)
    outsourcing_amt = IntegerField(default=0)
    out_for = CharField(max_length=100, blank=True)
    out_client = CharField(max_length=100, blank=True)
    outsourcing_paid = BooleanField(default=False)
    client = ForeignKey(Client, on_delete=CASCADE, default=1)
```

#### 4. Income Model
```python
class Income(models.Model):
    date = DateField()
    description = TextField()
    amount = IntegerField()
    note = TextField(blank=True)
    project_income = BooleanField(default=False)
    project_code = ForeignKey(Project, on_delete=CASCADE, related_name='received', null=True, blank=True)
```

#### 5. Expense Model
```python
class Expense(models.Model):
    date = DateField()
    category = CharField(max_length=50)
    description = TextField()
    amount = IntegerField()
    notes = TextField(blank=True)
    project_expense = BooleanField(default=False)
    project_code = ForeignKey(Project, on_delete=CASCADE, related_name='expense_expenses', null=True, blank=True)
```

#### 6. Assets Model
```python
class Assets(models.Model):
    date = DateField()
    type = CharField(max_length=100)
    name = CharField(max_length=200)
    quantity = DecimalField(decimal_places=50, max_digits=100)
    buy_price = DecimalField(decimal_places=50, max_digits=100)
    value = IntegerField(default=0)
    note = TextField(blank=True)
    image = ImageField(upload_to='media', blank=True, null=True)
```

#### 7. Entertainment Model
```python
class Entertainment(models.Model):
    date = DateTimeField(default=timezone.now)
    type = CharField(max_length=100)
    language = CharField(max_length=100)
    rating = IntegerField()
    name = CharField(max_length=100)
    source = CharField(max_length=100, blank=True)
    image = ImageField(upload_to='media', blank=True)
```

#### 8. CalendarEvent Model
```python
class CalendarEvent(models.Model):
    title = CharField(max_length=255)
    start_time = DateTimeField()
    end_time = DateTimeField()
```

#### 9. EmailOTP Model
```python
class EmailOTP(models.Model):
    user = ForeignKey(User, on_delete=CASCADE)
    otp = CharField(max_length=6)
    created_at = DateTimeField(default=timezone.now)
```

## Core Functionality

### 1. Authentication System
- **OTP-based Email Authentication**: Users register/login using email OTP
- **Email Integration**: SMTP configuration for sending OTPs
- **Session Management**: Django's built-in session handling

### 2. Client Management
- **CRUD Operations**: Create, read, update, delete clients
- **Search Functionality**: Search by name, company, number, email
- **Image Upload**: Profile pictures for clients
- **Project Association**: Link clients to projects

### 3. Project Management
- **Comprehensive Project Tracking**: Type, status, dates, amounts, locations
- **Financial Calculations**: Auto-calculated profit, pending amounts
- **Outsourcing Management**: Track outsourced work and payments
- **Location Services**: Google Maps integration for project locations
- **Status Management**: Project status tracking and updates

### 4. Financial Management
- **Income Tracking**: Project and non-project income
- **Expense Management**: Categorized expenses with project association
- **Profit Calculation**: Automatic profit calculation (amount - outsourcing_amt - expenses)
- **Payment Tracking**: Received and pending amounts

### 5. Asset Management
- **Equipment Tracking**: Professional equipment inventory
- **Value Management**: Purchase price and current value tracking
- **Categorization**: Asset types and quantities

### 6. Calendar Integration
- **Event Management**: Schedule and track events
- **Project Integration**: Link calendar events to projects
- **Date Management**: Start and end date tracking

### 7. Map Integration
- **Google Maps API**: Project location visualization
- **Geocoding**: Address to coordinates conversion
- **Location Search**: Find nearby places

## API Endpoints Structure

### Authentication Endpoints
- `GET /` - Send OTP page
- `POST /` - Send OTP to email
- `GET /verification` - Verify OTP page
- `POST /verification` - Verify OTP and login
- `GET /logout` - Logout user

### Client Management
- `GET /clients` - List all clients with search
- `POST /clients` - Create new client
- `GET /get_client_data/<int:clt_id>/` - Get client data
- `POST /edit_client/<int:clt_id>/` - Edit client
- `GET /clientd/<str:name>/` - Client details page

### Project Management
- `GET /projects/` - List all projects with filters
- `POST /projects/` - Create new project
- `GET /get_model_data/<str:code>/` - Get project data
- `POST /edit_model/<str:code>/` - Edit project
- `DELETE /delete_project/<int:project_id>/` - Delete project
- `GET /project/<str:code>/` - Project details page

### Financial Management
- `GET /income/` - List income entries
- `POST /income/` - Create income entry
- `GET /expense/` - List expense entries
- `POST /expense/` - Create expense entry
- `GET /budget/` - Budget overview
- `GET /total-expense/` - Total expense categorized view

### Map and Location
- `GET /map/` - Map view with projects
- `POST /add_project/` - Add project with location
- `GET /resolve-url/` - Resolve shortened URLs
- `GET /fetch-places/` - Fetch nearby places

### Calendar
- `GET /calendar/` - Calendar view
- `POST /calendar/` - Add calendar event
- `GET /calendar/events/` - Get calendar events

## Configuration

### Database Configuration
```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'postgres',
        'USER': 'postgres',
        'PASSWORD': 'darkside',
        'HOST': 'localhost',
        'PORT': '5433'
    }
}
```

### Email Configuration
```python
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'vbjf yidq fwnl gsfm'
DEFAULT_FROM_EMAIL = '<EMAIL>'
```

### Google Maps API
```python
GOOGLE_MAPS_API_KEY = 'AIzaSyAqIalCM0rqsXeHiyRP4ImBbVgqwMSv8D8'
```

## Business Logic

### Project Financial Calculations
```python
def update_project_finances(self):
    expenses_total = Expense.objects.filter(project_code=self).aggregate(total=Sum('amount'))['total'] or 0
    income_total = Income.objects.filter(project_code=self).aggregate(total=Sum('amount'))['total'] or 0
    self.profit = self.amount - (self.outsourcing_amt + expenses_total)
    self.received_amt = income_total
    self.pending_amt = self.amount - self.received_amt
```

### Auto-generated Project Codes
```python
def save(self, *args, **kwargs):
    if not self.code:
        super().save(*args, **kwargs)
        self.code = f'CYM-{self.id}'
        super().save(update_fields=['code'])
```

## Key Features

1. **Multi-tenant Client Management**: Regular clients and outsourcing clients
2. **Comprehensive Project Tracking**: From initial booking to completion
3. **Financial Analytics**: Profit/loss tracking, payment management
4. **Location Intelligence**: Google Maps integration for project locations
5. **Asset Management**: Equipment and resource tracking
6. **Calendar Integration**: Schedule management
7. **Search and Filtering**: Advanced search across all entities
8. **File Management**: Image uploads for clients, projects, assets
9. **OTP Authentication**: Secure email-based login system
10. **Responsive Design**: Mobile-friendly interface

## Dependencies
- Django 5.1
- PostgreSQL (psycopg2)
- Pillow (image processing)
- Google API libraries
- Requests (HTTP library)
- Django Extensions
- Social Auth Django
