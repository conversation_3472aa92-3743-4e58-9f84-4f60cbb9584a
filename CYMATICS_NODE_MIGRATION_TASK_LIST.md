# 🚀 Node.js Backend Migration Task Sheet - MISSING FEATURES ANALYSIS

## 📋 **MISSING FEATURES & IMPLEMENTATION TASKS**

> **FRONTEND ANALYSIS COMPLETED**: Based on comprehensive analysis of the React Native frontend codebase, the following high-priority features are required for full frontend-backend integration.

### **PHASE 1: CORE MISSING ENDPOINTS (HIGH PRIORITY)**

#### **Task 1.1: Client Detail Management**
- [ ] **Endpoint:** `GET /api/clients/:name/details`
- [ ] **Implementation:** Client detail with project aggregation
- [ ] **Features:**
  - Projects filtered by client name
  - Total amount calculation
  - Company counts and statistics
  - Client information display
- [ ] **Files to create:**
  - Add method in `client.controller.ts`
  - Add route in `client.routes.ts`
  - Add service method in `client.service.ts`

#### **Task 1.2: Project Detail Management**
- [ ] **Endpoint:** `GET /api/projects/:code/details`
- [ ] **Implementation:** Project detail with financial calculations
- [ ] **Features:**
  - Project expenses aggregation
  - Project income aggregation
  - Real-time profit calculation
  - Pending amount calculation
  - Received amount calculation
- [ ] **Files to modify:**
  - Add method in `project.controller.ts`
  - Add route in `project.routes.ts`
  - Add service method in `project.service.ts`

#### **Task 1.3: Dropdown Utility Endpoints**
- [ ] **Endpoint:** `GET /api/projects/dropdown/types`
- [ ] **Endpoint:** `GET /api/projects/dropdown/companies`
- [ ] **Endpoint:** `GET /api/outclients/dropdown/names`
- [ ] **Endpoint:** `GET /api/financial/expenses/dropdown/categories`
- [ ] **Implementation:** Utility endpoints for frontend dropdowns
- [ ] **Files to modify:**
  - Add methods in respective controllers
  - Add routes in respective route files
  - Add service methods

---

### **PHASE 2: BUDGET MANAGEMENT SYSTEM (HIGH PRIORITY)**

#### **Task 2.1: Budget Calculation Engine**
- [ ] **Create:** `src/services/budget.service.ts`
- [ ] **Implementation:** Complex budget calculation system
- [ ] **Features:**
  - Global budget state management
  - Percentage-based allocations (36% Cymatics, 10% Gadgets, etc.)
  - Monthly vs yearly calculations
  - Category-wise expense tracking
  - Current balance calculations
- [ ] **Business Logic:**
  ```typescript
  // Budget percentages
  const BUDGET_PERCENTAGES = {
    cymatics: 36,
    gadgets: 10,
    entertainment: 5,
    investments: 15,
    others: 5,
    yaso: 12.5,
    gopi: 12.5,
    adithyan: 4
  };
  ```

#### **Task 2.2: Budget API Endpoints**
- [ ] **Endpoint:** `GET /api/financial/budget/overview`
- [ ] **Endpoint:** `GET /api/financial/budget/category/:category`
- [ ] **Endpoint:** `GET /api/financial/budget/current-balance`
- [ ] **Endpoint:** `GET /api/financial/budget/monthly-trends`
- [ ] **Files to create:**
  - `src/controllers/budget.controller.ts`
  - `src/routes/budget.routes.ts`
  - Add to main app routes

#### **Task 2.3: Budget Database Schema Updates**
- [ ] **Create:** Budget tracking tables if needed
- [ ] **Update:** Prisma schema for budget categories
- [ ] **Migration:** Database migration for budget system

---

### **PHASE 3: SPECIALIZED DASHBOARD VIEWS (MEDIUM PRIORITY)**

#### **Task 3.1: Project Status Management**
- [ ] **Endpoint:** `GET /api/projects/grouped-by-status`
- [ ] **Implementation:** Projects grouped by status with search
- [ ] **Features:**
  - Group projects by status
  - Search within each status group
  - Status-wise statistics
- [ ] **Files to modify:**
  - Add method in `project.controller.ts`
  - Add route in `project.routes.ts`

#### **Task 3.2: Pending Payments Management**
- [ ] **Endpoint:** `GET /api/projects/pending-payments`
- [ ] **Implementation:** Projects with pending amounts
- [ ] **Features:**
  - Filter projects with pending_amt > 0
  - Order by pending amount (descending)
  - Search functionality
  - Pagination support

#### **Task 3.3: Client Book Management**
- [ ] **Endpoint:** `GET /api/clients/book`
- [ ] **Endpoint:** `GET /api/clients/book/:company`
- [ ] **Implementation:** Client book with project frequency analysis
- [ ] **Features:**
  - Company-wise project aggregation
  - Project count filtering
  - Advanced search and filtering
  - Company detail view

#### **Task 3.4: Advanced Project View**
- [ ] **Endpoint:** `GET /api/projects/advanced`
- [ ] **Implementation:** All projects with advanced filtering
- [ ] **Features:**
  - Multi-field search
  - Type, status, outsourcing filters
  - Pending amount range filtering
  - Expense calculations per project
  - Complex query building

---

### **PHASE 4: EXPENSE MANAGEMENT ENHANCEMENTS (MEDIUM PRIORITY)**

#### **Task 4.1: Expense Categorization System**
- [ ] **Endpoint:** `GET /api/financial/expenses/categorized`
- [ ] **Implementation:** Expenses grouped by category with pagination
- [ ] **Features:**
  - Group expenses by category
  - Pagination per category
  - Category-wise filtering
  - AJAX support for dynamic loading

#### **Task 4.2: Notes Management System**
- [ ] **Endpoint:** `PUT /api/financial/expenses/:id/notes`
- [ ] **Endpoint:** `PUT /api/financial/income/:id/notes`
- [ ] **Implementation:** Dedicated notes saving endpoints
- [ ] **Features:**
  - Update expense notes
  - Update income notes
  - Validation and error handling

---

### **PHASE 5: CALENDAR-PROJECT INTEGRATION (MEDIUM PRIORITY)**

#### **Task 5.1: Calendar-Project Integration**
- [ ] **Endpoint:** `POST /api/calendar/events/from-project`
- [ ] **Endpoint:** `GET /api/calendar/project-events`
- [ ] **Endpoint:** `PUT /api/calendar/project-events/:id`
- [ ] **Implementation:** Calendar integration with projects
- [ ] **Features:**
  - Create projects from calendar events
  - Display project events in calendar
  - Edit projects from calendar interface
  - Project-calendar synchronization

#### **Task 5.2: Calendar Service Enhancement**
- [ ] **Modify:** `src/services/calendar.service.ts`
- [ ] **Add:** Project-calendar integration methods
- [ ] **Features:**
  - Project event creation
  - Project event editing
  - Calendar-project data mapping

---

### **PHASE 6: MAPS-PROJECT INTEGRATION (MEDIUM PRIORITY)**

#### **Task 6.1: Maps-Project Integration**
- [ ] **Endpoint:** `GET /api/maps/projects`
- [ ] **Endpoint:** `POST /api/maps/projects`
- [ ] **Endpoint:** `PUT /api/maps/projects/:id/location`
- [ ] **Implementation:** Maps integration with projects
- [ ] **Features:**
  - Project map view with search
  - Add projects with location
  - Update project locations
  - Project markers on map

#### **Task 6.2: Maps Utility Endpoints**
- [ ] **Endpoint:** `POST /api/maps/resolve-url`
- [ ] **Endpoint:** `GET /api/maps/nearby-places`
- [ ] **Implementation:** Additional maps utilities
- [ ] **Features:**
  - URL resolution for shortened links
  - Nearby places search
  - Enhanced location services

---

### **PHASE 7: DYNAMIC FIELD MANAGEMENT (LOW PRIORITY)**

#### **Task 7.1: Dynamic Field Saving System**
- [ ] **Endpoint:** `PUT /api/projects/:id/field`
- [ ] **Implementation:** Dynamic field updating system
- [ ] **Features:**
  - Update any project field dynamically
  - Type conversion (boolean, numeric, date)
  - Field validation
  - Error handling

#### **Task 7.2: Rating System**
- [ ] **Endpoint:** `PUT /api/entertainment/:id/rating`
- [ ] **Endpoint:** `GET /api/entertainment/:id/rating`
- [ ] **Implementation:** Rating update system
- [ ] **Features:**
  - Update entertainment ratings
  - Get current ratings
  - Rating validation (1-10)

---

### **PHASE 8: ADVANCED ANALYTICS & CHARTS (LOW PRIORITY)**

#### **Task 8.1: Chart Data Endpoints**
- [ ] **Endpoint:** `GET /api/dashboard/charts/income-trends`
- [ ] **Endpoint:** `GET /api/dashboard/charts/pie-expenses`
- [ ] **Endpoint:** `GET /api/dashboard/charts/bar-categories`
- [ ] **Implementation:** Chart data generation
- [ ] **Features:**
  - Income trend charts
  - Expense pie charts
  - Category bar charts
  - Monthly/yearly data

#### **Task 8.2: Advanced Financial Analytics**
- [ ] **Service:** Enhanced financial analytics
- [ ] **Features:**
  - Complex financial calculations
  - Trend analysis
  - Profit margin calculations
  - Growth rate calculations

---

### **PHASE 9: DETAIL PAGE DATA ENDPOINTS (LOW PRIORITY)**

#### **Task 9.1: Entity Detail Endpoints**
- [ ] **Endpoint:** `GET /api/financial/expenses/:id/details`
- [ ] **Endpoint:** `GET /api/financial/income/:id/details`
- [ ] **Endpoint:** `GET /api/entertainment/:id/details`
- [ ] **Endpoint:** `GET /api/assets/:id/details`
- [ ] **Implementation:** Detail page data for all entities
- [ ] **Features:**
  - Formatted data for detail views
  - Related data inclusion
  - Calculated fields

---

## 📊 **IMPLEMENTATION PRIORITY MATRIX**

### **🔴 HIGH PRIORITY (Weeks 1-2)**
1. **Budget Management System** - Core business logic
2. **Dropdown Utility Endpoints** - Frontend dependencies
3. **Client/Project Detail Management** - Core functionality
4. **Specialized Dashboard Views** - Business requirements

### **🟡 MEDIUM PRIORITY (Weeks 3-4)**
1. **Expense Categorization System** - Enhanced functionality
2. **Calendar-Project Integration** - Feature enhancement
3. **Maps-Project Integration** - Feature enhancement
4. **Notes Management System** - User experience

### **🟢 LOW PRIORITY (Weeks 5-6)**
1. **Dynamic Field Management** - Advanced features
2. **Advanced Analytics & Charts** - Data visualization
3. **Detail Page Data Endpoints** - UI support
4. **Rating System** - Minor features

---

## 🛠 **IMPLEMENTATION GUIDELINES**

### **Code Structure Standards**
```typescript
// Controller Pattern
export class ControllerName {
  async methodName(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const result = await serviceName.methodName(params);
      sendSuccessResponse(res, result, 'Success message', 200);
    } catch (error) {
      next(error);
    }
  }
}

// Service Pattern
export class ServiceName {
  async methodName(params: ParamsType): Promise<ReturnType> {
    // Business logic implementation
    return result;
  }
}

// Route Pattern
router.get('/endpoint',
  validateQuery(schema),
  authenticateToken,
  controller.method
);
```

### **Database Query Patterns**
```typescript
// Aggregation Pattern
const result = await prisma.model.groupBy({
  by: ['field'],
  _sum: { amount: true },
  _count: { id: true },
  where: filters,
  orderBy: { field: 'desc' }
});

// Complex Filtering Pattern
const whereClause = {
  AND: [
    searchQuery ? {
      OR: [
        { field1: { contains: searchQuery, mode: 'insensitive' } },
        { field2: { contains: searchQuery, mode: 'insensitive' } }
      ]
    } : {},
    filters
  ]
};
```

### **Error Handling Standards**
```typescript
// Custom Error Classes
export class BusinessLogicError extends Error {
  constructor(message: string, public statusCode: number = 400) {
    super(message);
    this.name = 'BusinessLogicError';
  }
}

// Error Handling in Controllers
try {
  // Business logic
} catch (error) {
  if (error instanceof BusinessLogicError) {
    return res.status(error.statusCode).json({
      success: false,
      message: error.message
    });
  }
  next(error);
}
```

---

## ✅ **COMPLETION CHECKLIST**

### **Phase 1 Completion Criteria**
- [ ] All dropdown endpoints functional
- [ ] Client detail endpoint with aggregations
- [ ] Project detail endpoint with calculations
- [ ] Basic budget overview endpoint

### **Phase 2 Completion Criteria**
- [ ] Complete budget management system
- [ ] Budget percentage calculations
- [ ] Monthly/yearly budget tracking
- [ ] Budget category details

### **Phase 3 Completion Criteria**
- [ ] All specialized dashboard views
- [ ] Advanced project filtering
- [ ] Client book management
- [ ] Pending payments management

### **Final Completion Criteria**
- [ ] All 80+ Django endpoints replicated
- [ ] 100% feature parity achieved
- [ ] All business logic implemented
- [ ] Comprehensive testing completed
- [ ] Documentation updated

---

## 📈 **ESTIMATED TIMELINE**

- **Phase 1:** 2 weeks (High Priority)
- **Phase 2:** 2 weeks (Budget System)
- **Phase 3:** 2 weeks (Dashboard Views)
- **Phase 4-6:** 3 weeks (Medium Priority)
- **Phase 7-9:** 2 weeks (Low Priority)
- **Testing & Polish:** 1 week

**Total Estimated Time:** 12 weeks for complete feature parity

---

## 🎯 **SUCCESS METRICS**

1. **Feature Coverage:** 100% of Django endpoints replicated
2. **Business Logic:** All financial calculations match Django
3. **Performance:** API response times < 200ms
4. **Testing:** 90%+ code coverage
5. **Documentation:** Complete API documentation
6. **Compatibility:** Full frontend integration support

---

## 📋 **DETAILED MISSING ENDPOINTS COMPARISON**

### **1. CLIENT DETAIL VIEWS**

#### **Django Implementation:**
```python
# URL: /clientd/<str:name>/
def clientd(request, name):
    projects = Project.objects.filter(client__name=name)
    client = get_object_or_404(Client, name=name)
    total = projects.aggregate(total=Sum('amount'))['total']
    company_counts = projects.values('company', 'client__name', 'client__email', 'client__number').annotate(count=Count('company'))
    context = {'projects': projects, 'total': total, 'objs': company_counts, 'client': client}
    return render(request, 'clientsd.html', context)
```

#### **Node.js Missing:** ❌ Client detail endpoint with project aggregation

### **2. PROJECT DETAIL VIEWS**

#### **Django Implementation:**
```python
# URL: /project/<str:code>/
def projectd(request, code):
    project = get_object_or_404(Project, code=code)
    expenses = Expense.objects.filter(project_code=project)
    tote = expenses.aggregate(total=Sum('amount'))['total'] or 0
    incomes = Income.objects.filter(project_code=project)
    toti = incomes.aggregate(total=Sum('amount'))['total'] or 0

    # Update financial calculations
    project.profit = project.amount - (project.outsourcing_amt + tote)
    project.received_amt = toti
    project.pending_amt = project.amount - project.received_amt
    project.save()

    return render(request, 'projectd.html', {'objs': project, 'expenses': expenses, 'totalex': tote})
```

#### **Node.js Missing:** ❌ Project detail with real-time financial calculations

### **3. BUDGET MANAGEMENT SYSTEM**

#### **Django Implementation:**
```python
# Complex budget calculation with global variables
total_current_balance = 0
cym_curbal = 0
gad_curbal = 0
# ... other budget variables

def currentbalance():
    global total_current_balance, cym_curbal, gad_curbal, ent_curbal, inv_curbal, yaso_curbal, gopi_curbal, adi_curbal, oth_curbal

    year = datetime.now().year
    tal_income = Income.objects.filter(date__year=year).aggregate(total=Sum('amount'))['total']

    if tal_income:
        fo_san = Expense.objects.filter(date__year=year, category="Food & Snacks").aggregate(total=Sum('amount'))['total'] or 0
        fu_tra = Expense.objects.filter(date__year=year, category="Fuel & Travel").aggregate(total=Sum('amount'))['total'] or 0
        out = Expense.objects.filter(date__year=year, category="Outsourcing").aggregate(total=Sum('amount'))['total'] or 0
        total_income = tal_income - fo_san - fu_tra - out
    else:
        total_income = 0

    # Budget percentages (36% Cymatics, 10% Gadgets, etc.)
    cymbud = (36/100) * total_income
    gadbud = (10/100) * total_income
    entbud = (5/100) * total_income
    invbud = (15/100) * total_income
    othbud = (5/100) * total_income
    yasobud = (12.5/100) * total_income
    gopibud = (12.5/100) * total_income
    adibud = (4/100) * total_income

    # Calculate current balances
    cym_curbal = cymbud - cyexp
    gad_curbal = gadbud - gadexp
    # ... other calculations
```

#### **Node.js Missing:** ❌ Complete budget management system with percentage allocations

### **4. CLIENT BOOK MANAGEMENT**

#### **Django Implementation:**
```python
# URL: /clientsbook
def clientsbook(request):
    company_counts = Project.objects.values('company', 'client__name').annotate(count=Count('company'))

    # Complex filtering logic
    query = request.GET.get('q', '')
    project_count = request.GET.get('project_count', '')
    company = request.GET.get('company', '')

    filters = Q()
    if query:
        filters &= (Q(company__icontains=query) | Q(client__name__icontains=query) | Q(count__icontains=query))

    if company:
        com_list = company.split(',')
        filters &= Q(company__in=com_list)

    if project_count:
        project_count_values = project_count.split(',')
        numeric_values = [int(count) for count in project_count_values if count.isdigit()]
        greater_than_5_flag = 'greater_than_5' in project_count_values

        if numeric_values:
            filters &= Q(count__in=numeric_values)
        if greater_than_5_flag:
            filters &= Q(count__gt=5)

    objscc = company_counts.filter(filters)
    return render(request, 'clientsbook.html', {'objs': objscc, 'query': query, 'project_count': project_count, 'company': company})
```

#### **Node.js Missing:** ❌ Client book with project frequency analysis

### **5. DROPDOWN UTILITY ENDPOINTS**

#### **Django Implementation:**
```python
# Multiple utility endpoints
def get_unique_types(request):
    unique_types = Project.objects.values_list('type', flat=True).distinct()
    return JsonResponse(list(unique_types), safe=False)

def get_unique_company(request):
    unique_company = Project.objects.values_list('company', flat=True).distinct()
    return JsonResponse(list(unique_company), safe=False)

def get_unique_client(request):
    unique_client = Outclient.objects.values_list('name', flat=True).distinct()
    return JsonResponse(list(unique_client), safe=False)

def get_unique_category(request):
    unique_cat = Expense.objects.values_list('category', flat=True).distinct()
    return JsonResponse(list(unique_cat), safe=False)
```

#### **Node.js Missing:** ❌ Utility endpoints for frontend dropdowns

---

# Cymatics Django to Node.js Migration - Complete Task List

## Phase 1: Project Setup and Infrastructure

### 1.1 Initialize Node.js Project
- [x] Create new Node.js project directory
- [x] Initialize package.json with project details
- [x] Set up TypeScript configuration (recommended for type safety)
- [x] Configure ESLint and Prettier for code quality
- [x] Set up Git repository and .gitignore

### 1.2 Choose and Setup Framework
- [x] Choose Express.js as the web framework
- [x] Install and configure Express.js
- [x] Set up middleware for CORS, body parsing, file uploads
- [x] Configure static file serving for media files
- [x] Set up error handling middleware

### 1.3 Database Setup
- [x] Install PostgreSQL driver (pg)
- [x] Choose ORM/Query Builder (Prisma, Sequelize, or TypeORM)
- [x] Configure database connection
- [x] Set up connection pooling
- [x] Configure database for port 5433

### 1.4 Environment Configuration
- [x] Set up environment variables (.env file)
- [x] Configure different environments (development, production)
- [x] Set up configuration management
- [x] Configure logging system (Winston or similar)

## Phase 2: Database Schema Migration

### 2.1 Create Database Models
- [x] **Client Model**
  - [x] name (string, required)
  - [x] company (string, required)
  - [x] number (string, required)
  - [x] email (string, optional)
  - [x] img (string/file path, optional)
  - [x] timestamps (created_at, updated_at)

- [x] **Outclient Model**
  - [x] name (string, required)
  - [x] company (string, required)
  - [x] number (string, required)
  - [x] email (string, optional)
  - [x] img (string/file path, optional)
  - [x] timestamps (created_at, updated_at)

- [x] **Project Model**
  - [x] code (string, unique, auto-generated)
  - [x] name (string, optional)
  - [x] company (string, optional)
  - [x] type (string, optional)
  - [x] status (string, optional)
  - [x] shoot_start_date (datetime, optional)
  - [x] shoot_end_date (datetime, optional)
  - [x] amount (integer, default 0)
  - [x] location (string, optional)
  - [x] latitude (float, default 0.0)
  - [x] longitude (float, default 0.0)
  - [x] outsourcing (boolean, default false)
  - [x] reference (text, optional)
  - [x] image (string/file path, optional)
  - [x] pending_amt (integer, default 0)
  - [x] received_amt (integer, default 0)
  - [x] address (string, optional)
  - [x] map (string, optional)
  - [x] profit (integer, default 0)
  - [x] rating (integer, default 0)
  - [x] outsourcing_amt (integer, default 0)
  - [x] out_for (string, optional)
  - [x] out_client (string, optional)
  - [x] outsourcing_paid (boolean, default false)
  - [x] client_id (foreign key to Client)
  - [x] timestamps (created_at, updated_at)

- [x] **Income Model**
  - [x] date (date, required)
  - [x] description (text, required)
  - [x] amount (integer, required)
  - [x] note (text, optional)
  - [x] project_income (boolean, default false)
  - [x] project_id (foreign key to Project, optional)
  - [x] timestamps (created_at, updated_at)

- [x] **Expense Model**
  - [x] date (date, required)
  - [x] category (string, required)
  - [x] description (text, required)
  - [x] amount (integer, required)
  - [x] notes (text, optional)
  - [x] project_expense (boolean, default false)
  - [x] project_id (foreign key to Project, optional)
  - [x] timestamps (created_at, updated_at)

- [x] **Assets Model**
  - [x] date (date, required)
  - [x] type (string, required)
  - [x] name (string, required)
  - [x] quantity (decimal, required)
  - [x] buy_price (decimal, required)
  - [x] value (integer, default 0)
  - [x] note (text, optional)
  - [x] image (string/file path, optional)
  - [x] timestamps (created_at, updated_at)

- [x] **Entertainment Model**
  - [x] date (datetime, default now)
  - [x] type (string, required)
  - [x] language (string, required)
  - [x] rating (integer, required)
  - [x] name (string, required)
  - [x] source (string, optional)
  - [x] image (string/file path, optional)
  - [x] timestamps (created_at, updated_at)

- [x] **CalendarEvent Model**
  - [x] title (string, required)
  - [x] start_time (datetime, required)
  - [x] end_time (datetime, required)
  - [x] timestamps (created_at, updated_at)

- [x] **User Model**
  - [x] username (string, unique, required)
  - [x] email (string, unique, required)
  - [x] password (string, optional - for OTP system)
  - [x] is_active (boolean, default true)
  - [x] timestamps (created_at, updated_at)

- [x] **EmailOTP Model**
  - [x] user_id (foreign key to User)
  - [x] otp (string, 6 digits)
  - [x] created_at (datetime)
  - [x] expires_at (datetime)
  - [x] is_used (boolean, default false)

### 2.2 Database Migrations
- [x] Create migration files for all models
- [x] Set up foreign key relationships
- [x] Create indexes for performance optimization
- [x] Set up database constraints and validations

## Phase 3: Authentication System

### 3.1 OTP Authentication
- [x] Install email service library (Nodemailer)
- [x] Configure SMTP settings for Gmail
- [x] Create OTP generation utility (6-digit random number)
- [x] Implement send OTP endpoint
- [x] Implement verify OTP endpoint
- [x] Set up session management (express-session or JWT)
- [x] Create authentication middleware
- [x] Implement logout functionality

### 3.2 User Management
- [x] Create user registration logic
- [x] Implement user profile management
- [x] Set up password reset functionality (if needed)
- [x] Create user session handling

## Phase 4: Core API Development

### 4.1 Client Management APIs
- [x] **GET /api/clients** - List all clients with search and pagination
- [x] **POST /api/clients** - Create new client
- [x] **GET /api/clients/:id** - Get specific client
- [x] **PUT /api/clients/:id** - Update client
- [x] **DELETE /api/clients/:id** - Delete client
- [x] **GET /api/clients/stats** - Get client statistics
- [x] **GET /api/clients/dropdown** - Get clients for dropdown

### 4.2 Outclient Management APIs
- [x] **Outclient Service** - Complete business logic implemented
- [x] **GET /api/outclients** - List all outsourcing clients
- [x] **POST /api/outclients** - Create new outclient
- [x] **GET /api/outclients/:id** - Get specific outclient
- [x] **PUT /api/outclients/:id** - Update outclient
- [x] **DELETE /api/outclients/:id** - Delete outclient
- [x] **GET /api/outclients/stats** - Get outclient statistics
- [x] **GET /api/outclients/dropdown** - Get outclients for dropdown

### 4.3 Project Management APIs
- [x] **GET /api/projects** - List all projects with filters and search
- [x] **POST /api/projects** - Create new project
- [x] **GET /api/projects/code/:code** - Get project by code
- [x] **PUT /api/projects/:id** - Update project
- [x] **DELETE /api/projects/:id** - Delete project
- [x] **GET /api/projects/:code/data** - Get detailed project view
- [x] **PUT /api/projects/:id/status** - Update project status
- [x] **GET /api/projects/codes** - Get project codes for dropdown
- [x] **GET /api/projects/stats** - Get project statistics

### 4.4 Financial Management APIs
- [x] **GET /api/financial/income** - List all income entries with filters
- [x] **POST /api/financial/income** - Create new income entry
- [x] **GET /api/financial/income/:id** - Get specific income entry
- [x] **PUT /api/financial/income/:id** - Update income entry
- [x] **DELETE /api/financial/income/:id** - Delete income entry
- [x] **GET /api/financial/expenses** - List all expense entries with filters
- [x] **POST /api/financial/expenses** - Create new expense entry
- [x] **GET /api/financial/expenses/:id** - Get specific expense entry
- [x] **PUT /api/financial/expenses/:id** - Update expense entry
- [x] **DELETE /api/financial/expenses/:id** - Delete expense entry
- [x] **GET /api/financial/expenses/categories** - Get unique expense categories
- [x] **GET /api/financial/expenses/totals** - Get categorized expense totals
- [x] **GET /api/financial/summary** - Get financial summary
- [x] **GET /api/financial/budget** - Get budget overview

### 4.5 Asset Management APIs
- [x] **GET /api/assets** - List all assets with search
- [x] **POST /api/assets** - Create new asset
- [x] **GET /api/assets/:id** - Get specific asset
- [x] **PUT /api/assets/:id** - Update asset
- [x] **DELETE /api/assets/:id** - Delete asset
- [x] **GET /api/assets/stats** - Get asset statistics
- [x] **GET /api/assets/types** - Get asset types

### 4.6 Entertainment APIs
- [x] **Entertainment Service** - Complete business logic implemented
- [x] **GET /api/entertainment** - List all entertainment entries
- [x] **POST /api/entertainment** - Create new entertainment entry
- [x] **GET /api/entertainment/:id** - Get specific entertainment entry
- [x] **PUT /api/entertainment/:id** - Update entertainment entry
- [x] **DELETE /api/entertainment/:id** - Delete entertainment entry
- [x] **GET /api/entertainment/stats** - Get entertainment statistics
- [x] **GET /api/entertainment/types** - Get entertainment types
- [x] **GET /api/entertainment/languages** - Get entertainment languages

### 4.7 Calendar APIs
- [x] **Calendar Service** - Complete business logic implemented
- [x] **GET /api/calendar/events** - Get calendar events
- [x] **POST /api/calendar/events** - Create new calendar event
- [x] **GET /api/calendar/events/:id** - Get specific event
- [x] **PUT /api/calendar/events/:id** - Update calendar event
- [x] **DELETE /api/calendar/events/:id** - Delete calendar event
- [x] **GET /api/calendar/events/upcoming** - Get upcoming events
- [x] **GET /api/calendar/events/today** - Get today's events
- [x] **GET /api/calendar/events/week** - Get current week events
- [x] **GET /api/calendar/events/month** - Get current month events
- [x] **GET /api/calendar/events/range** - Get events for date range
- [x] **GET /api/calendar/events/stats** - Get calendar statistics

## Phase 5: External Service Integration

### 5.1 Google Maps Integration
- [x] Install Google Maps API client
- [x] Configure Google Maps API key
- [x] Implement geocoding service (address to coordinates)
- [x] Create location resolution endpoints
- [x] Implement nearby places search
- [x] Set up map data endpoints for frontend

### 5.2 Email Service
- [x] Configure Nodemailer with Gmail SMTP
- [x] Create email templates for OTP
- [x] Implement email sending utility
- [x] Set up email error handling and retries
- [x] Configure email rate limiting

## Phase 6: File Upload and Media Management

### 6.1 File Upload System
- [x] Install multer for file uploads
- [x] Configure file storage (local or cloud)
- [x] Set up file validation (size, type)
- [x] Create file upload endpoints
- [x] Implement file deletion functionality
- [x] Set up static file serving

### 6.2 Image Processing
- [x] Install image processing library (Sharp)
- [ ] Implement image resizing and optimization
- [ ] Create thumbnail generation
- [ ] Set up image format conversion

## Phase 7: Business Logic Implementation

### 7.1 Project Financial Calculations
- [x] Implement automatic profit calculation
- [x] Create pending amount calculation
- [x] Set up received amount tracking
- [x] Implement financial summary endpoints

### 7.2 Auto-generation Features
- [x] Implement auto-generated project codes (CYM-{id})
- [x] Set up automatic client assignment based on company
- [x] Create status auto-conversion to uppercase

### 7.3 Search and Filtering
- [x] Implement advanced search across all entities
- [x] Create filtering by multiple criteria
- [x] Set up pagination for large datasets
- [x] Implement sorting functionality

## Phase 8: Validation and Error Handling

### 8.1 Input Validation
- [x] Install validation library (Joi or express-validator)
- [x] Create validation schemas for all models
- [x] Implement request validation middleware
- [x] Set up custom validation rules

### 8.2 Error Handling
- [x] Create centralized error handling middleware
- [x] Implement custom error classes
- [x] Set up error logging
- [x] Create user-friendly error responses

## Phase 9: Security Implementation

### 9.1 Security Middleware
- [x] Install and configure helmet.js
- [x] Set up rate limiting
- [x] Implement CORS configuration
- [x] Add request sanitization

### 9.2 Authentication Security
- [x] Implement JWT token security
- [x] Set up session security
- [x] Add OTP expiration and rate limiting
- [x] Implement password hashing (if needed)

## Phase 10: Testing and Documentation

### 10.1 Testing
- [x] Set up testing framework (Jest)
- [ ] Write unit tests for all models
- [ ] Create integration tests for APIs
- [ ] Implement end-to-end testing
- [ ] Set up test database

### 10.2 API Documentation
- [ ] Install Swagger/OpenAPI
- [ ] Document all API endpoints
- [ ] Create API usage examples
- [ ] Set up interactive API documentation

## Phase 11: Performance Optimization

### 11.1 Database Optimization
- [x] Add database indexes
- [x] Implement query optimization
- [x] Set up database connection pooling
- [x] Create database performance monitoring

### 11.2 Caching
- [x] Install Redis for caching
- [x] Implement API response caching
- [x] Set up session caching
- [x] Create cache invalidation strategies

## Phase 12: Deployment Preparation

### 12.1 Production Configuration
- [x] Set up production environment variables
- [x] Configure production database
- [x] Set up production logging
- [x] Implement health check endpoints

### 12.2 Docker Configuration
- [ ] Create Dockerfile
- [ ] Set up docker-compose for development
- [ ] Configure production Docker setup
- [ ] Set up database migrations in Docker

## Phase 13: Data Migration

### 13.1 Data Export from Django
- [ ] Create Django management command to export data
- [ ] Export all model data to JSON/CSV
- [ ] Handle file migrations (images)
- [ ] Create data validation scripts

### 13.2 Data Import to Node.js
- [ ] Create data import scripts
- [ ] Implement data transformation logic
- [ ] Set up foreign key mapping
- [ ] Validate imported data integrity

## Phase 14: Final Integration and Testing

### 14.1 System Integration
- [ ] Test all API endpoints
- [ ] Verify business logic implementation
- [ ] Test external service integrations
- [ ] Validate data consistency

### 14.2 Performance Testing
- [ ] Load testing for all endpoints
- [ ] Database performance testing
- [ ] Memory usage optimization
- [ ] Response time optimization

## Phase 15: Documentation and Handover

### 15.1 Technical Documentation
- [ ] Create API documentation
- [ ] Write deployment guide
- [ ] Document configuration settings
- [ ] Create troubleshooting guide

### 15.2 User Documentation
- [ ] Create user manual
- [ ] Document new features
- [ ] Create migration guide
- [ ] Set up support documentation

## Success Criteria
- [ ] All Django functionality replicated in Node.js
- [ ] All data successfully migrated
- [ ] Performance meets or exceeds Django version
- [ ] All tests passing
- [ ] Documentation complete
- [ ] Production deployment successful
