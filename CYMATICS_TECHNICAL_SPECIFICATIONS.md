# Cymatics Node.js Backend - Technical Specifications

## Technology Stack Recommendations

### Core Framework
- **Node.js**: v18+ (LTS)
- **Express.js**: v4.18+ (Web framework)
- **TypeScript**: v5+ (Type safety and better development experience)

### Database & ORM
- **PostgreSQL**: v14+ (Same as Django setup)
- **Prisma**: v5+ (Modern ORM with excellent TypeScript support)
- **Alternative**: Sequelize v6+ or TypeORM v0.3+

### Authentication & Security
- **jsonwebtoken**: v9+ (JWT token management)
- **bcryptjs**: v2.4+ (Password hashing if needed)
- **express-rate-limit**: v6+ (Rate limiting)
- **helmet**: v7+ (Security headers)
- **cors**: v2.8+ (CORS handling)

### File Upload & Processing
- **multer**: v1.4+ (File upload middleware)
- **sharp**: v0.32+ (Image processing)
- **uuid**: v9+ (Unique file naming)

### Email & External Services
- **nodemailer**: v6.9+ (Email sending)
- **@googlemaps/google-maps-services-js**: v3+ (Google Maps API)
- **axios**: v1.5+ (HTTP client for external APIs)

### Validation & Utilities
- **joi**: v17+ (Input validation)
- **moment**: v2.29+ or **date-fns**: v2.30+ (Date manipulation)
- **lodash**: v4.17+ (Utility functions)

### Development & Testing
- **jest**: v29+ (Testing framework)
- **supertest**: v6+ (API testing)
- **nodemon**: v3+ (Development auto-restart)
- **eslint**: v8+ (Code linting)
- **prettier**: v3+ (Code formatting)

### Documentation & Monitoring
- **swagger-jsdoc**: v6+ (API documentation)
- **swagger-ui-express**: v5+ (API documentation UI)
- **winston**: v3.10+ (Logging)
- **morgan**: v1.10+ (HTTP request logging)

## Project Structure

```
cymatics-node-backend/
├── src/
│   ├── config/
│   │   ├── database.ts
│   │   ├── email.ts
│   │   ├── google-maps.ts
│   │   └── index.ts
│   ├── controllers/
│   │   ├── auth.controller.ts
│   │   ├── client.controller.ts
│   │   ├── project.controller.ts
│   │   ├── income.controller.ts
│   │   ├── expense.controller.ts
│   │   ├── asset.controller.ts
│   │   ├── entertainment.controller.ts
│   │   └── calendar.controller.ts
│   ├── middleware/
│   │   ├── auth.middleware.ts
│   │   ├── validation.middleware.ts
│   │   ├── upload.middleware.ts
│   │   └── error.middleware.ts
│   ├── models/
│   │   ├── client.model.ts
│   │   ├── project.model.ts
│   │   ├── income.model.ts
│   │   ├── expense.model.ts
│   │   ├── asset.model.ts
│   │   ├── entertainment.model.ts
│   │   ├── calendar.model.ts
│   │   └── user.model.ts
│   ├── routes/
│   │   ├── auth.routes.ts
│   │   ├── client.routes.ts
│   │   ├── project.routes.ts
│   │   ├── financial.routes.ts
│   │   ├── asset.routes.ts
│   │   ├── entertainment.routes.ts
│   │   └── calendar.routes.ts
│   ├── services/
│   │   ├── auth.service.ts
│   │   ├── email.service.ts
│   │   ├── maps.service.ts
│   │   ├── file.service.ts
│   │   └── calculation.service.ts
│   ├── utils/
│   │   ├── validators.ts
│   │   ├── helpers.ts
│   │   ├── constants.ts
│   │   └── errors.ts
│   ├── types/
│   │   ├── auth.types.ts
│   │   ├── project.types.ts
│   │   └── common.types.ts
│   └── app.ts
├── prisma/
│   ├── schema.prisma
│   ├── migrations/
│   └── seed.ts
├── uploads/
├── tests/
│   ├── unit/
│   ├── integration/
│   └── e2e/
├── docs/
├── .env.example
├── .gitignore
├── package.json
├── tsconfig.json
├── jest.config.js
└── README.md
```

## Database Schema (Prisma)

```prisma
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        Int      @id @default(autoincrement())
  username  String   @unique
  email     String   @unique
  isActive  Boolean  @default(true) @map("is_active")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  
  emailOTPs EmailOTP[]
  
  @@map("users")
}

model EmailOTP {
  id        Int      @id @default(autoincrement())
  userId    Int      @map("user_id")
  otp       String   @db.VarChar(6)
  createdAt DateTime @default(now()) @map("created_at")
  expiresAt DateTime @map("expires_at")
  isUsed    Boolean  @default(false) @map("is_used")
  
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("email_otps")
}

model Client {
  id        Int      @id @default(autoincrement())
  name      String   @db.VarChar(100)
  company   String   @db.VarChar(100)
  number    String   @db.VarChar(20)
  email     String?  @db.VarChar(100)
  img       String?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  
  projects Project[]
  
  @@map("clients")
}

model Outclient {
  id        Int      @id @default(autoincrement())
  name      String   @db.VarChar(100)
  company   String   @db.VarChar(100)
  number    String   @db.VarChar(20)
  email     String?  @db.VarChar(100)
  img       String?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  
  @@map("outclients")
}

model Project {
  id                Int       @id @default(autoincrement())
  code              String    @unique
  name              String?   @db.VarChar(100)
  company           String?   @db.VarChar(100)
  type              String?   @db.VarChar(50)
  status            String?   @db.VarChar(50)
  shootStartDate    DateTime? @map("shoot_start_date")
  shootEndDate      DateTime? @map("shoot_end_date")
  amount            Int       @default(0)
  location          String?   @db.VarChar(200)
  latitude          Float     @default(0.0)
  longitude         Float     @default(0.0)
  outsourcing       Boolean   @default(false)
  reference         String?   @db.Text
  image             String?
  pendingAmt        Int       @default(0) @map("pending_amt")
  receivedAmt       Int       @default(0) @map("received_amt")
  address           String?   @db.VarChar(500)
  map               String?   @db.VarChar(200)
  profit            Int       @default(0)
  rating            Int       @default(0)
  outsourcingAmt    Int       @default(0) @map("outsourcing_amt")
  outFor            String?   @db.VarChar(100) @map("out_for")
  outClient         String?   @db.VarChar(100) @map("out_client")
  outsourcingPaid   Boolean   @default(false) @map("outsourcing_paid")
  clientId          Int       @map("client_id")
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")
  
  client   Client    @relation(fields: [clientId], references: [id], onDelete: Cascade)
  incomes  Income[]  @relation("ProjectIncomes")
  expenses Expense[] @relation("ProjectExpenses")
  
  @@map("projects")
}

model Income {
  id            Int      @id @default(autoincrement())
  date          DateTime @db.Date
  description   String   @db.Text
  amount        Int
  note          String?  @db.Text
  projectIncome Boolean  @default(false) @map("project_income")
  projectId     Int?     @map("project_id")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")
  
  project Project? @relation("ProjectIncomes", fields: [projectId], references: [id], onDelete: Cascade)
  
  @@map("incomes")
}

model Expense {
  id             Int      @id @default(autoincrement())
  date           DateTime @db.Date
  category       String   @db.VarChar(50)
  description    String   @db.Text
  amount         Int
  notes          String?  @db.Text
  projectExpense Boolean  @default(false) @map("project_expense")
  projectId      Int?     @map("project_id")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")
  
  project Project? @relation("ProjectExpenses", fields: [projectId], references: [id], onDelete: Cascade)
  
  @@map("expenses")
}

model Asset {
  id        Int      @id @default(autoincrement())
  date      DateTime @db.Date
  type      String   @db.VarChar(100)
  name      String   @db.VarChar(200)
  quantity  Decimal  @db.Decimal(65, 30)
  buyPrice  Decimal  @db.Decimal(65, 30) @map("buy_price")
  value     Int      @default(0)
  note      String?  @db.Text
  image     String?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  
  @@map("assets")
}

model Entertainment {
  id        Int      @id @default(autoincrement())
  date      DateTime @default(now())
  type      String   @db.VarChar(100)
  language  String   @db.VarChar(100)
  rating    Int
  name      String   @db.VarChar(100)
  source    String?  @db.VarChar(100)
  image     String?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  
  @@map("entertainment")
}

model CalendarEvent {
  id        Int      @id @default(autoincrement())
  title     String   @db.VarChar(255)
  startTime DateTime @map("start_time")
  endTime   DateTime @map("end_time")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  
  @@map("calendar_events")
}
```

## Environment Configuration

```env
# .env.example
NODE_ENV=development
PORT=3000

# Database
DATABASE_URL="postgresql://postgres:darkside@localhost:5433/postgres"

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=vbjf yidq fwnl gsfm
FROM_EMAIL=<EMAIL>

# Google Maps
GOOGLE_MAPS_API_KEY=AIzaSyAqIalCM0rqsXeHiyRP4ImBbVgqwMSv8D8

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# OTP Configuration
OTP_EXPIRES_IN_MINUTES=10
OTP_MAX_ATTEMPTS=3
```

## API Response Standards

### Success Response Format
```typescript
interface SuccessResponse<T> {
  success: true;
  data: T;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
```

### Error Response Format
```typescript
interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
  path: string;
}
```

## Key Implementation Details

### 1. Auto-generated Project Codes
```typescript
// In project service
async createProject(data: CreateProjectData): Promise<Project> {
  const project = await prisma.project.create({
    data: {
      ...data,
      code: '', // Temporary empty code
    },
  });
  
  // Update with generated code
  return await prisma.project.update({
    where: { id: project.id },
    data: { code: `CYM-${project.id}` },
  });
}
```

### 2. Financial Calculations
```typescript
// In project service
async updateProjectFinances(projectId: number): Promise<void> {
  const project = await prisma.project.findUnique({
    where: { id: projectId },
    include: {
      incomes: true,
      expenses: true,
    },
  });
  
  if (!project) return;
  
  const totalExpenses = project.expenses.reduce((sum, exp) => sum + exp.amount, 0);
  const totalIncome = project.incomes.reduce((sum, inc) => sum + inc.amount, 0);
  
  await prisma.project.update({
    where: { id: projectId },
    data: {
      profit: project.amount - (project.outsourcingAmt + totalExpenses),
      receivedAmt: totalIncome,
      pendingAmt: project.amount - totalIncome,
    },
  });
}
```

### 3. OTP System
```typescript
// In auth service
async sendOTP(email: string): Promise<void> {
  const otp = Math.floor(100000 + Math.random() * 900000).toString();
  const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
  
  // Save OTP to database
  await prisma.emailOTP.create({
    data: {
      userId: user.id,
      otp,
      expiresAt,
    },
  });
  
  // Send email
  await emailService.sendOTP(email, otp);
}
```

### 4. File Upload Handling
```typescript
// Upload middleware configuration
const upload = multer({
  storage: multer.diskStorage({
    destination: (req, file, cb) => {
      cb(null, 'uploads/');
    },
    filename: (req, file, cb) => {
      const uniqueName = `${Date.now()}-${Math.round(Math.random() * 1E9)}${path.extname(file.originalname)}`;
      cb(null, uniqueName);
    },
  }),
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE || '5242880'), // 5MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  },
});
```

## Performance Considerations

### 1. Database Indexing
```sql
-- Add indexes for frequently queried fields
CREATE INDEX idx_projects_code ON projects(code);
CREATE INDEX idx_projects_client_id ON projects(client_id);
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_expenses_project_id ON expenses(project_id);
CREATE INDEX idx_incomes_project_id ON incomes(project_id);
CREATE INDEX idx_projects_shoot_dates ON projects(shoot_start_date, shoot_end_date);
```

### 2. Query Optimization
```typescript
// Use select to limit returned fields
const projects = await prisma.project.findMany({
  select: {
    id: true,
    code: true,
    name: true,
    status: true,
    amount: true,
    client: {
      select: {
        name: true,
        company: true,
      },
    },
  },
  where: filters,
  orderBy: { createdAt: 'desc' },
  take: limit,
  skip: offset,
});
```

### 3. Caching Strategy
```typescript
// Redis caching for frequently accessed data
const cacheKey = `project:${projectCode}`;
let project = await redis.get(cacheKey);

if (!project) {
  project = await prisma.project.findUnique({
    where: { code: projectCode },
    include: { client: true },
  });
  
  await redis.setex(cacheKey, 300, JSON.stringify(project)); // 5 minutes cache
}
```

This technical specification provides a comprehensive foundation for implementing the Node.js backend that fully replicates the Django functionality while following modern Node.js best practices.
