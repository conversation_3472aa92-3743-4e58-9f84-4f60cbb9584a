{"clientVersion":"5.22.0","code":"P2021","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError sending OTP: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","meta":{"modelName":"User","table":"public.users"},"name":"PrismaClientKnownRequestError","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-05 17:48:50:4850"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","method":"POST","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-05 17:48:50:4850","url":"/api/auth/send-otp","userAgent":"python-requests/2.32.3"}
{"clientVersion":"5.22.0","code":"P2021","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError sending OTP: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","meta":{"modelName":"User","table":"public.users"},"name":"PrismaClientKnownRequestError","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-05 17:56:22:5622"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","method":"POST","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-05 17:56:22:5622","url":"/api/auth/send-otp","userAgent":"python-requests/2.32.3"}
{"clientVersion":"5.22.0","code":"P2021","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError sending OTP: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","meta":{"modelName":"User","table":"public.users"},"name":"PrismaClientKnownRequestError","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-05 18:03:43:343"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","method":"POST","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-05 18:03:43:343","url":"/api/auth/send-otp","userAgent":"python-requests/2.32.3"}
{"clientVersion":"5.22.0","code":"P2021","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError sending OTP: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","meta":{"modelName":"User","table":"public.users"},"name":"PrismaClientKnownRequestError","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-05 18:11:55:1155"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","method":"POST","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-05 18:11:55:1155","url":"/api/auth/send-otp","userAgent":"python-requests/2.32.3"}
{"clientVersion":"5.22.0","code":"P2021","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError sending OTP: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","meta":{"modelName":"User","table":"public.users"},"name":"PrismaClientKnownRequestError","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-05 18:27:18:2718"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","method":"POST","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-05 18:27:18:2718","url":"/api/auth/send-otp","userAgent":"python-requests/2.32.3"}
{"clientVersion":"5.22.0","code":"P2021","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError sending OTP: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","meta":{"modelName":"User","table":"public.users"},"name":"PrismaClientKnownRequestError","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-05 18:34:23:3423"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","method":"POST","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-05 18:34:23:3423","url":"/api/auth/send-otp","userAgent":"python-requests/2.32.3"}
{"clientVersion":"5.22.0","code":"P2021","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError sending OTP: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","meta":{"modelName":"User","table":"public.users"},"name":"PrismaClientKnownRequestError","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-05 18:34:54:3454"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: \u001b[39m\n\u001b[31mInvalid `prisma.user.findUnique()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\u001b[39m\n\n\u001b[31m  41 const username = extractNameFromEmail(email);\u001b[39m\n\u001b[31m  42 \u001b[39m\n\u001b[31m  43 // Find or create user\u001b[39m\n\u001b[31m→ 44 let user = await prisma.user.findUnique(\u001b[39m\n\u001b[31mThe table `public.users` does not exist in the current database.\u001b[39m","method":"POST","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database.\n    at $n.handleRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7315)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async AuthService.sendOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:18)\n    at async sendOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:14:22)","timestamp":"2025-06-05 18:34:54:3454","url":"/api/auth/send-otp","userAgent":"python-requests/2.32.3"}
{"code":"NOT_FOUND_ERROR","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError getting client by ID: Client not found\u001b[39m","name":"NotFoundError","stack":"NotFoundError: Client not found\n    at ClientService.getClientById (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\client.service.ts:198:15)\n    at async getClientById (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\client.controller.ts:40:22)","statusCode":404,"timestamp":"2025-06-05 18:37:26:3726"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Client not found\u001b[39m","method":"GET","stack":"NotFoundError: Client not found\n    at ClientService.getClientById (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\client.service.ts:198:15)\n    at async getClientById (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\client.controller.ts:40:22)","timestamp":"2025-06-05 18:37:26:3726","url":"/api/clients/99999","userAgent":"python-requests/2.32.3"}
{"code":"NOT_FOUND_ERROR","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError getting client by ID: Client not found\u001b[39m","name":"NotFoundError","stack":"NotFoundError: Client not found\n    at ClientService.getClientById (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\client.service.ts:198:15)\n    at async getClientById (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\client.controller.ts:40:22)","statusCode":404,"timestamp":"2025-06-05 18:44:48:4448"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Client not found\u001b[39m","method":"GET","stack":"NotFoundError: Client not found\n    at ClientService.getClientById (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\client.service.ts:198:15)\n    at async getClientById (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\client.controller.ts:40:22)","timestamp":"2025-06-05 18:44:48:4448","url":"/api/clients/99999","userAgent":"python-requests/2.32.3"}
{"code":"VALIDATION_ERROR","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError creating project: Client not found\u001b[39m","name":"ValidationError","stack":"ValidationError: Client not found\n    at ProjectService.createProject (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\project.service.ts:120:15)\n    at async createProject (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\project.controller.ts:117:23)","statusCode":400,"timestamp":"2025-06-05 18:44:48:4448"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Client not found\u001b[39m","method":"POST","stack":"ValidationError: Client not found\n    at ProjectService.createProject (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\project.service.ts:120:15)\n    at async createProject (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\project.controller.ts:117:23)","timestamp":"2025-06-05 18:44:48:4448","url":"/api/projects","userAgent":"python-requests/2.32.3"}
{"code":"NOT_FOUND_ERROR","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError getting client by ID: Client not found\u001b[39m","name":"NotFoundError","stack":"NotFoundError: Client not found\n    at ClientService.getClientById (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\client.service.ts:198:15)\n    at async getClientById (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\client.controller.ts:40:22)","statusCode":404,"timestamp":"2025-06-05 18:48:58:4858"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Client not found\u001b[39m","method":"GET","stack":"NotFoundError: Client not found\n    at ClientService.getClientById (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\client.service.ts:198:15)\n    at async getClientById (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\client.controller.ts:40:22)","timestamp":"2025-06-05 18:48:58:4858","url":"/api/clients/99999","userAgent":"python-requests/2.32.3"}
{"code":"NOT_FOUND_ERROR","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError getting client by ID: Client not found\u001b[39m","name":"NotFoundError","stack":"NotFoundError: Client not found\n    at ClientService.getClientById (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\client.service.ts:198:15)\n    at async getClientById (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\client.controller.ts:40:22)","statusCode":404,"timestamp":"2025-06-06 10:38:47:3847"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Client not found\u001b[39m","method":"GET","stack":"NotFoundError: Client not found\n    at ClientService.getClientById (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\client.service.ts:198:15)\n    at async getClientById (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\client.controller.ts:40:22)","timestamp":"2025-06-06 10:38:47:3847","url":"/api/clients/99999","userAgent":"python-requests/2.32.3"}
{"clientVersion":"5.22.0","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError updating payment: \u001b[39m\n\u001b[31mInvalid `prisma.project.update()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\payment.service.ts:332:44\u001b[39m\n\n\u001b[31m  329   }\u001b[39m\n\u001b[31m  330 }\u001b[39m\n\u001b[31m  331 \u001b[39m\n\u001b[31m→ 332 const project = await prisma.project.update({\u001b[39m\n\u001b[31m        where: {\u001b[39m\n\u001b[31m          id: 4\u001b[39m\n\u001b[31m        },\u001b[39m\n\u001b[31m        data: {\u001b[39m\n\u001b[31m          amount: 3000,\u001b[39m\n\u001b[31m          pendingAmt: 3000,\u001b[39m\n\u001b[31m          description: \"Updated Test Payment\",\u001b[39m\n\u001b[31m          ~~~~~~~~~~~\u001b[39m\n\u001b[31m          name: \"Updated Test Payment\",\u001b[39m\n\u001b[31m      ?   code?: String | StringFieldUpdateOperationsInput,\u001b[39m\n\u001b[31m      ?   company?: String | NullableStringFieldUpdateOperationsInput | Null,\u001b[39m\n\u001b[31m      ?   type?: String | NullableStringFieldUpdateOperationsInput | Null,\u001b[39m\n\u001b[31m      ?   status?: String | NullableStringFieldUpdateOperationsInput | Null,\u001b[39m\n\u001b[31m      ?   shootStartDate?: DateTime | NullableDateTimeFieldUpdateOperationsInput | Null,\u001b[39m\n\u001b[31m      ?   shootEndDate?: DateTime | NullableDateTimeFieldUpdateOperationsInput | Null,\u001b[39m\n\u001b[31m      ?   location?: String | NullableStringFieldUpdateOperationsInput | Null,\u001b[39m\n\u001b[31m      ?   latitude?: Float | FloatFieldUpdateOperationsInput,\u001b[39m\n\u001b[31m      ?   longitude?: Float | FloatFieldUpdateOperationsInput,\u001b[39m\n\u001b[31m      ?   outsourcing?: Boolean | BoolFieldUpdateOperationsInput,\u001b[39m\n\u001b[31m      ?   reference?: String | NullableStringFieldUpdateOperationsInput | Null,\u001b[39m\n\u001b[31m      ?   image?: String | NullableStringFieldUpdateOperationsInput | Null,\u001b[39m\n\u001b[31m      ?   receivedAmt?: Int | IntFieldUpdateOperationsInput,\u001b[39m\n\u001b[31m      ?   address?: String | NullableStringFieldUpdateOperationsInput | Null,\u001b[39m\n\u001b[31m      ?   map?: String | NullableStringFieldUpdateOperationsInput | Null,\u001b[39m\n\u001b[31m      ?   profit?: Int | IntFieldUpdateOperationsInput,\u001b[39m\n\u001b[31m      ?   rating?: Int | IntFieldUpdateOperationsInput,\u001b[39m\n\u001b[31m      ?   outsourcingAmt?: Int | IntFieldUpdateOperationsInput,\u001b[39m\n\u001b[31m      ?   outFor?: String | NullableStringFieldUpdateOperationsInput | Null,\u001b[39m\n\u001b[31m      ?   outClient?: String | NullableStringFieldUpdateOperationsInput | Null,\u001b[39m\n\u001b[31m      ?   outsourcingPaid?: Boolean | BoolFieldUpdateOperationsInput,\u001b[39m\n\u001b[31m      ?   createdAt?: DateTime | DateTimeFieldUpdateOperationsInput,\u001b[39m\n\u001b[31m      ?   updatedAt?: DateTime | DateTimeFieldUpdateOperationsInput,\u001b[39m\n\u001b[31m      ?   client?: ClientUpdateOneRequiredWithoutProjectsNestedInput,\u001b[39m\n\u001b[31m      ?   incomes?: IncomeUpdateManyWithoutProjectNestedInput,\u001b[39m\n\u001b[31m      ?   expenses?: ExpenseUpdateManyWithoutProjectNestedInput\u001b[39m\n\u001b[31m        },\u001b[39m\n\u001b[31m        include: {\u001b[39m\n\u001b[31m          client: {\u001b[39m\n\u001b[31m            select: {\u001b[39m\n\u001b[31m              name: true,\u001b[39m\n\u001b[31m              company: true\u001b[39m\n\u001b[31m            }\u001b[39m\n\u001b[31m          }\u001b[39m\n\u001b[31m        }\u001b[39m\n\u001b[31m      })\u001b[39m\n\n\u001b[31mUnknown argument `description`. Available options are marked with ?.\u001b[39m","name":"PrismaClientValidationError","stack":"PrismaClientValidationError: \nInvalid `prisma.project.update()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\payment.service.ts:332:44\n\n  329   }\n  330 }\n  331 \n→ 332 const project = await prisma.project.update({\n        where: {\n          id: 4\n        },\n        data: {\n          amount: 3000,\n          pendingAmt: 3000,\n          description: \"Updated Test Payment\",\n          ~~~~~~~~~~~\n          name: \"Updated Test Payment\",\n      ?   code?: String | StringFieldUpdateOperationsInput,\n      ?   company?: String | NullableStringFieldUpdateOperationsInput | Null,\n      ?   type?: String | NullableStringFieldUpdateOperationsInput | Null,\n      ?   status?: String | NullableStringFieldUpdateOperationsInput | Null,\n      ?   shootStartDate?: DateTime | NullableDateTimeFieldUpdateOperationsInput | Null,\n      ?   shootEndDate?: DateTime | NullableDateTimeFieldUpdateOperationsInput | Null,\n      ?   location?: String | NullableStringFieldUpdateOperationsInput | Null,\n      ?   latitude?: Float | FloatFieldUpdateOperationsInput,\n      ?   longitude?: Float | FloatFieldUpdateOperationsInput,\n      ?   outsourcing?: Boolean | BoolFieldUpdateOperationsInput,\n      ?   reference?: String | NullableStringFieldUpdateOperationsInput | Null,\n      ?   image?: String | NullableStringFieldUpdateOperationsInput | Null,\n      ?   receivedAmt?: Int | IntFieldUpdateOperationsInput,\n      ?   address?: String | NullableStringFieldUpdateOperationsInput | Null,\n      ?   map?: String | NullableStringFieldUpdateOperationsInput | Null,\n      ?   profit?: Int | IntFieldUpdateOperationsInput,\n      ?   rating?: Int | IntFieldUpdateOperationsInput,\n      ?   outsourcingAmt?: Int | IntFieldUpdateOperationsInput,\n      ?   outFor?: String | NullableStringFieldUpdateOperationsInput | Null,\n      ?   outClient?: String | NullableStringFieldUpdateOperationsInput | Null,\n      ?   outsourcingPaid?: Boolean | BoolFieldUpdateOperationsInput,\n      ?   createdAt?: DateTime | DateTimeFieldUpdateOperationsInput,\n      ?   updatedAt?: DateTime | DateTimeFieldUpdateOperationsInput,\n      ?   client?: ClientUpdateOneRequiredWithoutProjectsNestedInput,\n      ?   incomes?: IncomeUpdateManyWithoutProjectNestedInput,\n      ?   expenses?: ExpenseUpdateManyWithoutProjectNestedInput\n        },\n        include: {\n          client: {\n            select: {\n              name: true,\n              company: true\n            }\n          }\n        }\n      })\n\nUnknown argument `description`. Available options are marked with ?.\n    at wn (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:29:1363)\n    at $n.handleRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6958)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async PaymentService.updatePayment (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\payment.service.ts:332:23)\n    at async updatePayment (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\payment.controller.ts:79:23)","timestamp":"2025-06-06 10:38:48:3848"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: \u001b[39m\n\u001b[31mInvalid `prisma.project.update()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\payment.service.ts:332:44\u001b[39m\n\n\u001b[31m  329   }\u001b[39m\n\u001b[31m  330 }\u001b[39m\n\u001b[31m  331 \u001b[39m\n\u001b[31m→ 332 const project = await prisma.project.update({\u001b[39m\n\u001b[31m        where: {\u001b[39m\n\u001b[31m          id: 4\u001b[39m\n\u001b[31m        },\u001b[39m\n\u001b[31m        data: {\u001b[39m\n\u001b[31m          amount: 3000,\u001b[39m\n\u001b[31m          pendingAmt: 3000,\u001b[39m\n\u001b[31m          description: \"Updated Test Payment\",\u001b[39m\n\u001b[31m          ~~~~~~~~~~~\u001b[39m\n\u001b[31m          name: \"Updated Test Payment\",\u001b[39m\n\u001b[31m      ?   code?: String | StringFieldUpdateOperationsInput,\u001b[39m\n\u001b[31m      ?   company?: String | NullableStringFieldUpdateOperationsInput | Null,\u001b[39m\n\u001b[31m      ?   type?: String | NullableStringFieldUpdateOperationsInput | Null,\u001b[39m\n\u001b[31m      ?   status?: String | NullableStringFieldUpdateOperationsInput | Null,\u001b[39m\n\u001b[31m      ?   shootStartDate?: DateTime | NullableDateTimeFieldUpdateOperationsInput | Null,\u001b[39m\n\u001b[31m      ?   shootEndDate?: DateTime | NullableDateTimeFieldUpdateOperationsInput | Null,\u001b[39m\n\u001b[31m      ?   location?: String | NullableStringFieldUpdateOperationsInput | Null,\u001b[39m\n\u001b[31m      ?   latitude?: Float | FloatFieldUpdateOperationsInput,\u001b[39m\n\u001b[31m      ?   longitude?: Float | FloatFieldUpdateOperationsInput,\u001b[39m\n\u001b[31m      ?   outsourcing?: Boolean | BoolFieldUpdateOperationsInput,\u001b[39m\n\u001b[31m      ?   reference?: String | NullableStringFieldUpdateOperationsInput | Null,\u001b[39m\n\u001b[31m      ?   image?: String | NullableStringFieldUpdateOperationsInput | Null,\u001b[39m\n\u001b[31m      ?   receivedAmt?: Int | IntFieldUpdateOperationsInput,\u001b[39m\n\u001b[31m      ?   address?: String | NullableStringFieldUpdateOperationsInput | Null,\u001b[39m\n\u001b[31m      ?   map?: String | NullableStringFieldUpdateOperationsInput | Null,\u001b[39m\n\u001b[31m      ?   profit?: Int | IntFieldUpdateOperationsInput,\u001b[39m\n\u001b[31m      ?   rating?: Int | IntFieldUpdateOperationsInput,\u001b[39m\n\u001b[31m      ?   outsourcingAmt?: Int | IntFieldUpdateOperationsInput,\u001b[39m\n\u001b[31m      ?   outFor?: String | NullableStringFieldUpdateOperationsInput | Null,\u001b[39m\n\u001b[31m      ?   outClient?: String | NullableStringFieldUpdateOperationsInput | Null,\u001b[39m\n\u001b[31m      ?   outsourcingPaid?: Boolean | BoolFieldUpdateOperationsInput,\u001b[39m\n\u001b[31m      ?   createdAt?: DateTime | DateTimeFieldUpdateOperationsInput,\u001b[39m\n\u001b[31m      ?   updatedAt?: DateTime | DateTimeFieldUpdateOperationsInput,\u001b[39m\n\u001b[31m      ?   client?: ClientUpdateOneRequiredWithoutProjectsNestedInput,\u001b[39m\n\u001b[31m      ?   incomes?: IncomeUpdateManyWithoutProjectNestedInput,\u001b[39m\n\u001b[31m      ?   expenses?: ExpenseUpdateManyWithoutProjectNestedInput\u001b[39m\n\u001b[31m        },\u001b[39m\n\u001b[31m        include: {\u001b[39m\n\u001b[31m          client: {\u001b[39m\n\u001b[31m            select: {\u001b[39m\n\u001b[31m              name: true,\u001b[39m\n\u001b[31m              company: true\u001b[39m\n\u001b[31m            }\u001b[39m\n\u001b[31m          }\u001b[39m\n\u001b[31m        }\u001b[39m\n\u001b[31m      })\u001b[39m\n\n\u001b[31mUnknown argument `description`. Available options are marked with ?.\u001b[39m","method":"PUT","stack":"PrismaClientValidationError: \nInvalid `prisma.project.update()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\payment.service.ts:332:44\n\n  329   }\n  330 }\n  331 \n→ 332 const project = await prisma.project.update({\n        where: {\n          id: 4\n        },\n        data: {\n          amount: 3000,\n          pendingAmt: 3000,\n          description: \"Updated Test Payment\",\n          ~~~~~~~~~~~\n          name: \"Updated Test Payment\",\n      ?   code?: String | StringFieldUpdateOperationsInput,\n      ?   company?: String | NullableStringFieldUpdateOperationsInput | Null,\n      ?   type?: String | NullableStringFieldUpdateOperationsInput | Null,\n      ?   status?: String | NullableStringFieldUpdateOperationsInput | Null,\n      ?   shootStartDate?: DateTime | NullableDateTimeFieldUpdateOperationsInput | Null,\n      ?   shootEndDate?: DateTime | NullableDateTimeFieldUpdateOperationsInput | Null,\n      ?   location?: String | NullableStringFieldUpdateOperationsInput | Null,\n      ?   latitude?: Float | FloatFieldUpdateOperationsInput,\n      ?   longitude?: Float | FloatFieldUpdateOperationsInput,\n      ?   outsourcing?: Boolean | BoolFieldUpdateOperationsInput,\n      ?   reference?: String | NullableStringFieldUpdateOperationsInput | Null,\n      ?   image?: String | NullableStringFieldUpdateOperationsInput | Null,\n      ?   receivedAmt?: Int | IntFieldUpdateOperationsInput,\n      ?   address?: String | NullableStringFieldUpdateOperationsInput | Null,\n      ?   map?: String | NullableStringFieldUpdateOperationsInput | Null,\n      ?   profit?: Int | IntFieldUpdateOperationsInput,\n      ?   rating?: Int | IntFieldUpdateOperationsInput,\n      ?   outsourcingAmt?: Int | IntFieldUpdateOperationsInput,\n      ?   outFor?: String | NullableStringFieldUpdateOperationsInput | Null,\n      ?   outClient?: String | NullableStringFieldUpdateOperationsInput | Null,\n      ?   outsourcingPaid?: Boolean | BoolFieldUpdateOperationsInput,\n      ?   createdAt?: DateTime | DateTimeFieldUpdateOperationsInput,\n      ?   updatedAt?: DateTime | DateTimeFieldUpdateOperationsInput,\n      ?   client?: ClientUpdateOneRequiredWithoutProjectsNestedInput,\n      ?   incomes?: IncomeUpdateManyWithoutProjectNestedInput,\n      ?   expenses?: ExpenseUpdateManyWithoutProjectNestedInput\n        },\n        include: {\n          client: {\n            select: {\n              name: true,\n              company: true\n            }\n          }\n        }\n      })\n\nUnknown argument `description`. Available options are marked with ?.\n    at wn (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:29:1363)\n    at $n.handleRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6958)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async PaymentService.updatePayment (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\payment.service.ts:332:23)\n    at async updatePayment (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\payment.controller.ts:79:23)","timestamp":"2025-06-06 10:38:48:3848","url":"/api/payments/4","userAgent":"python-requests/2.32.3"}
{"code":"NOT_FOUND_ERROR","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError getting client by ID: Client not found\u001b[39m","name":"NotFoundError","stack":"NotFoundError: Client not found\n    at ClientService.getClientById (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\client.service.ts:198:15)\n    at async getClientById (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\client.controller.ts:40:22)","statusCode":404,"timestamp":"2025-06-06 10:40:45:4045"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Client not found\u001b[39m","method":"GET","stack":"NotFoundError: Client not found\n    at ClientService.getClientById (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\client.service.ts:198:15)\n    at async getClientById (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\client.controller.ts:40:22)","timestamp":"2025-06-06 10:40:45:4045","url":"/api/clients/99999","userAgent":"python-requests/2.32.3"}
{"code":"AUTHENTICATION_ERROR","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError verifying OTP: Invalid or expired OTP\u001b[39m","name":"AuthenticationError","stack":"AuthenticationError: Invalid or expired OTP\n    at AuthService.verifyOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:131:15)\n    at async verifyOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:29:22)","statusCode":401,"timestamp":"2025-06-07 12:58:20:5820"}
{"ip":"::ffff:***************","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Invalid or expired OTP\u001b[39m","method":"POST","stack":"AuthenticationError: Invalid or expired OTP\n    at AuthService.verifyOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:131:15)\n    at async verifyOTP (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\auth.controller.ts:29:22)","timestamp":"2025-06-07 12:58:20:5820","url":"/api/auth/verify-otp","userAgent":"okhttp/4.12.0"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mPort 3000 is already in use\u001b[39m","timestamp":"2025-06-08 09:40:52:4052"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mPort 3000 is already in use\u001b[39m","timestamp":"2025-06-08 09:50:28:5028"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mPort 3000 is already in use\u001b[39m","timestamp":"2025-06-08 09:50:59:5059"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mPort 3000 is already in use\u001b[39m","timestamp":"2025-06-08 09:51:50:5150"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mPort 3000 is already in use\u001b[39m","timestamp":"2025-06-08 09:52:27:5227"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mPort 3000 is already in use\u001b[39m","timestamp":"2025-06-08 09:52:44:5244"}
{"code":"EDNS","command":"CONN","errno":-3008,"hostname":"smtp.gmail.com","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mEmail service connection failed: getaddrinfo ENOTFOUND smtp.gmail.com\u001b[39m","stack":"Error: getaddrinfo ENOTFOUND smtp.gmail.com\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","syscall":"getaddrinfo","timestamp":"2025-06-08 10:04:29:429"}
{"clientVersion":"5.22.0","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError getting projects: \u001b[39m\n\u001b[31mInvalid `prisma.project.count()` invocation in\u001b[39m\n\u001b[31mC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\project.service.ts:346:42\u001b[39m\n\n\u001b[31m  343 }\u001b[39m\n\u001b[31m  344 \u001b[39m\n\u001b[31m  345 // Get total count for pagination\u001b[39m\n\u001b[31m→ 346 const total = await prisma.project.count(\u001b[39m\n\u001b[31mOS can't spawn worker thread: The paging file is too small for this operation to complete. (os error 1455)\u001b[39m\n\n\u001b[31mThis is a non-recoverable error which probably happens when the Prisma Query Engine has a panic.\u001b[39m\n\n\u001b[31mhttps://github.com/prisma/prisma/issues/new?body=Hi+Prisma+Team%21+My+Prisma+Client+just+crashed.+This+is+the+report%3A%0A%23%23+Versions%0A%0A%7C+Name++++++++++++%7C+Version++++++++++++%7C%0A%7C-----------------%7C--------------------%7C%0A%7C+Node++++++++++++%7C+v20.18.0+++++++++++%7C+%0A%7C+OS++++++++++++++%7C+windows++++++++++++%7C%0A%7C+Prisma+Client+++%7C+5.22.0+++++++++++++%7C%0A%7C+Query+Engine++++%7C+605197351a3c8bdd595af2d2a9bc3025bca48ea2%7C%0A%7C+Database++++++++%7C+postgresql+++++++++%7C%0A%0A%0A%0A%23%23+Logs%0A%60%60%60%0Aequest%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0A%60%60%60%0A%0A%23%23+Client+Snippet%0A%60%60%60ts%0A%2F%2F+PLEASE+FILL+YOUR+CODE+SNIPPET+HERE%0A%60%60%60%0A%0A%23%23+Schema%0A%60%60%60prisma%0A%2F%2F+PLEASE+ADD+YOUR+SCHEMA+HERE+IF+POSSIBLE%0A%60%60%60%0A%0A%23%23+Prisma+Engine+Query%0A%60%60%60%0A%7B%22X%22%3Atrue%7D%7D%7D%7D%7D%0A%60%60%60%0A&title=OS+can%27t+spawn+worker+thread%3A+The+paging+file+is+too+small+for+this+operation+to+complete.+%28os+error+1455%29&template=bug_report.yml\u001b[39m\n\n\u001b[31mIf you want the Prisma team to look into it, please open the link above 🙏\u001b[39m\n\u001b[31mTo increase the chance of success, please post your schema and a snippet of\u001b[39m\n\u001b[31mhow you used Prisma Client in the issue. \u001b[39m\n\u001b[31m\u001b[39m","name":"PrismaClientRustPanicError","stack":"PrismaClientRustPanicError: \nInvalid `prisma.project.count()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\project.service.ts:346:42\n\n  343 }\n  344 \n  345 // Get total count for pagination\n→ 346 const total = await prisma.project.count(\nOS can't spawn worker thread: The paging file is too small for this operation to complete. (os error 1455)\n\nThis is a non-recoverable error which probably happens when the Prisma Query Engine has a panic.\n\nhttps://github.com/prisma/prisma/issues/new?body=Hi+Prisma+Team%21+My+Prisma+Client+just+crashed.+This+is+the+report%3A%0A%23%23+Versions%0A%0A%7C+Name++++++++++++%7C+Version++++++++++++%7C%0A%7C-----------------%7C--------------------%7C%0A%7C+Node++++++++++++%7C+v20.18.0+++++++++++%7C+%0A%7C+OS++++++++++++++%7C+windows++++++++++++%7C%0A%7C+Prisma+Client+++%7C+5.22.0+++++++++++++%7C%0A%7C+Query+Engine++++%7C+605197351a3c8bdd595af2d2a9bc3025bca48ea2%7C%0A%7C+Database++++++++%7C+postgresql+++++++++%7C%0A%0A%0A%0A%23%23+Logs%0A%60%60%60%0Aequest%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0Aprisma%3Aclient%3AlibraryEngine+sending+request%2C+this.libraryStarted%3A+true%0A%60%60%60%0A%0A%23%23+Client+Snippet%0A%60%60%60ts%0A%2F%2F+PLEASE+FILL+YOUR+CODE+SNIPPET+HERE%0A%60%60%60%0A%0A%23%23+Schema%0A%60%60%60prisma%0A%2F%2F+PLEASE+ADD+YOUR+SCHEMA+HERE+IF+POSSIBLE%0A%60%60%60%0A%0A%23%23+Prisma+Engine+Query%0A%60%60%60%0A%7B%22X%22%3Atrue%7D%7D%7D%7D%7D%0A%60%60%60%0A&title=OS+can%27t+spawn+worker+thread%3A+The+paging+file+is+too+small+for+this+operation+to+complete.+%28os+error+1455%29&template=bug_report.yml\n\nIf you want the Prisma team to look into it, please open the link above 🙏\nTo increase the chance of success, please post your schema and a snippet of\nhow you used Prisma Client in the issue. \n\n    at $n.handleRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:7676)\n    at $n.handleAndLogRequestError (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6623)\n    at $n.request (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:121:6307)\n    at async l (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:130:9633)\n    at async ProjectService.getProjects (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\project.service.ts:346:21)\n    at async getProjects (C:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\controllers\\project.controller.ts:34:22)","timestamp":"2025-06-08 19:50:12:5012"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mPort 3000 is already in use\u001b[39m","timestamp":"2025-06-09 13:47:00:470"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mPort 3000 is already in use\u001b[39m","timestamp":"2025-06-09 14:06:03:63"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mPort 3000 is already in use\u001b[39m","timestamp":"2025-06-09 14:11:42:1142"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mPort 3000 is already in use\u001b[39m","timestamp":"2025-06-09 14:13:45:1345"}
