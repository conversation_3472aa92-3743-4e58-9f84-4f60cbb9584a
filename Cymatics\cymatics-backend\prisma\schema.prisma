// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        Int      @id @default(autoincrement())
  username  String   @unique @db.VarChar(150)
  email     String   @unique @db.VarChar(254)
  isActive  Boolean  @default(true) @map("is_active")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  emailOTPs EmailOTP[]

  @@map("users")
}

model EmailOTP {
  id        Int      @id @default(autoincrement())
  userId    Int      @map("user_id")
  otp       String   @db.VarChar(6)
  createdAt DateTime @default(now()) @map("created_at")
  expiresAt DateTime @map("expires_at")
  isUsed    <PERSON>olean  @default(false) @map("is_used")

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("email_otps")
}

model Client {
  id        Int      @id @default(autoincrement())
  name      String   @db.VarChar(100)
  company   String   @db.VarChar(100)
  number    String   @db.VarChar(20)
  email     String?  @db.VarChar(100)
  img       String?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  projects Project[]

  @@map("clients")
}

model Outclient {
  id        Int      @id @default(autoincrement())
  name      String   @db.VarChar(100)
  company   String   @db.VarChar(100)
  number    String   @db.VarChar(20)
  email     String?  @db.VarChar(100)
  img       String?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("outclients")
}

model Project {
  id                Int       @id @default(autoincrement())
  code              String    @unique
  name              String?   @db.VarChar(100)
  company           String?   @db.VarChar(100)
  type              String?   @db.VarChar(50)
  status            String?   @db.VarChar(50)
  shootStartDate    DateTime? @map("shoot_start_date")
  shootEndDate      DateTime? @map("shoot_end_date")
  amount            Int       @default(0)
  location          String?   @db.VarChar(200)
  latitude          Float     @default(0.0)
  longitude         Float     @default(0.0)
  outsourcing       Boolean   @default(false)
  reference         String?   @db.Text
  image             String?
  pendingAmt        Int       @default(0) @map("pending_amt")
  receivedAmt       Int       @default(0) @map("received_amt")
  address           String?   @db.VarChar(500)
  map               String?   @db.VarChar(200)
  profit            Int       @default(0)
  rating            Int       @default(0)
  outsourcingAmt    Int       @default(0) @map("outsourcing_amt")
  outFor            String?   @db.VarChar(100) @map("out_for")
  outClient         String?   @db.VarChar(100) @map("out_client")
  outsourcingPaid   Boolean   @default(false) @map("outsourcing_paid")
  onedriveLink      String?   @db.Text @map("onedrive_link")
  clientId          Int       @default(1) @map("client_id")
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")

  client   Client    @relation(fields: [clientId], references: [id], onDelete: Cascade)
  incomes  Income[]  @relation("ProjectIncomes")
  expenses Expense[] @relation("ProjectExpenses")

  @@map("projects")
}

model Income {
  id            Int      @id @default(autoincrement())
  date          DateTime @db.Date
  description   String   @db.Text
  amount        Int
  note          String?  @db.Text
  projectIncome Boolean  @default(false) @map("project_income")
  projectId     Int?     @map("project_id")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  project Project? @relation("ProjectIncomes", fields: [projectId], references: [id], onDelete: Cascade)

  @@map("incomes")
}

model Expense {
  id             Int      @id @default(autoincrement())
  date           DateTime @db.Date
  category       String   @db.VarChar(50)
  description    String   @db.Text
  amount         Int
  notes          String?  @db.Text
  projectExpense Boolean  @default(false) @map("project_expense")
  projectId      Int?     @map("project_id")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  project Project? @relation("ProjectExpenses", fields: [projectId], references: [id], onDelete: Cascade)

  @@map("expenses")
}

model Asset {
  id        Int      @id @default(autoincrement())
  date      DateTime @db.Date
  type      String   @db.VarChar(100)
  name      String   @db.VarChar(200)
  quantity  Decimal  @db.Decimal(65, 30)
  buyPrice  Decimal  @db.Decimal(65, 30) @map("buy_price")
  value     Int      @default(0)
  note      String?  @db.Text
  image     String?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("assets")
}

model Entertainment {
  id        Int      @id @default(autoincrement())
  date      DateTime @default(now())
  type      String   @db.VarChar(100)
  language  String   @db.VarChar(100)
  rating    Int
  name      String   @db.VarChar(100)
  source    String?  @db.VarChar(100)
  image     String?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("entertainment")
}

model CalendarEvent {
  id        Int      @id @default(autoincrement())
  title     String   @db.VarChar(255)
  startTime DateTime @map("start_time")
  endTime   DateTime @map("end_time")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("calendar_events")
}
