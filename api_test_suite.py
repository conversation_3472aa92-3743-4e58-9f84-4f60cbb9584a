#!/usr/bin/env python3
"""
Cymatics Node.js Backend API Test Suite
Comprehensive testing script for all API endpoints
"""

import requests
import json
import os
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
from dataclasses import dataclass
import sys

# Configuration
BASE_URL = "http://localhost:3000"
TEST_EMAIL = "<EMAIL>"
LOGS_DIR = "logs"
TOKEN_FILE = "logs/auth_token.json"

@dataclass
class TestResult:
    endpoint: str
    method: str
    status_code: int
    success: bool
    response_time: float
    error_message: Optional[str] = None
    response_data: Optional[Dict] = None

class CymaticsAPITester:
    def __init__(self):
        self.base_url = BASE_URL
        self.session = requests.Session()
        self.auth_token = None
        self.test_results: List[TestResult] = []
        self.setup_logging()

        # Test data storage for cleanup
        self.created_resources = {
            'clients': [],
            'outclients': [],
            'projects': [],
            'income': [],
            'expenses': [],
            'assets': [],
            'entertainment': [],
            'calendar_events': []
        }

    def setup_logging(self):
        """Setup logging configuration"""
        if not os.path.exists(LOGS_DIR):
            os.makedirs(LOGS_DIR)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = f"{LOGS_DIR}/api_test_{timestamp}.log"

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.log_filename = log_filename

        # Try to load existing auth token
        self.load_auth_token()

    def make_request(self, method: str, endpoint: str, data: Dict = None,
                    files: Dict = None, params: Dict = None,
                    auth_required: bool = True) -> TestResult:
        """Make HTTP request and record results"""
        url = f"{self.base_url}{endpoint}"
        headers = {'Content-Type': 'application/json'}

        if auth_required and self.auth_token:
            headers['Authorization'] = f'Bearer {self.auth_token}'

        if files:
            # Remove Content-Type for file uploads
            headers.pop('Content-Type', None)

        start_time = time.time()

        try:
            if method.upper() == 'GET':
                response = self.session.get(url, headers=headers, params=params)
            elif method.upper() == 'POST':
                if files:
                    response = self.session.post(url, headers=headers, data=data, files=files)
                else:
                    response = self.session.post(url, headers=headers, json=data)
            elif method.upper() == 'PUT':
                if files:
                    response = self.session.put(url, headers=headers, data=data, files=files)
                else:
                    response = self.session.put(url, headers=headers, json=data)
            elif method.upper() == 'DELETE':
                response = self.session.delete(url, headers=headers)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            response_time = time.time() - start_time

            # Try to parse JSON response
            try:
                response_data = response.json()
            except (ValueError, json.JSONDecodeError):
                response_data = {"raw_response": response.text}

            # Consider 404 as success for certain test cases (testing non-existent resources)
            success = 200 <= response.status_code < 300
            error_message = None if success else response_data.get('message', response.text)

            # Special handling for expected 404s (testing non-existent resources)
            if response.status_code == 404 and '/99999' in endpoint:
                success = True  # This is an expected 404 for testing non-existent resources
                error_message = None

            result = TestResult(
                endpoint=endpoint,
                method=method.upper(),
                status_code=response.status_code,
                success=success,
                response_time=response_time,
                error_message=error_message,
                response_data=response_data
            )

            self.test_results.append(result)

            # Log the result
            status = "✅ PASS" if success else "❌ FAIL"
            self.logger.info(f"{status} {method.upper()} {endpoint} - {response.status_code} ({response_time:.3f}s)")

            if not success:
                self.logger.error(f"Error: {error_message}")

            return result

        except Exception as e:
            response_time = time.time() - start_time
            error_message = str(e)

            result = TestResult(
                endpoint=endpoint,
                method=method.upper(),
                status_code=0,
                success=False,
                response_time=response_time,
                error_message=error_message
            )

            self.test_results.append(result)
            self.logger.error(f"❌ FAIL {method.upper()} {endpoint} - Exception: {error_message}")

            return result

    def test_health_check(self):
        """Test health check endpoint"""
        self.logger.info("🏥 Testing Health Check...")
        return self.make_request('GET', '/health', auth_required=False)

    def test_api_info(self):
        """Test API info endpoint"""
        self.logger.info("ℹ️ Testing API Info...")
        return self.make_request('GET', '/api', auth_required=False)

    def test_authentication(self):
        """Test authentication flow with interactive OTP input"""
        self.logger.info("🔐 Testing Authentication...")

        # Check if we already have a valid token
        if self.check_authentication_status():
            self.logger.info("✅ Using existing valid authentication token")
            print("✅ Using existing valid authentication token")
            return True

        # Test send OTP
        self.logger.info(f"📧 Sending OTP to {TEST_EMAIL}...")
        otp_result = self.make_request('POST', '/api/auth/send-otp',
                                     data={'email': TEST_EMAIL},
                                     auth_required=False)

        if not otp_result.success:
            self.logger.error("Failed to send OTP, skipping auth tests")
            return False

        self.logger.info("✅ OTP sent successfully!")

        # Interactive OTP input
        print(f"\n📧 An OTP has been sent to {TEST_EMAIL}")
        print("📱 Please check your email and enter the OTP below:")

        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                otp = input(f"🔑 Enter OTP (Attempt {attempt + 1}/{max_attempts}): ").strip()

                if not otp:
                    print("❌ OTP cannot be empty. Please try again.")
                    continue

                if not otp.isdigit() or len(otp) != 6:
                    print("❌ OTP must be a 6-digit number. Please try again.")
                    continue

                # Test verify OTP
                self.logger.info(f"🔍 Verifying OTP: {otp}")
                verify_result = self.make_request('POST', '/api/auth/verify-otp',
                                                data={'email': TEST_EMAIL, 'otp': otp},
                                                auth_required=False)

                if verify_result.success and verify_result.response_data:
                    # Extract token from response
                    token = verify_result.response_data.get('data', {}).get('token')
                    if token:
                        self.auth_token = token
                        self.save_auth_token()  # Save token for future use
                        self.logger.info("✅ Authentication successful! Token stored.")
                        print("✅ Authentication successful!")

                        # Test other auth endpoints with token
                        self.make_request('GET', '/api/auth/profile', auth_required=True)

                        return True
                    else:
                        self.logger.error("Token not found in response")
                        print("❌ Token not found in response")
                else:
                    error_msg = verify_result.error_message or "Unknown error"
                    self.logger.error(f"OTP verification failed: {error_msg}")
                    print(f"❌ OTP verification failed: {error_msg}")

                    if attempt < max_attempts - 1:
                        print("🔄 Please try again with a new OTP.")

            except KeyboardInterrupt:
                print("\n⚠️ Authentication cancelled by user.")
                self.logger.warning("Authentication cancelled by user")
                return False
            except Exception as e:
                self.logger.error(f"Error during OTP input: {e}")
                print(f"❌ Error: {e}")

        print("❌ Maximum OTP attempts exceeded. Continuing without authentication.")
        self.logger.error("Maximum OTP attempts exceeded")
        return False

    def test_clients(self):
        """Test client management endpoints"""
        self.logger.info("👥 Testing Client Management...")

        # Test GET clients
        self.make_request('GET', '/api/clients')
        self.make_request('GET', '/api/clients/stats')
        self.make_request('GET', '/api/clients/dropdown')

        # Test CREATE client
        client_data = {
            'name': 'Test Client',
            'company': 'Test Company',
            'number': '+1234567890',
            'email': '<EMAIL>'
        }

        create_result = self.make_request('POST', '/api/clients', data=client_data)

        if create_result.success and create_result.response_data:
            client_id = create_result.response_data.get('data', {}).get('id')
            if client_id:
                self.created_resources['clients'].append(client_id)

                # Test GET specific client
                self.make_request('GET', f'/api/clients/{client_id}')
                self.make_request('GET', f'/api/clients/{client_id}/data')

                # Test UPDATE client
                update_data = {
                    'name': 'Updated Test Client',
                    'company': 'Updated Test Company',
                    'number': '+1234567891',
                    'email': '<EMAIL>'
                }
                self.make_request('PUT', f'/api/clients/{client_id}', data=update_data)

        # Test invalid client ID (expect 404 - this is correct behavior)
        # Note: This test intentionally expects a 404 response for non-existent client
        invalid_client_result = self.make_request('GET', '/api/clients/99999')
        if invalid_client_result.status_code == 404:
            self.logger.info("✅ Invalid client ID test passed - correctly returned 404")
        else:
            self.logger.warning(f"⚠️ Invalid client ID test unexpected result: {invalid_client_result.status_code}")

    def test_outclients(self):
        """Test outclient management endpoints"""
        self.logger.info("🏢 Testing Outclient Management...")

        # Test GET outclients
        self.make_request('GET', '/api/outclients')
        self.make_request('GET', '/api/outclients/stats')
        self.make_request('GET', '/api/outclients/dropdown')

        # Test CREATE outclient
        outclient_data = {
            'name': 'Test Outclient',
            'company': 'Test Outsourcing Company',
            'number': '+1234567890',
            'email': '<EMAIL>'
        }

        create_result = self.make_request('POST', '/api/outclients', data=outclient_data)

        if create_result.success and create_result.response_data:
            outclient_id = create_result.response_data.get('data', {}).get('id')
            if outclient_id:
                self.created_resources['outclients'].append(outclient_id)

                # Test GET specific outclient
                self.make_request('GET', f'/api/outclients/{outclient_id}')

                # Test UPDATE outclient
                update_data = {
                    'name': 'Updated Test Outclient',
                    'company': 'Updated Outsourcing Company',
                    'number': '+1234567891'
                }
                self.make_request('PUT', f'/api/outclients/{outclient_id}', data=update_data)

    def test_projects(self):
        """Test project management endpoints"""
        self.logger.info("📋 Testing Project Management...")

        # Test GET projects
        self.make_request('GET', '/api/projects')
        self.make_request('GET', '/api/projects/stats')
        self.make_request('GET', '/api/projects/codes')

        # Test CREATE project - use a valid client ID from created clients or skip if none exist
        client_id = None
        if self.created_resources['clients']:
            client_id = self.created_resources['clients'][0]
        else:
            # Try to get an existing client from the system
            clients_result = self.make_request('GET', '/api/clients?limit=1')
            if clients_result.success and clients_result.response_data:
                clients_data = clients_result.response_data.get('data', [])
                if clients_data and len(clients_data) > 0:
                    client_id = clients_data[0].get('id')

        if client_id:
            project_data = {
                'name': 'Test Project',
                'company': 'Test Company',
                'type': 'Photography',
                'status': 'ACTIVE',
                'amount': 5000,
                'location': 'Test Location',
                'clientId': client_id
            }
        else:
            # Skip project creation if no valid client exists
            self.logger.warning("No valid client found, skipping project creation test")
            return

        create_result = self.make_request('POST', '/api/projects', data=project_data)

        if create_result.success and create_result.response_data:
            project_data_response = create_result.response_data.get('data', {})
            project_id = project_data_response.get('id')
            project_code = project_data_response.get('code')

            if project_id:
                self.created_resources['projects'].append(project_id)

                # Test GET specific project
                self.make_request('GET', f'/api/projects/{project_id}')

                if project_code:
                    self.make_request('GET', f'/api/projects/code/{project_code}')
                    self.make_request('GET', f'/api/projects/{project_code}/data')

                # Test UPDATE project
                update_data = {
                    'name': 'Updated Test Project',
                    'status': 'COMPLETED',
                    'amount': 6000
                }
                self.make_request('PUT', f'/api/projects/{project_id}', data=update_data)

                # Test UPDATE project status
                status_data = {'status': 'ON_HOLD'}
                self.make_request('PUT', f'/api/projects/{project_id}/status', data=status_data)

    def test_financial(self):
        """Test financial management endpoints"""
        self.logger.info("💰 Testing Financial Management...")

        # Test income endpoints
        self.make_request('GET', '/api/financial/income')

        # Test CREATE income
        income_data = {
            'date': datetime.now().strftime('%Y-%m-%d'),
            'description': 'Test Income Entry',
            'amount': 1000,
            'note': 'Test income note',
            'projectIncome': False
        }

        income_result = self.make_request('POST', '/api/financial/income', data=income_data)

        if income_result.success and income_result.response_data:
            income_id = income_result.response_data.get('data', {}).get('id')
            if income_id:
                self.created_resources['income'].append(income_id)

                # Test GET specific income
                self.make_request('GET', f'/api/financial/income/{income_id}')

                # Test UPDATE income
                update_data = {
                    'description': 'Updated Test Income',
                    'amount': 1200
                }
                self.make_request('PUT', f'/api/financial/income/{income_id}', data=update_data)

        # Test expense endpoints
        self.make_request('GET', '/api/financial/expenses')

        # Test utility endpoints (fixed backend route ordering)
        self.make_request('GET', '/api/financial/expenses/categories')
        self.make_request('GET', '/api/financial/expenses/totals')

        # Test CREATE expense
        expense_data = {
            'date': datetime.now().strftime('%Y-%m-%d'),
            'category': 'Equipment',
            'description': 'Test Expense Entry',
            'amount': 500,
            'notes': 'Test expense notes',
            'projectExpense': False
        }

        expense_result = self.make_request('POST', '/api/financial/expenses', data=expense_data)

        if expense_result.success and expense_result.response_data:
            expense_id = expense_result.response_data.get('data', {}).get('id')
            if expense_id:
                self.created_resources['expenses'].append(expense_id)

                # Test GET specific expense
                self.make_request('GET', f'/api/financial/expenses/{expense_id}')

                # Test UPDATE expense
                update_data = {
                    'description': 'Updated Test Expense',
                    'amount': 600
                }
                self.make_request('PUT', f'/api/financial/expenses/{expense_id}', data=update_data)

        # Test financial summary endpoints
        self.make_request('GET', '/api/financial/summary')
        self.make_request('GET', '/api/financial/budget')

        # Test new financial chart endpoints
        self.make_request('GET', '/api/financial/income/chart-data')
        self.make_request('GET', '/api/financial/income/chart-data?period=12months')
        self.make_request('GET', '/api/financial/expenses/categorized')
        self.make_request('GET', '/api/financial/expenses/categorized?period=12months')

    def test_assets(self):
        """Test asset management endpoints"""
        self.logger.info("🏭 Testing Asset Management...")

        # Test GET assets
        self.make_request('GET', '/api/assets')
        self.make_request('GET', '/api/assets/stats')
        self.make_request('GET', '/api/assets/types')

        # Test CREATE asset
        asset_data = {
            'date': datetime.now().strftime('%Y-%m-%d'),
            'type': 'Camera',
            'name': 'Test Camera',
            'quantity': 1,
            'buyPrice': 2000,
            'value': 2000,
            'note': 'Test asset note'
        }

        asset_result = self.make_request('POST', '/api/assets', data=asset_data)

        if asset_result.success and asset_result.response_data:
            asset_id = asset_result.response_data.get('data', {}).get('id')
            if asset_id:
                self.created_resources['assets'].append(asset_id)

                # Test GET specific asset
                self.make_request('GET', f'/api/assets/{asset_id}')

                # Test UPDATE asset
                update_data = {
                    'name': 'Updated Test Camera',
                    'value': 1800
                }
                self.make_request('PUT', f'/api/assets/{asset_id}', data=update_data)

    def test_entertainment(self):
        """Test entertainment management endpoints"""
        self.logger.info("🎬 Testing Entertainment Management...")

        # Test GET entertainment
        self.make_request('GET', '/api/entertainment')
        self.make_request('GET', '/api/entertainment/stats')
        self.make_request('GET', '/api/entertainment/types')
        self.make_request('GET', '/api/entertainment/languages')

        # Test CREATE entertainment
        entertainment_data = {
            'type': 'Movie',
            'language': 'English',
            'rating': 8,
            'name': 'Test Movie',
            'source': 'Netflix'
        }

        entertainment_result = self.make_request('POST', '/api/entertainment', data=entertainment_data)

        if entertainment_result.success and entertainment_result.response_data:
            entertainment_id = entertainment_result.response_data.get('data', {}).get('id')
            if entertainment_id:
                self.created_resources['entertainment'].append(entertainment_id)

                # Test GET specific entertainment
                self.make_request('GET', f'/api/entertainment/{entertainment_id}')

                # Test UPDATE entertainment
                update_data = {
                    'name': 'Updated Test Movie',
                    'rating': 9
                }
                self.make_request('PUT', f'/api/entertainment/{entertainment_id}', data=update_data)

    def test_calendar(self):
        """Test calendar management endpoints"""
        self.logger.info("📅 Testing Calendar Management...")

        # Test GET calendar events
        self.make_request('GET', '/api/calendar/events')
        self.make_request('GET', '/api/calendar/events/upcoming')
        self.make_request('GET', '/api/calendar/events/today')
        self.make_request('GET', '/api/calendar/events/week')
        self.make_request('GET', '/api/calendar/events/month')
        self.make_request('GET', '/api/calendar/events/stats')

        # Test CREATE calendar event
        start_time = datetime.now() + timedelta(hours=1)
        end_time = start_time + timedelta(hours=2)

        event_data = {
            'title': 'Test Event',
            'startTime': start_time.isoformat(),
            'endTime': end_time.isoformat()
        }

        event_result = self.make_request('POST', '/api/calendar/events', data=event_data)

        if event_result.success and event_result.response_data:
            event_id = event_result.response_data.get('data', {}).get('id')
            if event_id:
                self.created_resources['calendar_events'].append(event_id)

                # Test GET specific event
                self.make_request('GET', f'/api/calendar/events/{event_id}')

                # Test UPDATE event
                update_data = {
                    'title': 'Updated Test Event'
                }
                self.make_request('PUT', f'/api/calendar/events/{event_id}', data=update_data)

        # Test date range query
        start_date = datetime.now().strftime('%Y-%m-%d')
        end_date = (datetime.now() + timedelta(days=7)).strftime('%Y-%m-%d')
        self.make_request('GET', f'/api/calendar/events/range?startDate={start_date}&endDate={end_date}')

    def test_maps(self):
        """Test maps integration endpoints"""
        self.logger.info("🗺️ Testing Maps Integration...")

        # Test geocoding
        geocode_data = {'address': 'New York, NY, USA'}
        self.make_request('POST', '/api/maps/geocode', data=geocode_data)

        # Test reverse geocoding (fix parameter names: lat/lng -> latitude/longitude)
        reverse_data = {'latitude': 40.7128, 'longitude': -74.0060}
        self.make_request('POST', '/api/maps/reverse-geocode', data=reverse_data)

        # Test detailed geocoding
        self.make_request('POST', '/api/maps/detailed-geocode', data=geocode_data)

        # Test nearby places (fix parameter names: lat/lng -> latitude/longitude)
        nearby_data = {
            'latitude': 40.7128,
            'longitude': -74.0060,
            'radius': 1000,
            'type': 'restaurant'
        }
        self.make_request('POST', '/api/maps/nearby-places', data=nearby_data)

        # Test distance calculation (fix parameter names: lat/lng -> latitude/longitude)
        distance_data = {
            'origin': {'latitude': 40.7128, 'longitude': -74.0060},
            'destination': {'latitude': 40.7589, 'longitude': -73.9851}
        }
        self.make_request('POST', '/api/maps/distance', data=distance_data)

        # Test static map (fix query parameter names: lat/lng -> latitude/longitude)
        self.make_request('GET', '/api/maps/static-map?latitude=40.7128&longitude=-74.0060&zoom=12')

        # Test directions
        self.make_request('POST', '/api/maps/directions', data=distance_data)

        # Test coordinate validation (fix parameter names: lat/lng -> latitude/longitude)
        validate_data = {'latitude': 40.7128, 'longitude': -74.0060}
        self.make_request('POST', '/api/maps/validate-coordinates', data=validate_data)

    def test_dashboard(self):
        """Test dashboard endpoints"""
        self.logger.info("📊 Testing Dashboard...")

        # Test dashboard stats
        self.make_request('GET', '/api/dashboard/stats')

        # Test financial summary
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        end_date = datetime.now().strftime('%Y-%m-%d')
        self.make_request('GET', f'/api/dashboard/financial-summary?startDate={start_date}&endDate={end_date}')

        # Test new dashboard endpoints
        self.make_request('GET', '/api/dashboard/today-schedule')
        self.make_request('GET', '/api/dashboard/charts/income-expense')
        self.make_request('GET', '/api/dashboard/charts/income-expense?period=12months')
        self.make_request('GET', '/api/dashboard/charts/project-details')
        self.make_request('GET', '/api/dashboard/charts/expense-breakdown')
        self.make_request('GET', '/api/dashboard/charts/expense-breakdown?period=12months')

    def test_budget(self):
        """Test budget management endpoints"""
        self.logger.info("💰 Testing Budget Management...")

        # Test budget overview
        self.make_request('GET', '/api/budget/overview')

        # Test budget categories
        self.make_request('GET', '/api/budget/categories')

        # Test investment details
        self.make_request('GET', '/api/budget/investment-details')

        # Test CREATE budget category
        category_data = {
            'name': 'Test Budget Category',
            'percentage': 25,
            'color': '#4CAF50',
            'description': 'Test budget category description'
        }

        category_result = self.make_request('POST', '/api/budget/categories', data=category_data)

        if category_result.success and category_result.response_data:
            category_id = category_result.response_data.get('data', {}).get('id')
            if category_id:
                # Test UPDATE budget category
                update_data = {
                    'name': 'Updated Test Budget Category',
                    'percentage': 30,
                    'color': '#2196F3'
                }
                self.make_request('PUT', f'/api/budget/categories/{category_id}', data=update_data)

                # Test DELETE budget category
                self.make_request('DELETE', f'/api/budget/categories/{category_id}')

    def test_payments(self):
        """Test payment management endpoints"""
        self.logger.info("💳 Testing Payment Management...")

        # Test payment stats
        self.make_request('GET', '/api/payments/stats')

        # Test get all payments
        self.make_request('GET', '/api/payments')
        self.make_request('GET', '/api/payments?page=1&limit=5')

        # Test payments by status
        self.make_request('GET', '/api/payments/status/pending')
        self.make_request('GET', '/api/payments/status/ongoing')
        self.make_request('GET', '/api/payments/status/completed')

        # Test CREATE payment - use a valid client ID from created clients or skip if none exist
        client_id = None
        if self.created_resources['clients']:
            client_id = self.created_resources['clients'][0]
        else:
            # Try to get an existing client from the system
            clients_result = self.make_request('GET', '/api/clients?limit=1')
            if clients_result.success and clients_result.response_data:
                clients_data = clients_result.response_data.get('data', [])
                if clients_data and len(clients_data) > 0:
                    client_id = clients_data[0].get('id')

        if client_id:
            payment_data = {
                'clientId': client_id,
                'amount': 2500,
                'description': 'Test Payment',
                'dueDate': (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
            }

            payment_result = self.make_request('POST', '/api/payments', data=payment_data)

            if payment_result.success and payment_result.response_data:
                payment_id = payment_result.response_data.get('data', {}).get('id')
                if payment_id:
                    # Test GET specific payment
                    self.make_request('GET', f'/api/payments/{payment_id}')

                    # Test UPDATE payment
                    update_data = {
                        'amount': 3000,
                        'description': 'Updated Test Payment'
                    }
                    self.make_request('PUT', f'/api/payments/{payment_id}', data=update_data)

                    # Test UPDATE payment status
                    status_data = {'status': 'completed'}
                    self.make_request('PUT', f'/api/payments/{payment_id}/status', data=status_data)

                    # Test DELETE payment
                    self.make_request('DELETE', f'/api/payments/{payment_id}')
        else:
            self.logger.warning("No valid client found, skipping payment creation test")

    def test_projects_enhanced(self):
        """Test enhanced project endpoints"""
        self.logger.info("📋 Testing Enhanced Project Management...")

        # Test projects by status
        self.make_request('GET', '/api/projects/status/pending')
        self.make_request('GET', '/api/projects/status/ongoing')
        self.make_request('GET', '/api/projects/status/completed')

    def cleanup_test_data(self):
        """Clean up created test data"""
        self.logger.info("🧹 Cleaning up test data...")

        # Delete in reverse order to handle dependencies
        cleanup_order = [
            ('calendar_events', '/api/calendar/events'),
            ('entertainment', '/api/entertainment'),
            ('assets', '/api/assets'),
            ('expenses', '/api/financial/expenses'),
            ('income', '/api/financial/income'),
            ('projects', '/api/projects'),
            ('outclients', '/api/outclients'),
            ('clients', '/api/clients')
        ]

        for resource_type, endpoint_base in cleanup_order:
            for resource_id in self.created_resources[resource_type]:
                self.make_request('DELETE', f'{endpoint_base}/{resource_id}')

    def generate_summary_report(self):
        """Generate test summary report"""
        try:
            total_tests = len(self.test_results)
            passed_tests = sum(1 for result in self.test_results if result.success)
            failed_tests = total_tests - passed_tests
            success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0

            # Group results by endpoint category
            categories = {}
            for result in self.test_results:
                category = result.endpoint.split('/')[2] if len(result.endpoint.split('/')) > 2 else 'root'
                if category not in categories:
                    categories[category] = {'total': 0, 'passed': 0, 'failed': 0}

                categories[category]['total'] += 1
                if result.success:
                    categories[category]['passed'] += 1
                else:
                    categories[category]['failed'] += 1

            # Generate summary
            summary = f"""
{'='*80}
🧪 CYMATICS API TEST SUITE SUMMARY
{'='*80}

📊 OVERALL RESULTS:
   Total Tests: {total_tests}
   ✅ Passed: {passed_tests}
   ❌ Failed: {failed_tests}
   📈 Success Rate: {success_rate:.1f}%

📋 RESULTS BY CATEGORY:
"""

            for category, stats in categories.items():
                category_success_rate = (stats['passed'] / stats['total'] * 100) if stats['total'] > 0 else 0
                summary += f"   {category.upper()}: {stats['passed']}/{stats['total']} ({category_success_rate:.1f}%)\n"

            # Add failed tests details
            failed_results = [r for r in self.test_results if not r.success]
            if failed_results:
                summary += f"\n❌ FAILED TESTS DETAILS:\n"
                for result in failed_results:
                    summary += f"   {result.method} {result.endpoint}\n"
                    summary += f"      Status: {result.status_code}\n"
                    summary += f"      Error: {result.error_message}\n"
                    summary += f"      Response Time: {result.response_time:.3f}s\n\n"

            # Add performance stats
            avg_response_time = sum(r.response_time for r in self.test_results) / total_tests if total_tests > 0 else 0
            max_response_time = max((r.response_time for r in self.test_results), default=0)
            min_response_time = min((r.response_time for r in self.test_results), default=0)

            summary += f"""
⚡ PERFORMANCE STATS:
   Average Response Time: {avg_response_time:.3f}s
   Fastest Response: {min_response_time:.3f}s
   Slowest Response: {max_response_time:.3f}s

📝 Log File: {self.log_filename}
{'='*80}
"""

            # Write summary to file
            summary_filename = f"{LOGS_DIR}/test_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(summary_filename, 'w', encoding='utf-8') as f:
                f.write(summary)

            # Print summary
            print(summary)
            self.logger.info(f"Summary report saved to: {summary_filename}")

            return {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': failed_tests,
                'success_rate': success_rate,
                'categories': categories,
                'summary_file': summary_filename
            }

        except Exception as e:
            self.logger.error(f"Failed to generate summary report: {e}")
            # Return a basic summary even if detailed report generation fails
            total_tests = len(self.test_results) if self.test_results else 0
            passed_tests = sum(1 for result in self.test_results if result.success) if self.test_results else 0
            failed_tests = total_tests - passed_tests
            success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0

            return {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': failed_tests,
                'success_rate': success_rate,
                'categories': {},
                'summary_file': None
            }

    def load_auth_token(self):
        """Load authentication token from file if it exists"""
        try:
            if os.path.exists(TOKEN_FILE):
                with open(TOKEN_FILE, 'r') as f:
                    token_data = json.load(f)
                    self.auth_token = token_data.get('token')
                    if self.auth_token:
                        self.logger.info("Loaded existing auth token from file")
        except Exception as e:
            self.logger.warning(f"Failed to load auth token: {e}")
            self.auth_token = None

    def save_auth_token(self):
        """Save authentication token to file for reuse"""
        try:
            if self.auth_token:
                token_data = {
                    'token': self.auth_token,
                    'email': TEST_EMAIL,
                    'timestamp': datetime.now().isoformat()
                }
                with open(TOKEN_FILE, 'w') as f:
                    json.dump(token_data, f, indent=2)
                self.logger.info("Auth token saved to file")
        except Exception as e:
            self.logger.warning(f"Failed to save auth token: {e}")

    def check_authentication_status(self):
        """Check if we have a valid authentication token"""
        if self.auth_token:
            # Test if token is still valid
            test_result = self.make_request('GET', '/api/auth/profile', auth_required=True)
            if test_result.success:
                return True
            else:
                # Token is invalid, remove it
                self.auth_token = None
                if os.path.exists(TOKEN_FILE):
                    os.remove(TOKEN_FILE)
                self.logger.info("Invalid token removed")
        return False

    def run_all_tests(self):
        """Run all API tests"""
        self.logger.info("🚀 Starting Cymatics API Test Suite...")
        print("\n🧪 Cymatics API Test Suite")
        print("=" * 50)
        start_time = time.time()

        try:
            # Test basic endpoints first
            print("\n📡 Testing basic endpoints...")
            self.test_health_check()
            self.test_api_info()

            # Test authentication with interactive OTP input
            print("\n🔐 Setting up authentication...")
            auth_success = self.test_authentication()

            if not auth_success:
                self.logger.warning("⚠️ Authentication failed - some tests may fail with 401 errors")
                print("\n⚠️ Warning: Authentication failed. Protected endpoints will return 401 errors.")
                print("💡 You can still run the tests to see endpoint availability.")

                # Ask user if they want to continue
                try:
                    continue_choice = input("\n🤔 Continue with tests anyway? (y/n): ").strip().lower()
                    if continue_choice not in ['y', 'yes']:
                        self.logger.info("Test suite cancelled by user")
                        return self.generate_summary_report()
                except KeyboardInterrupt:
                    self.logger.info("Test suite cancelled by user")
                    return self.generate_summary_report()
            else:
                self.logger.info("🎉 Authentication successful! All endpoints will be tested with proper authorization.")
                print("🎉 Authentication successful! Running full test suite...")

            print("\n🚀 Running API endpoint tests...")

            # Test all endpoints
            self.test_clients()
            self.test_outclients()
            self.test_projects()
            self.test_projects_enhanced()
            self.test_financial()
            self.test_budget()
            self.test_payments()
            self.test_assets()
            self.test_entertainment()
            self.test_calendar()
            self.test_maps()
            self.test_dashboard()

            # Clean up test data
            self.cleanup_test_data()

        except Exception as e:
            self.logger.error(f"Test suite failed with exception: {e}")

        finally:
            total_time = time.time() - start_time
            self.logger.info(f"🏁 Test suite completed in {total_time:.2f} seconds")

            # Generate and return summary
            return self.generate_summary_report()

def main():
    """Main function to run the test suite"""
    print("🧪 Cymatics API Test Suite")
    print("=" * 50)
    print(f"📧 Test Email: {TEST_EMAIL}")
    print(f"🌐 Server URL: {BASE_URL}")
    print("=" * 50)

    # Check if server is running
    print("\n🔍 Checking server status...")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code != 200:
            print(f"❌ Server health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to server at {BASE_URL}")
        print(f"   Error: {e}")
        print("   Please ensure the Node.js backend is running on port 3000")
        return False

    print(f"✅ Server is running at {BASE_URL}")

    # Run tests
    tester = CymaticsAPITester()
    summary = tester.run_all_tests()

    # Check if summary was generated successfully
    if not summary:
        print("❌ Failed to generate test summary")
        return False

    # Print final results
    print(f"\n🎯 Final Results:")
    print(f"   Success Rate: {summary['success_rate']:.1f}%")
    print(f"   Total Tests: {summary['total_tests']}")
    print(f"   Passed: {summary['passed_tests']}")
    print(f"   Failed: {summary['failed_tests']}")

    # Show authentication status
    auth_status = "✅ Authenticated" if tester.auth_token else "❌ Not Authenticated"
    print(f"   Auth Status: {auth_status}")

    if summary['failed_tests'] > 0:
        print(f"\n📄 Check {summary['summary_file']} for detailed failure analysis")

        # If not authenticated, provide helpful message
        if not tester.auth_token:
            print("\n💡 Tip: Most failures are likely due to missing authentication.")
            print("   Run the test again and complete the OTP verification for better results.")

    return summary['success_rate'] >= 80  # Return True if success rate is 80% or higher

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
