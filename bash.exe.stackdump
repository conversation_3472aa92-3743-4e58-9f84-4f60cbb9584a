Stack trace:
Frame         Function      Args
0007FFFF8E80  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF7D80) msys-2.0.dll+0x1FE8E
0007FFFF8E80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9158) msys-2.0.dll+0x67F9
0007FFFF8E80  000210046832 (000210286019, 0007FFFF8D38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF8E80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF8E80  000210068E24 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9160  00021006A225 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF91E3A0000 ntdll.dll
7FF91D130000 KERNEL32.DLL
7FF91B940000 KERNELBASE.dll
7FF91D930000 USER32.dll
7FF91BDC0000 win32u.dll
000210040000 msys-2.0.dll
7FF91DFA0000 GDI32.dll
7FF91B800000 gdi32full.dll
7FF91BD10000 msvcp_win.dll
7FF91C010000 ucrtbase.dll
7FF91C910000 advapi32.dll
7FF91CBD0000 msvcrt.dll
7FF91C770000 sechost.dll
7FF91DE80000 RPCRT4.dll
7FF91A420000 CRYPTBASE.DLL
7FF91BF70000 bcryptPrimitives.dll
7FF91E1B0000 IMM32.DLL
