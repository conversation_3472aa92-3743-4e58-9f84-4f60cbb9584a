# Database Population Script for Cymatics Pro

This script populates your Cymatics Pro database with realistic sample data to help you see how the website looks and functions with actual content.

## What Data is Created

The script creates sample data for all database tables:

### 📊 **Data Summary**
- **4 Users** - Admin and staff users with different roles
- **10 Clients** - Photography/videography clients with companies
- **5 Outsourcing Clients** - External service providers
- **25 Projects** - Various photography/videography projects with realistic details
- **~75 Income Entries** - Project payments and general income
- **~155 Expense Entries** - Project expenses and business costs
- **15 Assets** - Professional equipment and gear
- **15 Entertainment** - Movies, TV shows, documentaries
- **20 Calendar Events** - Meetings, shoots, and appointments
- **~9 Email OTPs** - Authentication records

### 🎯 **Project Types Included**
- Wedding Photography
- Corporate Video
- Product Photography
- Event Coverage
- Portrait Sessions
- Commercial Video
- Real Estate Photography
- Music Videos
- Documentaries
- Fashion Shoots

### 💰 **Financial Data**
- Project amounts: $1,000 - $15,000
- Income entries with partial payments
- Expense categories: Equipment, Travel, Software, etc.
- Outsourcing costs and payments
- Realistic profit calculations

### 📍 **Geographic Data**
- Projects across 10 major US cities
- Real latitude/longitude coordinates
- Realistic addresses for location shoots

## How to Run

### Prerequisites
1. Make sure your PostgreSQL database is running on port 5433
2. Ensure Django environment is properly set up
3. All migrations should be applied

### Running the Script

```bash
# Navigate to the Django project directory
cd "cymatics django"

# Run the population script
python populate_db.py
```

### Expected Output
```
🚀 Starting database population...
==================================================
Clearing existing data...
✓ Existing data cleared
Creating users...
✓ Created 4 users
Creating clients...
✓ Created 10 clients
Creating outsourcing clients...
✓ Created 5 outsourcing clients
Creating projects...
✓ Created 25 projects
Creating income entries...
✓ Created 75 income entries
Creating expense entries...
✓ Created 155 expense entries
Creating assets...
✓ Created 15 assets
Creating entertainment entries...
✓ Created 15 entertainment entries
Creating calendar events...
✓ Created 20 calendar events
Creating email OTP entries...
✓ Created 9 email OTP entries
==================================================
✅ Database population completed successfully!
📊 Summary:
   • Users: 4
   • Clients: 10
   • Outsourcing Clients: 5
   • Projects: 25
   • Income Entries: 75
   • Expense Entries: 155
   • Assets: 15
   • Entertainment: 15
   • Calendar Events: 20
   • Email OTPs: 9
==================================================
🎉 Your Cymatics Pro database is now populated with sample data!
💡 You can now run the Django server to see the website with data.
```

## After Running the Script

1. **Start the Django server:**
   ```bash
   python manage.py runserver
   ```

2. **Login credentials:**
   - Username: `admin`
   - Password: `password123`

3. **Explore the populated data:**
   - Dashboard with financial summaries
   - Client management with realistic companies
   - Project tracking with locations and status
   - Income and expense management
   - Asset inventory
   - Entertainment tracking
   - Calendar events

## Features You Can Now Test

✅ **Dashboard Analytics** - View income/expense charts and project summaries
✅ **Client Management** - Browse clients with contact information
✅ **Project Tracking** - See projects with locations, dates, and financial data
✅ **Financial Management** - Review income and expenses with project associations
✅ **Asset Management** - View professional equipment inventory
✅ **Map Integration** - See project locations on maps
✅ **Search & Filtering** - Test search functionality across all modules
✅ **Calendar Integration** - View scheduled events and appointments

## Important Notes

⚠️ **Data Safety**: The script clears existing data before populating. Make sure to backup any important data first.

🔄 **Re-running**: You can run the script multiple times. It will clear and recreate all sample data.

🎲 **Randomization**: Some data is randomized (dates, amounts, assignments) so each run creates slightly different data.

## Troubleshooting

If you encounter any issues:

1. **Database Connection**: Ensure PostgreSQL is running on port 5433
2. **Permissions**: Make sure the database user has proper permissions
3. **Dependencies**: Verify all Django models are properly migrated
4. **Python Path**: Run the script from the Django project root directory

## Next Steps

After populating the database:
1. Explore all the website features
2. Test the search and filtering functionality
3. Review the financial reports and charts
4. Check the map integration with project locations
5. Test the CRUD operations on all modules

Enjoy exploring your fully populated Cymatics Pro application! 🎉
