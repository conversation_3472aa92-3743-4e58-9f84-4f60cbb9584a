 /* General Styles */
 * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
    display: flex;
    height: 100vh;
    overflow: hidden;
}

.container {
    display: flex;
    width: 100%;
    height: 100%;
}

.sidebar {
    background-color: #1e1e1e;
    color: white;
    width: 250px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    overflow-y: auto; /* Allow vertical scrolling */
    scrollbar-width: none; /* For Firefox */
}

/* Hide scrollbar for Chrome, Safari and Opera */
.sidebar::-webkit-scrollbar {
    display: none;
}

.sidebar .logo {
    padding: 20px;
    text-align: center;
}

.menu-title {
    padding: 10px 0;
    text-align: center;
}

.sidebar nav ul {
    list-style: none;
    padding: 0;
    width: 100%;
}

.sidebar nav ul li {
    padding: 12px 20px;
    cursor: pointer;
    transition: background-color 0.3s, color 0.3s, border-left 0.3s;
    text-align: left;
    display: flex;
    align-items: center;
}

.sidebar nav ul li:hover {
    background-color: #333;
    color: #fff;
    border-left: 4px solid #fff;
}

.menu-icon {
    margin-right: 10px;
    width: 24px;
    height: 24px;
}

.main-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    height: 100%;
}

.search-filter {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.search-add input[type="text"] {
    padding: 10px;
    font-size: 14px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.search-add .Filter {
    padding: 10px 20px;
    background-color: #000000; /* Black background */
    color: #fff; /* White text */
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-left: 10px;
}

.search .Filter:hover {
    background-color: #494949;
}

.table-container {
    background-color: #fff;
    padding: 20px;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

table th, table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
    position: relative;
}

table th {
    background-color: #f4f4f4;
    font-weight: bold;
}

table tr:hover {
    background-color: #f1f1f1;
}

.editable {
    cursor: pointer;
    padding: 8px;
    width: 100%;
    box-sizing: border-box;
}

.edit-input {
    width: 100%; /* Full width for better visibility */
    border: 1px solid #ddd;
    padding: 8px;
    font-size: 14px;
    box-sizing: border-box;
    transition: transform 0.2s ease, box-shadow 0.2s ease; /* Transition for pop-up effect */
    transform: translateY(0); /* Initial position */
}

.edit-input.focused {
    transform: translateY(-5px) scale(1.05); /* Move up slightly and scale up */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Add shadow for pop-up effect */
}

.edit-input:focus {
    outline: none;
    background-color: #e6f7ff;
}

/* Filter Dropdown */
.filter-dropdown {
    background-color: white;
    border: 1px solid #ddd;
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    position: absolute;
    top: 60px; /* Adjust to position directly below the filter button */
    right: 20px; /* Adjust as per your layout */
    width: 260px;
    padding: 10px;
    display: none; /* Initially hidden */
}

.filter-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f1f1f1;
    cursor: pointer; /* Change cursor to pointer */
}

.filter-item:last-child {
    border-bottom: none;
}

.arrow-icon {
    width: 10px;
    height: 10px;
    transition: transform 0.3s; /* Smooth transition for arrow rotation */
}

.amount-dropdown,
.type-dropdown,
.status-dropdown {
    display: none; /* Hidden by default */
    position: absolute;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 10px;
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
    width: 260px; /* Match filter dropdown width */
    padding: 10px;
    z-index: 10; /* Ensure it's above other elements */
    overflow-y: auto; /* Enable vertical scrolling */
    max-height: 200px; /* Set maximum height to avoid overflow */
    top: 100%; /* Position directly below the filter item */
}

.amount-item,
.type-item,
.status-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
}

.amount-item input[type="checkbox"],
.type-item input[type="checkbox"],
.status-item input[type="checkbox"] {
    margin-left: 10px;
}

.filter-actions {
    display: flex;
    justify-content: space-between;
    padding-top: 10px;
}

.filter-btn {
    background-color: black;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
}

.filter-btn:hover {
    opacity: 0.8;
}
.switch {
position: relative;
display: inline-block;
width: 34px;
height: 20px;
margin-left: 120px;
}

.switch input {
opacity: 0;
width: 0;
height: 0;
}

.slider {
position: absolute;
cursor: pointer;
top: 0;
left: 0;
right: 0;
bottom: 0;
background-color: #ccc;
-webkit-transition: .4s;
transition: .4s;
border-radius: 20px;
}

.slider:before {
position: absolute;
content: "";
height: 16px;
width: 16px;
left: 2px;
bottom: 2px;
background-color: white;
-webkit-transition: .4s;
transition: .4s;
border-radius: 50%;
}

input:checked + .slider {
background-color: #000000;
}

input:checked + .slider:before {
-webkit-transform: translateX(14px);
-ms-transform: translateX(14px);
transform: translateX(14px);
}

.slider.round {
border-radius: 34px;
}

.slider.round:before {
border-radius: 50%;
}
