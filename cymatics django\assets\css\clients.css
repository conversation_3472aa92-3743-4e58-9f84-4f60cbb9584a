
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    display: flex;
    height: 100vh;
    flex-direction: column;
}

.container {
    display: flex;
    width: 100%;
    flex-grow: 1;
}

.sidebar {
    background-color: #1e1e1e;
    color: white;
    width: 250px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
}

.sidebar .logo {
    padding: 20px;
    text-align: center;
}

.menu-title {
    padding: 10px 0;
    text-align: center;
}

.sidebar nav ul {
    list-style: none;
    padding: 0;
    width: 100%;
}

.sidebar nav ul li {
    padding: 12px 20px;
    cursor: pointer;
    transition: background-color 0.3s, color 0.3s, border-left 0.3s;
    text-align: left;
    display: flex;
    align-items: center;
}

.sidebar nav ul li:hover {
    background-color: #333;
    color: #fff;
    border-left: 4px solid #fff;
}

.menu-icon {
    margin-right: 10px;
    width: 24px;
    height: 24px;
}

.main-content {
    flex-grow: 1;
    background-color: #f1f1f1;
    padding: 20px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.clients-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.clients-table th, .clients-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
    position: relative;
}

.clients-table tr:hover .client-actions {
    display: flex;
}

.client-actions {
    display: none;
    align-items: center;
    gap: 10px;
}

.client-actions button {
    padding: 5px 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.share-btn {
    background-color: #333;
    color: white;
}

.call-btn {
    background-color: #f0f0f0;
    color: #333;
}

.button-icon {
    margin-right: 5px;
    width: 16px;
    height: 16px;
}

.search-box {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.search-box input {
    padding: 10px;
    width: 15%;
    margin-right: 10px;
}

.new-btn {
    background-color: #333;
    color: white;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    border-radius: 5px;
    display: flex;
    align-items: center;
}

.options-icon {
    width: 24px;
    height: 24px;
    cursor: pointer;
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.4);
}

.modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 50%;
    border-radius: 10px;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.close:hover,
.close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}

.modal-content form {
    display: flex;
    flex-direction: column;
}

.modal-content form label {
    margin-top: 10px;
}

.modal-content form input,
.modal-content form button {
    padding: 10px;
    margin-top: 5px;
    border-radius: 5px;
    border: 1px solid #ccc;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
}

.form-actions button {
    margin-left: 10px;
}

form button[type="submit"],
form button[type="button"] {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

form button[type="submit"] {
    background-color: black;
    color: white;
}

form button[type="button"] {
    background-color: #ccc;
}
