body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    display: flex;
    height: 100vh;
    flex-direction: column;
}

.container {
    display: flex;
    width: 100%;
    flex-grow: 1;
}

.sidebar {
    background-color: #1e1e1e;
    color: white;
    width: 250px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
}

.sidebar .logo {
    padding: 20px;
    text-align: center;
}

.menu-title {
    padding: 10px 0;
    text-align: center;
}

.sidebar nav ul {
    list-style: none;
    padding: 0;
    width: 100%;
}

.sidebar nav ul li {
    padding: 12px 20px;
    cursor: pointer;
    transition: background-color 0.3s, color 0.3s, border-left 0.3s;
    text-align: left;
    display: flex;
    align-items: center;
}

.sidebar nav ul li:hover {
    background-color: #333;
    color: #fff;
    border-left: 4px solid #fff;
}

.menu-icon {
    margin-right: 10px;
    width: 24px;
    height: 24px;
}

.main-content {
    flex-grow: 1;
    background-color: #f1f1f1;
    padding: 20px;
    display: flex;
    flex-direction: column;
}

.client-info {
    background-color: #ffffff;
    padding: 20px;
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.client-info h1 {
    margin: 0;
    font-size: 24px;
}

.client-info p {
    margin: 5px 0;
    font-size: 18px;
}

.client-info .contact-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.client-info .contact-details {
    flex-grow: 1;
    margin-right: 10px;
}

.client-info .client-actions {
    display: flex;
    gap: 15px;
}

.client-actions button {
    padding: 10px 20px;
    border: 1px solid #ddd;
    border-radius: 25px;
    cursor: pointer;
    display: flex;
    align-items: center;
    background-color: #f0f0f0;
    color: #333;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: background-color 0.3s ease, transform 0.2s ease;
}

.client-actions button:hover {
    background-color: #e0e0e0;
    transform: translateY(-2px);
}

.client-actions button img {
    margin-right: 10px;
    width: 16px;
    height: 16px;
}

.client-balance {
    font-size: 48px;
    font-weight: bold;
    text-align: center;
    width: 100%;
    margin: 20px 0;
}

.projects-section {
    background-color: #1e1e1e;
    color: white;
    padding: 20px;
}

.projects-section h2 {
    margin-top: 0;
}

.project-card {
    background-color: #333;
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin-top: 10px;
    display: flex;
    align-items: center;
    transition: background-color 0.3s ease, transform 0.2s ease;
    cursor: pointer;
}

.project-card:hover {
    background-color: #444;
    transform: translateY(-2px);
}

.project-card img {
    margin-right: 10px;
    width: 50px;
    height: 50px;
    border-radius: 5px;
}

.project-card .arrow-icon {
    margin-left: auto;
    width: 10px;
    height: 10px;
}