body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    display: flex;
    height: 100vh;
  }
  
  .container {
    display: flex;
    width: 100%;
  }
  
  .sidebar {
    background-color: #1e1e1e;
    color: white;
    width: 250px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .sidebar .logo {
    padding: 20px;
  }
  
  .sidebar nav ul {
    list-style: none;
    padding: 0;
    width: 100%;
  }
  
  .menu-item {
    padding: 12px 20px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.3s;
  }
  
  .menu-item.active {
    background-color: #333;
  }
  
  .menu-item:hover {
    background-color: #333;
  }
  
  .menu-icon {
    margin-right: 10px;
    width: 24px;
    height: 24px;
  }
  
  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: white;
  }
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: white;
    border-bottom: 1px solid #ddd;
  }
  
  .header h1 {
    font-size: 24px;
  }
  
  .search-add {
    display: flex;
    align-items: center;
  }
  
  .search-add input {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    margin-right: 10px;
  }
  
  .search-add .add-btn {
    padding: 10px 20px;
    border: none;
    background-color: #000000;
    color: white;
    border-radius: 5px;
    cursor: pointer;
  }
  
  .content {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    padding: 20px;
  }
  
  .movie {
    position: relative;
    flex: 1;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f9f9f9;
    cursor: pointer;
    transition: background-color 0.3s;
  }
  
  .movie:hover {
    background-color: #e9e9e9;
  }
  
  .movie p {
    margin: 0;
  }
  
  .options {
    position: absolute;
    top: 20px;
    right: 20px;
  }
  
  .options-btn {
    border: none;
    background: none;
    font-size: 20px;
    cursor: pointer;
  }
  
  .options-menu {
    display: none;
    position: absolute;
    top: 30px;
    right: 0;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 10;
  }
  
  .options-menu button {
    border: none;
    background: none;
    padding: 10px 20px;
    width: 100%;
    text-align: left;
    cursor: pointer;
  }
  
  .options-menu button:hover {
    background-color: #f4f4f4;
  }
  .modal {
    display: none;
    position: fixed;
    z-index: 100;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    padding-top: 60px;
  }
  
  .modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 400px;
    border-radius: 10px;
  }
  
  .close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
  }
  
  .close:hover,
  .close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
  }
  
  
  #editForm, #addForm {
    display: flex;
    flex-direction: column;
    gap: 15px; 
  }
  
  #editForm label, #addForm label {
    margin-bottom: 5px; 
  }
  
  #editForm input, #addForm input, #addForm select {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    width: 100%; 
    box-sizing: border-box;
  }
  
  .form-buttons {
    display: flex;
    gap: 10px; 
  }
  
  .submit-btn,
  .cancel-btn {
    flex: 1; 
    padding: 10px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
  }
  
  .submit-btn {
    background-color: #000;
    color: white;
  }
  
  .cancel-btn {
    background-color: #ddd;
    color: black;
  }
  
  
  .modal {
    display: none;
    position: fixed;
    z-index: 100;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    padding-top: 60px;
  }
  
  .modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 400px;
    border-radius: 10px;
  }
  
  .close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
  }
  
  .close:hover,
  .close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
  }
  
  
  #editForm,
  #addForm {
    display: flex;
    flex-direction: column;
    gap: 15px; 
  }
  
  #editForm label,
  #addForm label {
    margin-bottom: 5px; 
  }
  
  #editForm input,
  #addForm input,
  #addForm select {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    width: 100%; 
    box-sizing: border-box;
  }
  
  .form-buttons {
    display: flex;
    gap: 10px; 
  }
  
  .submit-btn,
  .cancel-btn {
    flex: 1; 
    padding: 10px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
  }
  
  .submit-btn {
    background-color: #000;
    color: white;
  }
  
  .cancel-btn {
    background-color: #ddd;
    color: black;
  }
