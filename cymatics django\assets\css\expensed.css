body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    display: flex;
    height: 100vh;
}

.container {
    display: flex;
    width: 100%;
}

.sidebar {
    background-color: #1e1e1e;
    color: white;
    width: 250px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.sidebar .logo {
    padding: 20px;
}

.sidebar nav ul {
    list-style: none;
    padding: 0;
    width: 100%;
}

.menu-item {
    padding: 12px 20px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.3s;
}

.menu-item.active {
    background-color: #333;
}

.menu-item:hover {
    background-color: #333;
}

.menu-icon {
    margin-right: 10px;
    width: 24px;
    height: 24px;
}

.main-content {
    flex-grow: 1;
    background-color: white;
    padding: 40px;
    box-sizing: border-box;
}

.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #ddd;
    padding-bottom: 20px;
    margin-bottom: 20px;
}

.header img {
    width: 50px;
    height: 70px;
}

.header h1 {
    margin: 0;
    font-size: 24px;
    flex-grow: 1;
    margin-left: 20px;
}

.date {
    font-size: 17px;
    color: #5e5d5d;
    font-weight: lighter;
}

.header .edit-button {
    background-color: black;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    text-align: center;
    font-size: 16px;
}

.expense-details {
    background-color: #f0f0f0;
    padding: 20px;
    border-radius: 10px;
}

.expense-details h2 {
    margin: 0 0 20px 0;
}

.expense-details p {
    margin: 10px 0;
}

.edit-button {
    display: inline-block;
    padding: 10px 20px;
    background-color: #000;
    color: white;
    border-radius: 5px;
    text-decoration: none;
    border: none;
    cursor: pointer;
}

.edit-button:hover {
    background-color: #555;
}

.header img {
    width: 14%;
    height: 100px;
    object-fit: cover;
    margin-bottom: 10px;
}

.note {
    margin-top: 20px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.note textarea {
    width: 100%;
    border: none;
    padding: 10px;
    font-size: 16px;
    resize: none;
    height: 100px;
}

.note .done-button {
    display: inline-block;
    padding: 10px 20px;
    background-color: #000000;
    color: white;
    border-radius: 5px;
    text-decoration: none;
    border: none;
    cursor: pointer;
    margin-top: 15px;
    margin-left:550px;

}

.note .done-button:hover {
    background-color: #858886;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: #fefefe;
    padding: 20px;
    border-radius: 10px;
    width: 400px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.modal-header h2 {
    margin: 0;
    font-size: 18px;
}

.modal-body {
    display: flex;
    flex-direction: column;
}

.modal-body label {
    margin-bottom: 10px;
    font-weight: bold;
}

.modal-body input,
.modal-body select,
.modal-body textarea {
    margin-bottom: 15px;
    padding: 10px;
    font-size: 16px;
    width: 100%;
    box-sizing: border-box;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
}

.modal-footer button {
    padding: 10px 20px;
    margin-left: 10px;
    border-radius: 5px;
    border: none;
    cursor: pointer;
    font-size: 16px;
}

.modal-footer .submit-btn {
    background-color: #000000;
    color: white;
}

.modal-footer .cancel-btn {
    background-color: #868484;
    color: rgb(0, 0, 0);
}