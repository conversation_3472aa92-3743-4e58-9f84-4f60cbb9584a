body, html {
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
}

.container {
    display: flex;
    height: auto;
    overflow-y: auto;
}


    .sidebar {
      background-color: #1e1e1e;
      color: white;
      width: 250px;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
  }
  
  .sidebar .logo {
      padding: 20px;
      text-align: center;
  }
  
  .menu-title {
      padding: 10px 0;
      text-align: center;
  }
  
  .sidebar nav ul {
      list-style: none;
      padding: 0;
      width: 100%;
  }
  
  .sidebar nav ul li {
      padding: 12px 20px;
      cursor: pointer;
      transition: background-color 0.3s, color 0.3s, border-left 0.3s;
      text-align: left;
      display: flex;
      align-items: center;
  }
  
  .sidebar nav ul li:hover {
      background-color: #333;
      color: #fff;
      border-left: 4px solid #fff;
  }
  
  .menu-icon {
      margin-right: 10px;
      width: 24px;
      height: 24px;
  }
  



.main-content {
    flex-grow: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
    background-color: #fff;
}

.upper-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    background-color: #fff;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
}

.upper-bar .total-income span:first-child {
    font-weight: bold;
    color: #333;
}

.upper-bar .total-income span:last-child {
    font-size: 24px;
    color: green;
}

.chart-section {
    margin-bottom: 30px;
}

.add-income {
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
}

.add-income-btn {
    padding: 15px 30px;
    background-color: #cacaca;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    font-size: 18px;
}

.add-income-btn:hover {
    background-color: #d0d0d0;
}

.header {
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-end;
}

.header form {
    display: flex;
    align-items: center;
}

.header form input[type="text"] {
    padding: 5px;
    border-radius: 5px;
    border: 1px solid #ddd;
    margin-right: 10px;
}

.header form button {
    padding: 5px 10px;
    background-color: #333;
    color: #fff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.income-table {
    flex-grow: 1;
    overflow-y: auto;
}

.income-table table {
    width: 100%;
    border-collapse: collapse;
}

.income-table table th,
.income-table table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.income-table table th {
    background-color: #f4f4f4;
}

.action-btn {
    padding: 5px 10px;
    border: none;
    background-color: #ccc;
    border-radius: 5px;
    cursor: pointer;
  }

.footer {
    display: flex;
    justify-content: center;
    padding: 10px 0;
    background-color: #333;
}

.footer .btn {
    font-size: 24px;
    color: #fff;
    background-color: #B24BF3;
    border: none;
    border-radius: 50%;
    padding: 10px 20px;
    cursor: pointer;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgb(0, 0, 0);
    background-color: rgba(0, 0, 0, 0.4);
    padding-top: 60px;
}

.modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #ddd;
    width: 80%;
    max-width: 500px;
    border-radius: 10px;
    position: relative;
}

.modal-content .close {
    position: absolute;
    top: 10px;
    right: 10px;
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.modal-content .close:hover,
.modal-content .close:focus {
    color: #000;
    text-decoration: none;
    cursor: pointer;
}

.modal-content form {
    display: flex;
    flex-direction: column;
}

.modal-content form label {
    margin-bottom: 10px;
}

.modal-content form input,
.modal-content form button {
    padding: 10px;
    margin-bottom: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.modal-content form .form-actions {
    display: flex;
    justify-content: space-between;
}

.modal-content form .form-actions button {
    padding: 10px 20px;
}
.form-actions button {
    margin-left: 10px;
  }
  form button[type="submit"],
  form button[type="button"] {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    align-items : left;
  }
  
  form button[type="submit"] {
    background-color: black;
    color: white;
  }
  
  form button[type="button"] {
    background-color: #ccc;
  }



.footer {
    display: flex;
    justify-content: center;
    padding: 20px;
    background-color: white;
    margin-top: auto;
    border-top: 1px solid #ddd;
  }

.footer .btn {
    padding: 10px 20px;
    background-color: #000000;
    color: white;
    border:None;
    width: 100px;
    border-radius: 10px;
    cursor: pointer;
    font-size: 24px;

}