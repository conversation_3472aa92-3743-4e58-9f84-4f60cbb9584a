body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    display: flex;
    height: 100vh;
}

.container {
    display: flex;
    width: 100%;
}

.sidebar {
    background-color: #1e1e1e;
    color: white;
    width: 250px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.sidebar .logo {
    padding: 20px;
}

.sidebar nav ul {
    list-style: none;
    padding: 0;
    width: 100%;
}

.menu-item {
    padding: 12px 20px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.3s;
}

.menu-item.active {
    background-color: #333;
}

.menu-item:hover {
    background-color: #333;
}

.menu-icon {
    margin-right: 10px;
    width: 24px;
    height: 24px;
}

.main-content {
    flex-grow: 1;
    background-color: #f1f1f1;
    padding: 20px;
    display: flex;
    flex-direction: column;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header input[type="search"] {
    padding: 5px;
    font-size: 16px;
    width: 200px;
}

.completed-list {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.payment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.payment-item:last-child {
    border-bottom: none;
}

.payment-item img {
    width: 70px;
    height: 70px;
    border-radius: 8px;
    margin-right: 15px;
}

.payment-item-details {
    flex: 1;
    display: flex;
    align-items: center;
}

.payment-item-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.payment-item-subtitle {
    font-size: 14px;
    color: #888;
}

.call-button {
    background-color: #000;
    color: #fff;
    padding: 10px 20px;
    border-radius: 5px;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    margin-right: 10px;
}

.call-button svg {
    margin-right: 5px;
}

.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-button {
    background-color: transparent;
    border: none;
    cursor: pointer;
    font-size: 24px;
    padding: 10px;
}

.dropdown-content {
    display: none;
    position: absolute;
    right: 0;
    background-color: #f9f9f9;
    min-width: 120px;
    box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.2);
    z-index: 1;
    border-radius: 5px;
}

.dropdown-content button {
    color: black;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    background: none;
    border: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
}

.dropdown-content button:hover {
    background-color: #ddd;
}

.dropdown:hover .dropdown-content {
    display: block;
}

.modal {
display: none;
position: fixed;
z-index: 1;
left: 0;
top: 0;
width: 100%;
height: 100%;
overflow: auto;
background-color: rgba(0,0,0,0.4);
}

.modal-content {
background-color: #fefefe;
margin: 15% auto;
padding: 20px;
border: 1px solid #888;
width: 35%;
border-radius: 10px;
}
.modal-content label {
display: block;
margin-bottom: 2px; /* Reduce space between label and input */
font-weight: normal;
}

.close {
color: #aaa;
float: right;
font-size: 28px;
font-weight: bold;
}
.close:hover,
.close:focus {
color: black;
text-decoration: none;
cursor: pointer;
}

#editForm {
display: flex;
flex-direction: column;
}

#editForm label {
margin-top: 10px;
}

#editForm input,
#editForm select {
padding: 8px;
margin-top: 5px;
border: 1px solid #ccc;
border-radius: 4px;
}

#editForm button {
padding: 10px;
margin-top: 20px;
background-color: #000000;
color: #fff;
border: none;
border-radius: 4px;
cursor: pointer;
}

#editForm button:hover {
background-color: #555;
}

.close:hover,
.close:focus {
color: black;
text-decoration: none;
cursor: pointer;
}

  /* Outsourcing Section Styles */
  #outsourcingDetails {
    margin-top: 10px;
}

#outsourcingDetails label {
    display: block;
    margin-top: 10px;
    font-weight: medium;
}

#outsourcingDetails select,
#outsourcingDetails input[type="number"] {
    width: 100%;
    padding: 8px;
    margin-top: 5px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.switch {
    position: relative;
    display: inline-block;
    width: 34px;
    height: 20px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 20px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    border-radius: 50%;
    transition: .4s;
}

input:checked + .slider {
    background-color: #000000;
}

input:checked + .slider:before {
    transform: translateX(14px);
}