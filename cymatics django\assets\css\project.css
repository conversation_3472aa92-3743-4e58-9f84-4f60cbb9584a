        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            height: 100vh;
            flex-direction: column;
            overflow: hidden; /* Prevent scrollbars */
        }

        .container {
            display: flex;
            width: 100%;
            flex-grow: 1;
            overflow: hidden; /* Prevent scrollbars */
        }

        .sidebar {
            background-color: #1e1e1e;
            color: white;
            width: 250px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
        }

        .sidebar .logo {
            padding: 20px;
            text-align: center;
        }

        .menu-title {
            padding: 10px 0;
            text-align: center;
        }

        .sidebar nav ul {
            list-style: none;
            padding: 0;
            width: 100%;
        }

        .sidebar nav ul li {
            padding: 12px 20px;
            cursor: pointer;
            transition: background-color 0.3s, color 0.3s, border-left 0.3s;
            text-align: left;
            display: flex;
            align-items: center;
        }

        .sidebar nav ul li:hover {
            background-color: #333;
            color: #fff;
            border-left: 4px solid #fff;
        }

        .menu-icon {
            margin-right: 10px;
            width: 24px;
            height: 24px;
        }

        .main-content {
            flex-grow: 1;
            background-color: #ffffff;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .header h1 {
            font-size: 24px;
        }

        .search-add {
            display: flex;
            align-items: center;
        }

        .search-add input {
            padding: 10px;
            margin-right: 20px;
            background-color: rgb(248, 248, 248);
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: small;
        }

        .add-btn {
            padding: 10px 20px;
            background-color: #000000;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .add-btn:hover {
            background-color: #555;
        }

        .content {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }

        .project-card {
            background-color: #ffffff;
            border: 1px solid #ccc;
            border-radius: 8px;
            padding: 20px;
            width: 340px;
            text-align: center;
            position: relative;
        }

        .project-card img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            margin-bottom: 10px;
        }

        .project-card p {
            margin-bottom: 10px;
            font-size: 14px;
            text-align: left;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            text-align: left;
        }

        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropbtn {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            background-color: #f9f9f9;
            min-width: 100px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1;
        }

        .dropdown-content a {
            color: black;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
        }

        .dropdown-content a:hover {
            background-color: #f1f1f1;
        }

        .files-btn {
            padding: 3px;
            padding-right: 50px;
            padding-left: 50px;
            margin-top: 10px;
            background-color: #000000;
            color: #f9f9f9;
            border: none;
            border-radius: 6px;
            cursor: pointer;
        }

        .share-btn {
            padding: 3px;
            padding-right: 50px;
            padding-left: 50px;
            margin-top: 10px;
            background-color: #ffffff;
            color: #000000;
            border: 1px solid #888;
            border-radius: 6px;
            cursor: pointer;
        }

        .files-btn img,
        .share-btn img {
            width: 20px;
            height: 20px;
            vertical-align: middle;
            margin-right: 6px;
            margin-top: 7px;
        }

       

        .files-btn:hover {
            background-color: #000000;
        }

        .share-btn:hover {
            background-color: #fdfafa;
        }

        .filter-btn img {
            width: 20px;
            height: 20px;
            vertical-align: middle;
            margin-right: 6px;
            margin-top: -2px;
        }
        .toggle-company img {
            width: 15px;
            height: 10px;
            vertical-align: middle;
            margin-right: 20px;
            margin-top: -2px;
        }

        .toggle-status img {
            width: 15px;
            height: 10px;
            vertical-align: middle;
            padding-right:100px;
            margin-right: 50px;
            margin-top: -2px;
        }
        .toggle-type img {
            width: 15px;
            height: 10px;
            vertical-align: middle;
            margin-right: 6px;
            margin-top: -2px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.4);
        }

        .modal-content {
            background-color: #ffffff;
            margin: 10% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 400px;
            border-radius: 8px;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
        }

        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }

        #editForm, #addForm {
            display: flex;
            flex-direction: column;
        }

        #editForm label, #addForm label {
            margin-top: 10px;
        }

        #editForm input, #editForm select,
        #addForm input, #addForm select {
            padding: 8px;
            margin-top: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        #editForm button, #addForm button {
            padding: 10px;
            margin-top: 20px;
            background-color: #000000;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        #editForm button:hover, #addForm button:hover {
            background-color: #555;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
        }

        .form-actions button {
            margin-left: 10px;
        }

        /* Outsourcing Section Styles */
        #outsourcingDetails {
            margin-top: 10px;
        }

        #outsourcingDetails label {
            display: block;
            margin-top: 10px;
            font-weight: medium;
        }

        #outsourcingDetails select,
        #outsourcingDetails input[type="number"] {
            width: 100%;
            padding: 8px;
            margin-top: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        #addOutsourcingDetails {
            margin-top: 10px;
        }

        #addOutsourcingDetails label {
            display: block;
            margin-top: 10px;
            font-weight: medium;
        }

        #addOutsourcingDetails select,
        #addOutsourcingDetails input[type="number"] {
            width: 100%;
            padding: 8px;
            margin-top: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 34px;
            height: 20px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 20px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            border-radius: 50%;
            transition: .4s;
        }

        input:checked + .slider {
            background-color: #000000;
        }

        input:checked + .slider:before {
            transform: translateX(14px);
        }

        .filter-btn {
            background-color: rgb(248, 248, 248);
            color: rgb(92, 90, 90);
            border: 1px solid #ccc;
            padding: 8px 16px;
            margin-right: 30px;
            border-radius: 4px;
            cursor: pointer;
            position: relative;
            padding-right: 20px;
            justify-content: space-between;
            align-items: center;
            display: flex;
            width: 130px;
            font-size: medium;
            font-style: normal;
        }
        .filter-btn .filter-text {
             flex-grow: 1;
             margin-left:20px;
         }
         
         .filter-btn .dropdown-icon {
           margin-left: 28px; /* Align icon to the right */
           width: 18px;
           height: 18px;
           margin-top: 3px;

       }

       .dropdown-section .side-arrow {
    margin-left: 20px;
    margin-right:50px; /* Gap between the label and the side arrow */
    width: 20px;
    height: 12px;
   
   vertical-align: middle;
   
}


        .filter-dropdown {
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            position: absolute;
            top: 80px; /* Adjust to position directly below the filter button */
            right: 20px; /* Adjust as per your layout */
            width: 280px; /* Slightly wider for more space */
            padding: 10px;
            display: none; /* Initially hidden */
            z-index: 10; /* Ensure it appears above other elements */
        }
         
        .filter-dropdown .dropdown-section {
            color: black;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
        }

        .filter-dropdown .dropdown-section:hover{
            background-color: #f1f1f1;
            display:block;
        }

        .dropdown-section label {
            font-weight: bold;
            cursor: pointer;
            display: block;
        }

        .dropdown-section input[type="checkbox"] {
            width: 20px; /* Increased checkbox size */
            height: 20px; /* Increased checkbox size */
            margin-right: 20px; 

        }

        .submenu {
            display: none; /* Start as hidden */
            padding-left: 20px; /* Indent for submenu */
            color: #000000;
        }

        .clear-btn {
            padding: 5px;
            padding-right: 40px;
            padding-left: 40px;
            margin-top: 10px;
            background-color: #ffffff;
            color: rgb(0, 0, 0);
            border: 1px solid #888;
            border-radius: 4px;
            cursor: pointer;
        }

        .done-btn {
            padding: 5px;
            padding-right: 40px;
            padding-left: 40px;
            margin-top: 10px;
            background-color: #000000;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .clear-btn:hover, .done-btn:hover {
            background-color: #555;
        }

