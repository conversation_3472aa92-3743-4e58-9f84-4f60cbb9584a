        body {
            margin: 0;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            display: flex;
            height: 100vh;
        }
        .container {
            display: flex;
            width: 100%;
        }
        .sidebar {
        background-color: #1e1e1e;
        color: white;
        width: 250px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
    }
    
    .sidebar .logo {
        padding: 20px;
        text-align: center;
    }
    
    .menu-title {
        padding: 10px 0;
        text-align: center;
    }
    
    .sidebar nav ul {
        list-style: none;
        padding: 0;
        width: 100%;
    }
    
    .sidebar nav ul li {
        padding: 12px 20px;
        cursor: pointer;
        transition: background-color 0.3s, color 0.3s, border-left 0.3s;
        text-align: left;
        display: flex;
        align-items: center;
    }
    
    .sidebar nav ul li:hover {
        background-color: #333;
        color: #fff;
        border-left: 4px solid #fff;
    }
    
    .menu-icon {
        margin-right: 10px;
        width: 24px;
        height: 24px;
    }
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background-color: white;
        }
        .project-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background-color: white;
            border-bottom: 1px solid #ddd;
        }
        .project-header h4 {
            margin: 0;
        }
        .project-title {
            font-size: 30px;
            font-weight: bold;
            margin-right: 80px; 
            margin-bottom: 10px;
            margin-top: 10px;
        }
        .project-status {
            color: rgb(0, 0, 0);
            margin-right: 20px; 
            margin-bottom: 10px;
        }
        .edit-button {
            background-color: #000;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 20px; 
        }
        .project-details {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }
        .project-details img {
            width: 40%;
            height: 300px;
            object-fit: cover;
            margin-bottom: 10px;
        }
        .project-info {
            margin-top: 20px;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            align-items: center;
        }
        .info-row:last-child {
            border-bottom: none;
        }
        .info-label {
            font-weight: bold;
        }
        .info-llabel {
            font-weight:medium;
            margin-top: -10px; 
            color:#9a9595;
        }
        .received-bar-container {
            width: 100%;
            background-color: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 5px 0;
            height: 13px;
        }
        .received-bar {
            height: 15px;
            background-color: #030303;
            width: 70%;
            text-align: right;
            padding-right: 5px;
            color: white;
            line-height: 15px;
        }
        .contact-buttons {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 50px;
            margin-top: 10px;
            margin-left:1000px;
        }
        .contact-buttons button {
            padding: 10px 20px;
            margin-left: 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .contact-buttons button.call,
        .contact-buttons button.sms,
        .contact-buttons button.map,
        .contact-buttons button.copy {
            background-color: #000;
            color: white;
        }
        .file {
            display: inline-flex;
            justify-content: space-between;
            padding: 1px;
            border: 1px solid #ccc;
            border-radius: 2px;
            margin-top: 5px;
            align-items: center;
            color: #000;
        }
        .file img {
            width: 50%;
            height: 30px;
            object-fit: cover;
            margin-bottom: 5px;
            padding: 20px;
        }
        
        .rating {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 10px;
            margin-top: 30px;
            align-items: center;
            padding-right: 400px;
            flex-direction: column;
            width: 200px;
            
        }
        .expense{
            display: flex;
            justify-content: space-between;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 10px;
            margin-top: 30px;
            align-items: center;
            padding-right: 100px;   
        }
        .expense1{
            display: flex;
            justify-content: space-between;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 10px;
            margin-top: 30px;
            align-items: center;
            padding-right: 100px;
            
        }
        .expense2{
            display: flex;
            justify-content: space-between;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 10px;
            margin-top: 30px;
            align-items: center;
            padding-right: 100px;
            
        }

        .profit{
            display: flex;
            justify-content: space-between;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 10px;
            margin-top: 30px;
            align-items: center;
            padding-right: 30px;
            margin-right:620px;
            font-size: 25px;
            color:#000000;
            
        }
        .profit1 span{
            color:#ababab;
            font-size: medium;
        }
        
        .indiv{
            padding-left: 35px; 
            justify-content: space-between; 
            border-radius: 4px;
        }
        .rating .stars {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .rating .stars img {
            font-size: 10px;
            color: #616161;
            margin-right: 5px;
            height: 50px;
            width: 51%;
        }
        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.4);
        }
        .modal-content {
            background-color: #fefefe;
            margin: 10% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 400px;
            border-radius: 8px;
        }
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
        }
        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }
        
        .expense-container {
    display: flex;
    flex-direction: column; /* Stack items vertically */
    padding: 20px;
    border: 1px solid #242424;
    margin: 20px;
    border-radius: 10px;
    background-color: #000000;
}

.expense-item {
    display: flex;
    align-items: center; /* Align items vertically center */
    padding: 10px 0;
    border-bottom: 1px solid #333; /* Optional: Add border between items */
    position: relative; /* Position relative for the arrow positioning */
}

.expense-item:last-child {
    border-bottom: none; /* Remove border for the last item */
}

.expense-container .icon img {
    width: 50px;
    height: 50px;
    margin-right: 15px; /* Space between icon and details */
}

.expense-container .expense-details {
    flex: 1; /* Take up remaining space */
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.expense-container .arrow {
    font-size: 20px;
    color: #ebe8e8;
    margin-left: 15px; /* Space between details and arrow */
    position: absolute; /* Position absolute for correct placement */
    right: 0; /* Align to the right */
}



        .icon {
            width: 50px;
            height: 50px;
            margin-right: 25px;
        }
        .icon img {
            width: 100%;
            height: 100%;
            margin-right: 25px;
        }
        .expense-details {
            flex: 1;
            margin-right: auto;
        }
        .exd{
            display: flex;
            justify-content: space-between;
            padding: 10px;
            border-bottom: 1px solid #333;
            font-size: 18px;
            color: white;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        
        .amount {
            font-size: 10px;
            font-weight:medium;
            color: white;
        }
        .category {
            font-size: 17px;
            color: white;
        }
        .subcategory {
            font-size: 15px;
            color: #aaa;
        }
        .sub{
            font-size: 17px;
            color: #fff; 
        }
        .arrow a {
            color: inherit;
            text-decoration: none;
        }

        .arrow {
            font-size: 20px;
            color: #ebe8e8;
            margin-left: 15px;
            position: absolute;
            right: 0;
        }
        
        .form-actions {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
        }
        .form-actions button {
            margin-left: 10px;
        }
        #editForm {
            display: flex;
            flex-direction: column;
        }
        #editForm label {
            margin-top: 10px;
        }
        #editForm input,
        #editForm select {
            padding: 8px;
            margin-top: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        #editForm button {
            padding: 10px;
            margin-top: 20px;
            background-color: #000000;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        #editForm button:hover {
            background-color: #555;
        }
        #editForm button[type="button"] {
            background-color: white; /* White background for cancel button */
            color: black; /* Black text for cancel button */
        }
.close {
  color: #aaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: black;
  text-decoration: none;
  cursor: pointer;
}


        .info-row {
            display: flex;
            justify-content: space-between;
            padding: 30px 0;
            border-bottom: 1px solid #eee; 
        }

        .info-row2 {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee; 
        }

        .container1 {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            margin: auto;
        }
        .inforow {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            padding-top: 10px;
        }
        .inforow .text {
            display: flex;
            flex-direction: column;
        }
        .inforow .text .name {
            font-size: 14px;
            color:#9a9595;
        }
        .inforow.text.phone-number{
            font-size: 19px;
            color: #000000;
            margin-top: 10px;
            font-weight: bolder;
        }
        .inforow .text .location {
            font-size: 14px;
            color:#9a9595;
            margin-top: 10px;
        }
        
        .inforow button {
            background-color: #e4e1e1;
            color: rgb(0, 0, 0);
            border: none;
            padding: 5px 15px;
            border-radius: 25px;
            cursor: pointer;
            margin-left: 10px;
            
        }
        .inforow button img {
            width: 20px; /* Adjust icon size */
            height: 20px; /* Adjust icon size */
            margin-right: 10px; /* Space between icon and text */
            padding-left:10px;
            margin-bottom: auto;
        }

        .filesrow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px; /* Changed margin to only top */
}

.filesrow button {
    display: flex;
    align-items: center; /* Center the icon and text vertically */
    justify-content: center; /* Center the icon and text horizontally */
    color: white; /* Text color for buttons */
    border: 1px solid #d0d0d0; /* Border for buttons */
    padding: 10px 20px; /* Padding for buttons */
    border-radius: 4px; /* Rounded corners */
    cursor: pointer; /* Cursor on hover */
    flex: 1; /* Allow buttons to grow */
    margin: 0 5px; /* Space between buttons */
    font-weight: bold;
}

.files-button {
    background-color: black; /* Black background for Files button */
}

.share-button,
.copy-button {
    background-color: white; /* White background for Share and Copy buttons */
    color: black; /* Text color for Share and Copy buttons */
}

.filesrow button img {
    width: 20px; /* Adjust icon size */
    height: 20px; /* Adjust icon size */
    margin-right: 10px; /* Space between icon and text */
}

.filesrow button:last-child {
    margin-right: 0; /* No margin on the last button */
}
        .info-row:last-child {
            border-bottom: none;
        }
        .infolabel {
            font-weight: 200;
            font-size: 16px;
            margin-top: 18px;
            margin-left: 22px;
            color:#616161;
        }
        .inform{
            display: flex;
            justify-content: space-between;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
            font-size:small;
            color:#555;
        }
        .inform-label {
            font-weight: bold;
            margin-top: 25px;
            font-size:medium;
            color:#000
        }
        .info{
    display: flex;
    justify-content: space-between; /* Distributes items evenly with space in between */
    padding: 25px 0;
    border-bottom: 1px solid #eee;
}
.info-item {
    flex: 1; /* Allows items to grow and fill the available space equally */
    text-align: center; /* Centers the text within each item */
    font-size: large;
    font-weight: 550;
    color: #242424;
}

.info-item2 {
    flex: 1; /* Allows items to grow and fill the available space equally */
    text-align: center; /* Centers the text within each item */
    color:#9a9595;
}
        .rating {
            display: flex;
            justify-content: space-between;
            padding: 5px;
            border: none;
            border-radius: 4px;
            margin-top: 20px;
            align-items: center;

        }
        
        
        .rating .stars {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .rating .stars i {
            font-size: 30px;
            color: #ccc;
            margin-right: 5px;
            cursor: pointer;
        }
        .rating .stars i.filled {
            color: rgb(0, 0, 0);}

            .button img {
            width: 50%;
            height: 30px;
            object-fit: cover;
            margin-bottom: 5px;
            padding: 20px;
        }
        .container2 {
            background-color: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            border: 1px solid #ccc;
            width: 100%;
            max-width: 1000px;
            padding-top: 30px;
            margin-top:18px;
            padding-right:170px;
            margin-top:30px;

        }
        .info-row1 {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            margin-top:10px;
        }
        .info-item1 {
            font-size: 16px;
            color: #333;
        }
        .info-item1 span {
            font-weight: medium;
            color:#9a9595;
        }
        .project-header .map-image {
            width: 30%; 
            height: auto; 
            margin-right: 20px; 
        }
        .row {
            display: flex;
            align-items: center; 
        }
        .info-llabel {
    font-weight: medium;
    margin-top: -10px;
    color:#9a9595;
     }

     .info-llabel1{
        color:#2c2c2c;
     }
 
   /* Outsourcing Section Styles */
#outsourcingDetails {
    margin-top: 10px;
}

#outsourcingDetails label {
    display: block;
    margin-top: 10px;
    font-weight: medium;
}

#outsourcingDetails select,
#outsourcingDetails input[type="number"] {
    width: 100%;
    padding: 8px;
    margin-top: 5px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.switch {
    position: relative;
    display: inline-block;
    width: 34px;
    height: 20px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 20px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    border-radius: 50%;
    transition: .4s;
}

input:checked + .slider {
    background-color: #000000;
}

input:checked + .slider:before {
    transform: translateX(14px);
}
