body {
    margin: 0;
    font-family: Arial, sans-serif;
  }

  .container {
    display: flex;
  }

  .sidebar {
    width: 250px;
    background-color: #333;
    color: white;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    overflow-y: auto;
  }

  .logo {
    text-align: center;
    padding: 20px 0;
  }

  nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  nav ul li {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    cursor: pointer;
  }

  nav ul li:hover {
    background-color: #575757;
  }

  .menu-icon {
    margin-right: 10px;
    width: 24px;  /* Change this value to adjust the width */
    height: 24px; /* Change this value to adjust the height */
  }

  .main-content {
    margin-left: 250px;
    padding: 20px;
    width: calc(100% - 250px);
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .search-bar {
    width: 200px;
    padding: 5px 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
  }

  .status-grid {
    display: flex;
    justify-content: space-between;
  }

  .status-column {
    width: 30%;
    border: 1px solid #ccc; /* Add a subtle border */
    border-radius: 5px; /* Optional: round the corners slightly */
    padding: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Optional: add a subtle shadow */
  }

  .status-column h2 {
    text-align: center;
    margin-bottom: 20px;
  }

  .project-card {
    background-color: #f4f4f4;
    padding: 10px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .project-box {
    width: 20px;
    height: 20px;
    background-color: #ccc;
    margin-right: 10px;
    border: 1px solid #aaa;
  }

  .project-card span {
    font-weight: Medium;
  }
