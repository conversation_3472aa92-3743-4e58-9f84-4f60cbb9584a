# Generated by Django 4.0.1 on 2024-07-18 12:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cymaticsapp', '0002_alter_clients_email_alter_clients_img_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='project',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.TextField()),
                ('name', models.CharField(max_length=100)),
                ('company', models.CharField(max_length=100)),
                ('type', models.CharField(max_length=50)),
                ('status', models.CharField(max_length=50)),
                ('shoot_start_data', models.DateTimeField()),
                ('shoot_end_date', models.DateTimeField()),
                ('Amount', models.IntegerField()),
                ('location', models.CharField(max_length=200)),
                ('outsourcing', models.BooleanField()),
                ('reference', models.TextField(blank=True)),
                ('image', models.ImageField(blank=True, upload_to='media')),
                ('pending_amt', models.IntegerField()),
                ('received_amt', models.IntegerField()),
                ('address', models.CharField(max_length=500)),
                ('map', models.CharField(blank=True, max_length=200)),
                ('profit', models.IntegerField()),
                ('rating', models.IntegerField()),
                ('expenses', models.IntegerField()),
            ],
        ),
        migrations.AlterField(
            model_name='clients',
            name='number',
            field=models.CharField(max_length=20),
        ),
    ]
