# Generated by Django 4.0.1 on 2024-07-18 15:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cymaticsapp', '0005_rename_clients_client'),
    ]

    operations = [
        migrations.CreateModel(
            name='Income',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('description', models.TextField()),
                ('amount', models.IntegerField()),
                ('note', models.TextField(blank=True)),
                ('income', models.BooleanField()),
                ('project_code', models.TextField()),
                ('image', models.ImageField(blank=True, upload_to='media')),
            ],
        ),
    ]
