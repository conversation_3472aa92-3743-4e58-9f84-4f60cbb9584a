# Generated by Django 4.0.1 on 2024-07-18 16:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cymaticsapp', '0006_income'),
    ]

    operations = [
        migrations.CreateModel(
            name='Expense',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('category', models.<PERSON>r<PERSON>ield(max_length=50)),
                ('description', models.TextField()),
                ('amount', models.IntegerField()),
                ('note', models.TextField(blank=True)),
                ('expense', models.BooleanField()),
                ('project_code', models.TextField()),
            ],
        ),
    ]
