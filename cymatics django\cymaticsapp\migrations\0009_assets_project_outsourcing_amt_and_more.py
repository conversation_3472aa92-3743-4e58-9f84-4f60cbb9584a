# Generated by Django 4.0.1 on 2024-07-19 12:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cymaticsapp', '0008_remove_expense_note'),
    ]

    operations = [
        migrations.CreateModel(
            name='Assets',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('type', models.CharField(max_length=100)),
                ('name', models.Char<PERSON>ield(max_length=200)),
                ('quantity', models.IntegerField()),
                ('buy_price', models.IntegerField()),
                ('value', models.IntegerField()),
                ('note', models.TextField(blank=True)),
                ('image', models.ImageField(blank=True, upload_to='')),
            ],
        ),
        migrations.AddField(
            model_name='project',
            name='outsourcing_amt',
            field=models.IntegerField(blank=True, default=0),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='project',
            name='outsourcing_paid',
            field=models.BooleanField(default=0),
            preserve_default=False,
        ),
    ]
