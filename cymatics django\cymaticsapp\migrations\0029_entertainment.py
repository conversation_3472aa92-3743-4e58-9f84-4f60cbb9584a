# Generated by Django 4.0.1 on 2024-07-24 10:13

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cymaticsapp', '0028_project_client'),
    ]

    operations = [
        migrations.CreateModel(
            name='Entertainment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('type', models.<PERSON>r<PERSON><PERSON>(max_length=100)),
                ('language', models.CharField(max_length=100)),
                ('rating', models.IntegerField()),
                ('name', models.Char<PERSON>ield(max_length=100)),
                ('source', models.Char<PERSON>ield(blank=True, max_length=100)),
                ('image', models.ImageField(blank=True, upload_to='media')),
            ],
        ),
    ]
