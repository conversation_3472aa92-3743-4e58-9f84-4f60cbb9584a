# Generated by Django 5.1 on 2024-10-21 09:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cymaticsapp', '0067_emailotp'),
    ]

    operations = [
        migrations.CreateModel(
            name='Outclient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('company', models.CharField(max_length=100)),
                ('number', models.Char<PERSON>ield(max_length=20)),
                ('email', models.EmailField(blank=True, max_length=100)),
                ('img', models.ImageField(blank=True, upload_to='media')),
            ],
        ),
    ]
