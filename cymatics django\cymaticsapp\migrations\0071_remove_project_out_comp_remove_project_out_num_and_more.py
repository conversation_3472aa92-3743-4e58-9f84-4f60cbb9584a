# Generated by Django 5.1 on 2024-10-25 07:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cymaticsapp', '0070_alter_project_latitude_alter_project_longitude'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='project',
            name='out_comp',
        ),
        migrations.RemoveField(
            model_name='project',
            name='out_num',
        ),
        migrations.AlterField(
            model_name='project',
            name='amount',
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name='project',
            name='code',
            field=models.TextField(blank=True, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='project',
            name='company',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AlterField(
            model_name='project',
            name='latitude',
            field=models.FloatField(default=0.0),
        ),
        migrations.Alter<PERSON>ield(
            model_name='project',
            name='location',
            field=models.Char<PERSON>ield(blank=True, max_length=200),
        ),
        migrations.Alter<PERSON>ield(
            model_name='project',
            name='longitude',
            field=models.FloatField(default=0.0),
        ),
        migrations.AlterField(
            model_name='project',
            name='name',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AlterField(
            model_name='project',
            name='shoot_end_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='project',
            name='shoot_start_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='project',
            name='status',
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AlterField(
            model_name='project',
            name='type',
            field=models.CharField(blank=True, max_length=50),
        ),
    ]
