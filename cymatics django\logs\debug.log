Watching for file changes with StatReloader
Waiting for apps ready_event.
Apps ready_event triggered. Sending autoreload_started signal.
Watching dir D:\Django\.venv\Lib\site-packages\django_extensions\templates with glob **/*.
Watching dir D:\Django\.venv\Scripts\cymatics work\templates with glob **/*.
Watching dir D:\Django\.venv\Scripts\cymatics work\locale with glob **/*.mo.
Watching dir D:\Django\.venv\Lib\site-packages\django_extensions\locale with glob **/*.mo.
Watching dir D:\Django\.venv\Scripts\cymatics work\cymaticsapp\locale with glob **/*.mo.
Watching dir D:\Django\.venv\Lib\site-packages\social_django\locale with glob **/*.mo.
(0.015) 
            SELECT
                c.relname,
                CASE
                    WHEN c.relispartition THEN 'p'
                    WHEN c.relkind IN ('m', 'v') THEN 'v'
                    ELSE 't'
                END,
                obj_description(c.oid, 'pg_class')
            FROM pg_catalog.pg_class c
            LEFT JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace
            WHERE c.relkind IN ('f', 'm', 'p', 'r', 'v')
                AND n.nspname NOT IN ('pg_catalog', 'pg_toast')
                AND pg_catalog.pg_table_is_visible(c.oid)
        ; args=None; alias=default
(0.000) SELECT "django_migrations"."id", "django_migrations"."app", "django_migrations"."name", "django_migrations"."applied" FROM "django_migrations"; args=(); alias=default
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\email\base64mime.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\utils\lorem_ipsum.py first seen with mtime 1727602615.8936148
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\logging\config.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\warnings.py first seen with mtime 1666616650.0
File D:\Django\.venv\Lib\site-packages\urllib3\http2\probe.py first seen with mtime 1727626976.0257912
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\wsgiref\handlers.py first seen with mtime 1666616650.0
File D:\Django\.venv\Lib\site-packages\django\contrib\messages\apps.py first seen with mtime 1727602615.3033774
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\subprocess.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\forms\forms.py first seen with mtime 1727602615.8120258
File D:\Django\.venv\Lib\site-packages\social_django\storage.py first seen with mtime 1727863949.0432472
File D:\Django\.venv\Lib\site-packages\django\contrib\messages\constants.py first seen with mtime 1727602615.3033774
File D:\Django\.venv\Lib\site-packages\psycopg2\sql.py first seen with mtime 1727603057.6835434
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\csv.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\email\utils.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\social_core\pipeline\__init__.py first seen with mtime 1727626981.587309
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\windows_utils.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\db\migrations\operations\fields.py first seen with mtime 1727602615.782693
File D:\Django\.venv\Lib\site-packages\django_extensions\apps.py first seen with mtime 1727602646.7851217
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\signals.py first seen with mtime 1727667290.9555664
File D:\Django\.venv\Lib\site-packages\django\core\handlers\exception.py first seen with mtime 1727602615.7201962
File D:\Django\.venv\Lib\site-packages\django\utils\timesince.py first seen with mtime 1727602615.8966143
File D:\Django\.venv\Lib\site-packages\django\core\handlers\__init__.py first seen with mtime 1727602615.717911
File D:\Django\.venv\Lib\site-packages\six.py first seen with mtime 1727603103.1495697
File D:\Django\.venv\Lib\site-packages\urllib3\util\proxy.py first seen with mtime 1727626976.0267918
File D:\Django\.venv\Lib\site-packages\django\dispatch\__init__.py first seen with mtime 1727602615.8090239
File D:\Django\.venv\Lib\site-packages\django\contrib\staticfiles\apps.py first seen with mtime 1727602615.6874578
File D:\Django\.venv\Lib\site-packages\django\urls\base.py first seen with mtime 1727602615.879238
File D:\Django\.venv\Scripts\cymatics work\cymaticspro\urls.py first seen with mtime 1727187892.7092955
File D:\Django\.venv\Lib\site-packages\django\db\models\sql\where.py first seen with mtime 1727602615.8090239
File D:\Django\.venv\Lib\site-packages\django\contrib\messages\utils.py first seen with mtime 1727602615.3043764
File D:\Django\.venv\Lib\site-packages\django\db\models\sql\datastructures.py first seen with mtime 1727602615.8070166
File D:\Django\.venv\Lib\site-packages\urllib3\http2\__init__.py first seen with mtime 1727626976.0257912
File D:\Django\.venv\Lib\site-packages\sqlparse\engine\filter_stack.py first seen with mtime 1727602613.553207
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\stringprep.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\utils\__init__.py first seen with mtime 1727602615.8813968
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\contextlib.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\core\management\commands\runserver.py first seen with mtime 1727602615.7364416
File D:\Django\.venv\Lib\site-packages\django\db\backends\utils.py first seen with mtime 1727602615.746444
File D:\Django\.venv\Lib\site-packages\idna\__init__.py first seen with mtime 1727626978.1915507
File D:\Django\.venv\Lib\site-packages\urllib3\util\__init__.py first seen with mtime 1727626976.0267918
File D:\Django\.venv\Lib\site-packages\django\contrib\sessions\__init__.py first seen with mtime 1727602615.481816
File D:\Django\.venv\Lib\site-packages\django\db\models\functions\__init__.py first seen with mtime 1727602615.80067
File D:\Django\.venv\Lib\site-packages\social_django\managers.py first seen with mtime 1727863949.0411773
File D:\Django\.venv\Lib\site-packages\django\template\__init__.py first seen with mtime 1727602615.8602757
File D:\Django\.venv\Lib\site-packages\django\template\library.py first seen with mtime 1727602615.8642836
File D:\Django\.venv\Lib\site-packages\requests\structures.py first seen with mtime 1727626978.9016902
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\DLLs\_lzma.pyd first seen with mtime 1666616688.0
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\tokens.py first seen with mtime 1727602614.6834962
File D:\Django\.venv\Lib\site-packages\django\views\generic\dates.py first seen with mtime 1727602615.917791
File D:\Django\.venv\Lib\site-packages\django\core\mail\message.py first seen with mtime 1727602615.7211964
File D:\Django\.venv\Lib\site-packages\django\forms\boundfield.py first seen with mtime 1727602615.8110251
File D:\Django\.venv\Lib\site-packages\django\contrib\staticfiles\utils.py first seen with mtime 1727602615.6899412
File D:\Django\.venv\Lib\site-packages\django\db\models\functions\comparison.py first seen with mtime 1727602615.80067
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\format_helpers.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\views\debug.py first seen with mtime 1727602615.911289
File D:\Django\.venv\Lib\site-packages\django\utils\regex_helper.py first seen with mtime 1727602615.8946142
File D:\Django\.venv\Lib\site-packages\openid\__init__.py first seen with mtime 1727626979.191984
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\models.py first seen with mtime 1727794479.7556791
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\dataclasses.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\encodings\utf_8.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\http\cookie.py first seen with mtime 1727602615.8532767
File D:\Django\.venv\Lib\site-packages\social_django\models.py first seen with mtime 1727863949.042235
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\__init__.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\forms\__init__.py first seen with mtime 1727602615.8110251
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\json\__init__.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\contrib\contenttypes\views.py first seen with mtime 1727602614.8438966
File D:\Django\.venv\Lib\site-packages\django\contrib\admin\filters.py first seen with mtime 1727602614.2925608
File D:\Django\.venv\Lib\site-packages\django\db\models\fields\related_descriptors.py first seen with mtime 1727602615.7993228
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\protocols.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\social_core\storage.py first seen with mtime 1727626981.521399
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\email\mime\multipart.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\core\checks\urls.py first seen with mtime 1727602615.7060974
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\contextvars.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\asgiref\__init__.py first seen with mtime 1727602613.8898947
File D:\Django\.venv\Lib\site-packages\django\views\decorators\__init__.py first seen with mtime 1727602615.912289
File D:\Django\.venv\Lib\site-packages\django\core\files\storage\filesystem.py first seen with mtime 1727602615.7167256
File D:\Django\.venv\Lib\site-packages\django\utils\encoding.py first seen with mtime 1727602615.8882942
File D:\Django\.venv\Lib\site-packages\django\template\loaders\__init__.py first seen with mtime 1727602615.8692849
File D:\Django\.venv\Lib\site-packages\requests\sessions.py first seen with mtime 1727626978.900689
File D:\Django\.venv\Lib\site-packages\django\contrib\admin\decorators.py first seen with mtime 1727602614.2912495
File D:\Django\.venv\Lib\site-packages\requests\__init__.py first seen with mtime 1727626978.8966918
File D:\Django\.venv\Lib\site-packages\requests\auth.py first seen with mtime 1727626978.8986907
File D:\Django\.venv\Lib\site-packages\django\contrib\staticfiles\finders.py first seen with mtime 1727602615.6874578
File D:\Django\.venv\Lib\site-packages\django\template\context.py first seen with mtime 1727602615.8612754
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\sslproto.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\core\checks\__init__.py first seen with mtime 1727602615.7026973
File D:\Django\.venv\Lib\site-packages\social_core\strategy.py first seen with mtime 1727626981.5223973
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\concurrent\futures\__init__.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\email\policy.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\utils\safestring.py first seen with mtime 1727602615.8946142
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\secrets.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\core\checks\security\base.py first seen with mtime 1727602615.7112136
File D:\Django\.venv\Lib\site-packages\django\core\files\locks.py first seen with mtime 1727602615.7136369
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\windows_events.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\utils\timezone.py first seen with mtime 1727602615.9056153
File D:\Django\.venv\Lib\site-packages\django\http\__init__.py first seen with mtime 1727602615.8532767
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\email\_parseaddr.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\asgiref\current_thread_executor.py first seen with mtime 1727602613.8908904
File D:\Django\.venv\Lib\site-packages\sqlparse\cli.py first seen with mtime 1727602613.5482774
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\email\parser.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\email\encoders.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\DLLs\_asyncio.pyd first seen with mtime 1666616688.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\base_tasks.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\contrib\admin\apps.py first seen with mtime 1727602614.2902515
File D:\Django\.venv\Lib\site-packages\django\contrib\sites\shortcuts.py first seen with mtime 1727602615.5913322
File D:\Django\.venv\Lib\site-packages\urllib3\util\util.py first seen with mtime 1727626976.0318234
File D:\Django\.venv\Lib\site-packages\django\apps\config.py first seen with mtime 1727602614.0925376
File D:\Django\.venv\Lib\site-packages\django\contrib\staticfiles\management\__init__.py first seen with mtime 1727602615.6909883
File D:\Django\.venv\Lib\site-packages\django\utils\connection.py first seen with mtime 1727602615.884404
File D:\Django\.venv\Lib\site-packages\social_core\backends\base.py first seen with mtime 1727626981.528876
File D:\Django\.venv\Lib\site-packages\django\contrib\admin\views\autocomplete.py first seen with mtime 1727602614.57911
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\runners.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\charset_normalizer\legacy.py first seen with mtime 1727626978.6277256
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\email\feedparser.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\urllib3\util\connection.py first seen with mtime 1727626976.0267918
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\trsock.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\db\models\query.py first seen with mtime 1727602615.7925751
File D:\Django\.venv\Lib\site-packages\sqlparse\tokens.py first seen with mtime 1727602613.551959
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\re\_constants.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\urllib3\contrib\__init__.py first seen with mtime 1727626976.0217931
File D:\Django\.venv\Lib\site-packages\urllib3\util\url.py first seen with mtime 1727626976.0318234
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\shutil.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\zoneinfo\_tzpath.py first seen with mtime 1666616650.0
File D:\Django\.venv\Lib\site-packages\django\contrib\sessions\models.py first seen with mtime 1727602615.482818
File D:\Django\.venv\Lib\site-packages\django\core\checks\templates.py first seen with mtime 1727602615.705082
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\glob.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\contrib\admin\utils.py first seen with mtime 1727602614.296414
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\hmac.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\lzma.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\constants.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\core\__init__.py first seen with mtime 1727602615.693949
File D:\Django\.venv\Lib\site-packages\requests\_internal_utils.py first seen with mtime 1727626978.8976915
File D:\Django\.venv\Lib\site-packages\dateutil\parser\isoparser.py first seen with mtime 1727603103.2014868
File D:\Django\.venv\Lib\site-packages\django\db\models\constraints.py first seen with mtime 1727602615.7861488
File D:\Django\.venv\Lib\site-packages\django\db\migrations\state.py first seen with mtime 1727602615.7806945
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\email\iterators.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\urllib3\util\wait.py first seen with mtime 1727626976.0328226
File D:\Django\.venv\Lib\site-packages\social_core\utils.py first seen with mtime 1727626981.5233994
File D:\Django\.venv\Lib\site-packages\django\core\files\storage\handler.py first seen with mtime 1727602615.717911
File D:\Django\.venv\Lib\site-packages\asgiref\sync.py first seen with mtime 1727602613.8918898
File D:\Django\.venv\Lib\site-packages\django\core\signing.py first seen with mtime 1727602615.6964054
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\operator.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\charset_normalizer\md__mypyc.cp311-win_amd64.pyd first seen with mtime 1727626978.6297264
File D:\Django\.venv\Lib\site-packages\django\core\cache\backends\locmem.py first seen with mtime 1727602615.7014222
File D:\Django\.venv\Lib\site-packages\django\db\models\manager.py first seen with mtime 1727602615.7905731
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\threading.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\db\models\__init__.py first seen with mtime 1727602615.783695
File D:\Django\.venv\Lib\site-packages\django\forms\models.py first seen with mtime 1727602615.813025
File D:\Django\.venv\Lib\site-packages\django\views\decorators\common.py first seen with mtime 1727602615.913288
File D:\Django\.venv\Lib\site-packages\requests\certs.py first seen with mtime 1727626978.8986907
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\json\encoder.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\templatetags\l10n.py first seen with mtime 1727602615.8724754
File D:\Django\.venv\Lib\site-packages\requests\models.py first seen with mtime 1727626978.900689
File D:\Django\.venv\Lib\site-packages\django\contrib\admin\templatetags\admin_list.py first seen with mtime 1727602614.578082
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\hashers.py first seen with mtime 1727602614.6814961
File D:\Django\.venv\Lib\site-packages\django\core\validators.py first seen with mtime 1727602615.6974127
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\importlib\resources\readers.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\base_subprocess.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\core\signals.py first seen with mtime 1727602615.6949499
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\DLLs\_queue.pyd first seen with mtime 1666616686.0
File D:\Django\.venv\Lib\site-packages\django\template\backends\__init__.py first seen with mtime 1727602615.867284
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\struct.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\template\loader_tags.py first seen with mtime 1727602615.8652844
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\sysconfig.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\core\files\storage\__init__.py first seen with mtime 1727602615.7157247
File D:\Django\.venv\Lib\site-packages\django\db\backends\postgresql\schema.py first seen with mtime 1727602615.7700033
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\ctypes\wintypes.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\db\models\signals.py first seen with mtime 1727602615.7925751
File D:\Django\.venv\Lib\site-packages\dateutil\tz\win.py first seen with mtime 1727603103.2041552
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\urllib\response.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\utils\http.py first seen with mtime 1727602615.8916147
File D:\Django\.venv\Lib\site-packages\openid\kvform.py first seen with mtime 1727626979.195357
File D:\Django\.venv\Lib\site-packages\charset_normalizer\__init__.py first seen with mtime 1727626978.6257255
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\fnmatch.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\collections\__init__.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\http\multipartparser.py first seen with mtime 1727602615.8542764
File D:\Django\.venv\Lib\site-packages\django\views\generic\base.py first seen with mtime 1727602615.9167907
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\ssl.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\core\checks\compatibility\django_4_0.py first seen with mtime 1727602615.7072055
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\concurrent\futures\thread.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\core\cache\__init__.py first seen with mtime 1727602615.6974127
File D:\Django\.venv\Lib\site-packages\django\contrib\admin\templatetags\admin_urls.py first seen with mtime 1727602614.578082
File D:\Django\.venv\Lib\site-packages\django\db\backends\base\__init__.py first seen with mtime 1727602615.746444
File D:\Django\.venv\Lib\site-packages\django\core\cache\backends\__init__.py first seen with mtime 1727602615.698414
File D:\Django\.venv\Lib\site-packages\openid\cryptutil.py first seen with mtime 1727626979.1929836
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\tasks.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\http\server.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\db\backends\base\operations.py first seen with mtime 1727602615.749894
File D:\Django\.venv\Lib\site-packages\django\utils\text.py first seen with mtime 1727602615.8956141
File D:\Django\.venv\Lib\site-packages\django\utils\translation\trans_real.py first seen with mtime 1727602615.9092803
File D:\Django\.venv\Lib\site-packages\openid\oidutil.py first seen with mtime 1727626979.1963656
File D:\Django\.venv\Lib\site-packages\django\template\exceptions.py first seen with mtime 1727602615.8632772
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\signal.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\templatetags\tz.py first seen with mtime 1727602615.8738153
File D:\Django\.venv\Lib\site-packages\django\core\mail\utils.py first seen with mtime 1727602615.722195
File D:\Django\.venv\Lib\site-packages\django\contrib\admin\views\main.py first seen with mtime 1727602614.580111
File D:\Django\.venv\Lib\site-packages\django\core\serializers\base.py first seen with mtime 1727602615.7414415
File D:\Django\.venv\Lib\site-packages\django\views\decorators\http.py first seen with mtime 1727602615.9157817
File D:\Django\.venv\Lib\site-packages\django\templatetags\cache.py first seen with mtime 1727602615.8724754
File D:\Django\.venv\Lib\site-packages\django\core\handlers\base.py first seen with mtime 1727602615.7191865
File D:\Django\.venv\Lib\site-packages\django\contrib\admin\views\__init__.py first seen with mtime 1727602614.57911
File D:\Django\.venv\Lib\site-packages\django\contrib\messages\storage\base.py first seen with mtime 1727602615.3053775
File D:\Django\.venv\Lib\site-packages\openid\message.py first seen with mtime 1727626979.195357
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\exceptions.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\dateutil\tz\__init__.py first seen with mtime 1727603103.202698
File D:\Django\.venv\Lib\site-packages\django\conf\__init__.py first seen with mtime 1727602614.0935397
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\_compat_pickle.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\email\quoprimime.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\core\checks\messages.py first seen with mtime 1727602615.703704
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\linecache.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\urllib3\exceptions.py first seen with mtime 1727626976.0197935
File D:\Django\.venv\Lib\site-packages\django\template\loaders\filesystem.py first seen with mtime 1727602615.8712828
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\hashlib.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\contrib\__init__.py first seen with mtime 1727602614.2892513
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\DLLs\_decimal.pyd first seen with mtime 1666616688.0
File D:\Django\.venv\Lib\site-packages\django_extensions\templatetags\indent_text.py first seen with mtime 1727602646.8719614
File D:\Django\.venv\Lib\site-packages\django\contrib\contenttypes\fields.py first seen with mtime 1727602614.841895
File D:\Django\.venv\Lib\site-packages\requests\exceptions.py first seen with mtime 1727626978.8996902
File D:\Django\.venv\Lib\site-packages\idna\idnadata.py first seen with mtime 1727626978.19355
File D:\Django\.venv\Lib\site-packages\django\contrib\contenttypes\admin.py first seen with mtime 1727602614.840895
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\zoneinfo\_common.py first seen with mtime 1666616650.0
File D:\Django\.venv\Lib\site-packages\requests\cookies.py first seen with mtime 1727626978.8996902
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\inspect.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\psycopg2\_ipaddress.py first seen with mtime 1727603057.6538224
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\collections\abc.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\views\static.py first seen with mtime 1727602615.912289
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\graphlib.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\db\models\functions\mixins.py first seen with mtime 1727602615.802677
File D:\Django\.venv\Lib\site-packages\django\utils\_os.py first seen with mtime 1727602615.8813968
File D:\Django\.venv\Lib\site-packages\sqlparse\filters\tokens.py first seen with mtime 1727602613.556826
File D:\Django\.venv\Lib\site-packages\django\db\backends\base\features.py first seen with mtime 1727602615.7488937
File D:\Django\.venv\Lib\site-packages\charset_normalizer\models.py first seen with mtime 1727626978.630725
File D:\Django\.venv\Lib\site-packages\django\db\models\fields\related.py first seen with mtime 1727602615.798315
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\importlib\_abc.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\db\models\fields\generated.py first seen with mtime 1727602615.7959187
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\importlib\metadata\_collections.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\importlib\resources\_common.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\email\generator.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\core\handlers\asgi.py first seen with mtime 1727602615.7191865
File D:\Django\.venv\Lib\site-packages\idna\intranges.py first seen with mtime 1727626978.19355
File D:\Django\.venv\Lib\site-packages\django\core\files\utils.py first seen with mtime 1727602615.7157247
File D:\Django\.venv\Lib\site-packages\social_django\strategy.py first seen with mtime 1727863949.0432472
File D:\Django\.venv\Lib\site-packages\django\template\defaulttags.py first seen with mtime 1727602615.8632772
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\encodings\aliases.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\mixins.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\core\servers\__init__.py first seen with mtime 1727602615.7434402
File D:\Django\.venv\Lib\site-packages\django\db\migrations\__init__.py first seen with mtime 1727602615.7751758
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\nturl2path.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\sqlparse\keywords.py first seen with mtime 1727602613.5495925
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\string.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\importlib\resources\_itertools.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\contrib\sites\__init__.py first seen with mtime 1727602615.58833
File D:\Django\.venv\Lib\site-packages\django_extensions\templatetags\syntax_color.py first seen with mtime 1727602646.8719614
File D:\Django\.venv\Lib\site-packages\urllib3\_collections.py first seen with mtime 1727626976.0167859
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\forms.py first seen with mtime 1727602614.6804962
File D:\Django\.venv\Lib\site-packages\django\contrib\contenttypes\forms.py first seen with mtime 1727602614.8428955
File D:\Django\.venv\Lib\site-packages\django\core\cache\utils.py first seen with mtime 1727602615.698414
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\_markupbase.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\pkgutil.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\urllib3\_base_connection.py first seen with mtime 1727626976.0157435
File D:\Django\.venv\Lib\site-packages\django\db\backends\base\introspection.py first seen with mtime 1727602615.7488937
File D:\Django\.venv\Lib\site-packages\django\utils\dateparse.py first seen with mtime 1727602615.885405
File D:\Django\.venv\Lib\site-packages\django\core\files\temp.py first seen with mtime 1727602615.7136369
File D:\Django\.venv\Lib\site-packages\django\core\exceptions.py first seen with mtime 1727602615.6949499
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\apps.py first seen with mtime 1727864457.7701368
File D:\Django\.venv\Lib\site-packages\django\core\checks\files.py first seen with mtime 1727602615.703704
File D:\Django\.venv\Scripts\cymatics work\cymaticspro\__init__.py first seen with mtime 1727187892.7072968
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\difflib.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\utils\html.py first seen with mtime 1727602615.8906136
File D:\Django\.venv\Lib\site-packages\django\db\models\aggregates.py first seen with mtime 1727602615.783695
File D:\Django\.venv\Lib\site-packages\social_django\apps.py first seen with mtime 1727863949.040166
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\zoneinfo\__init__.py first seen with mtime 1666616650.0
File D:\Django\.venv\Lib\site-packages\django\template\backends\django.py first seen with mtime 1727602615.867284
File D:\Django\.venv\Lib\site-packages\django\utils\translation\reloader.py first seen with mtime 1727602615.9081168
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\xml\etree\ElementPath.py first seen with mtime 1666616650.0
File D:\Django\.venv\Lib\site-packages\dateutil\tz\tz.py first seen with mtime 1727603103.2041552
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\subprocess.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\requests\__version__.py first seen with mtime 1727626978.8976915
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\dis.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\importlib\metadata\_itertools.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\urllib3\util\request.py first seen with mtime 1727626976.02779
File D:\Django\.venv\Lib\site-packages\requests\adapters.py first seen with mtime 1727626978.8976915
File D:\Django\.venv\Lib\site-packages\urllib3\util\ssl_.py first seen with mtime 1727626976.0287907
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\base_futures.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\middleware\__init__.py first seen with mtime 1727602615.8552773
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\email\_encoded_words.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\dateutil\relativedelta.py first seen with mtime 1727603103.1990547
File D:\Django\.venv\Lib\site-packages\django\core\checks\security\csrf.py first seen with mtime 1727602615.7112136
File D:\Django\.venv\Lib\site-packages\django\db\models\fields\json.py first seen with mtime 1727602615.796926
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\getpass.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\DLLs\_ssl.pyd first seen with mtime 1666616688.0
File D:\Django\.venv\Lib\site-packages\django\contrib\admin\widgets.py first seen with mtime 1727602614.296414
File D:\Django\.venv\Lib\site-packages\social_django\utils.py first seen with mtime 1727863949.0442467
File D:\Django\.venv\Lib\site-packages\django\db\migrations\operations\base.py first seen with mtime 1727602615.782693
File D:\Django\.venv\Lib\site-packages\django\utils\decorators.py first seen with mtime 1727602615.8868651
File D:\Django\.venv\Lib\site-packages\sqlparse\filters\others.py first seen with mtime 1727602613.5556333
File D:\Django\.venv\Lib\site-packages\django\utils\formats.py first seen with mtime 1727602615.8896048
File D:\Django\.venv\Lib\site-packages\dateutil\_common.py first seen with mtime 1727603103.1979268
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\uuid.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\logging\__init__.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\re\_casefix.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\db\backends\postgresql\base.py first seen with mtime 1727602615.7669487
File D:\Django\.venv\Lib\site-packages\django\urls\converters.py first seen with mtime 1727602615.8802376
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\locale.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\dateutil\tz\_factories.py first seen with mtime 1727603103.202698
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\__init__.py first seen with mtime 1727602614.6769836
File D:\Django\.venv\Lib\site-packages\django\urls\conf.py first seen with mtime 1727602615.879238
File D:\Django\.venv\Lib\site-packages\django\contrib\contenttypes\__init__.py first seen with mtime 1727602614.840895
File D:\Django\.venv\Lib\site-packages\django\conf\locale\__init__.py first seen with mtime 1727602614.0965385
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\gzip.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\management\__init__.py first seen with mtime 1727602614.833894
File D:\Django\.venv\Lib\site-packages\urllib3\filepost.py first seen with mtime 1727626976.0207932
File D:\Django\.venv\Lib\site-packages\charset_normalizer\constant.py first seen with mtime 1727626978.626726
File D:\Django\.venv\Lib\site-packages\sqlparse\filters\reindent.py first seen with mtime 1727602613.5556333
File D:\Django\.venv\Lib\site-packages\django\contrib\sessions\apps.py first seen with mtime 1727602615.481816
File D:\Django\.venv\Lib\site-packages\requests\hooks.py first seen with mtime 1727626978.8996902
File D:\Django\.venv\Lib\site-packages\social_core\pipeline\utils.py first seen with mtime 1727626981.5893073
File D:\Django\.venv\Lib\site-packages\django\views\csrf.py first seen with mtime 1727602615.9102893
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\token.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\utils\dates.py first seen with mtime 1727602615.8868651
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\email\_header_value_parser.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\heapq.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\core\wsgi.py first seen with mtime 1727602615.6974127
File D:\Django\.venv\Lib\site-packages\idna\package_data.py first seen with mtime 1727626978.194551
File D:\Django\.venv\Lib\site-packages\django\contrib\messages\api.py first seen with mtime 1727602615.302377
File D:\Django\.venv\Lib\site-packages\sqlparse\formatter.py first seen with mtime 1727602613.5495925
File D:\Django\.venv\Lib\site-packages\django\utils\choices.py first seen with mtime 1727602615.8834035
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\mimetypes.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\taskgroups.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\http\request.py first seen with mtime 1727602615.8542764
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\email\_policybase.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\email\header.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\sqlparse\lexer.py first seen with mtime 1727602613.550825
File D:\Django\.venv\Lib\site-packages\django\core\files\move.py first seen with mtime 1727602615.7136369
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\selectors.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\checks.py first seen with mtime 1727602614.679497
File D:\Django\.venv\Lib\site-packages\django\urls\__init__.py first seen with mtime 1727602615.8782284
File D:\Django\.venv\Lib\site-packages\django\contrib\contenttypes\checks.py first seen with mtime 1727602614.841895
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\email\mime\message.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\encodings\__init__.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\utils\cache.py first seen with mtime 1727602615.8834035
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\signals.py first seen with mtime 1727602614.6834962
File D:\Django\.venv\Lib\site-packages\django\core\files\storage\base.py first seen with mtime 1727602615.7167256
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\pathlib.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\conf\urls\static.py first seen with mtime 1727602614.2892513
File D:\Django\.venv\Lib\site-packages\django\db\backends\postgresql\client.py first seen with mtime 1727602615.7679963
File D:\Django\.venv\Lib\site-packages\django\contrib\admin\options.py first seen with mtime 1727602614.2951837
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\email\mime\text.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\importlib\resources\_adapters.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\core\files\storage\mixins.py first seen with mtime 1727602615.717911
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\email\errors.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\dateutil\__init__.py first seen with mtime 1727603103.1968284
File D:\Django\.venv\Lib\site-packages\django\core\checks\registry.py first seen with mtime 1727602615.705082
File D:\Django\.venv\Lib\site-packages\dateutil\_version.py first seen with mtime 1727603103.1979268
File D:\Django\.venv\Lib\site-packages\django\template\utils.py first seen with mtime 1727602615.866285
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\json\scanner.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\openid\store\__init__.py first seen with mtime 1727626979.2033634
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\DLLs\select.pyd first seen with mtime 1666616688.0
File D:\Django\.venv\Lib\site-packages\django\db\__init__.py first seen with mtime 1727602615.7444432
File D:\Django\.venv\Lib\site-packages\django\core\serializers\python.py first seen with mtime 1727602615.742442
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\email\contentmanager.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\urllib\__init__.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django_extensions\templatetags\__init__.py first seen with mtime 1727602646.870962
File D:\Django\.venv\Lib\site-packages\django\contrib\contenttypes\management\__init__.py first seen with mtime 1727602614.9424133
File D:\Django\.venv\Lib\site-packages\django\utils\numberformat.py first seen with mtime 1727602615.8946142
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\base_user.py first seen with mtime 1727602614.6784897
File D:\Django\.venv\Lib\site-packages\requests\status_codes.py first seen with mtime 1727626978.9016902
File D:\Django\.venv\Lib\site-packages\django\utils\asyncio.py first seen with mtime 1727602615.8824036
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\concurrent\__init__.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\sqlparse\__init__.py first seen with mtime 1727602613.5482774
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\socketserver.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\db\models\fields\files.py first seen with mtime 1727602615.7959187
File D:\Django\.venv\Lib\site-packages\django\core\files\base.py first seen with mtime 1727602615.7122135
File D:\Django\.venv\Lib\site-packages\django\views\i18n.py first seen with mtime 1727602615.911289
File D:\Django\.venv\Lib\site-packages\django\utils\autoreload.py first seen with mtime 1727602615.8824036
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\encodings\idna.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\forms\utils.py first seen with mtime 1727602615.8150253
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\importlib\__init__.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\certifi\core.py first seen with mtime 1727626978.8570979
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\email\message.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\sqlparse\engine\grouping.py first seen with mtime 1727602613.553207
File D:\Django\.venv\Lib\site-packages\requests\packages.py first seen with mtime 1727626978.900689
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\textwrap.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\base_events.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\template\autoreload.py first seen with mtime 1727602615.8602757
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\html\__init__.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\requests\utils.py first seen with mtime 1727626978.9016902
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\gettext.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\email\headerregistry.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\psycopg2\errors.py first seen with mtime 1727603057.6815538
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\models.py first seen with mtime 1727602614.682496
File D:\Django\.venv\Lib\site-packages\django\views\decorators\cache.py first seen with mtime 1727602615.913288
File D:\Django\.venv\Lib\site-packages\django\contrib\contenttypes\models.py first seen with mtime 1727602614.8428955
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\http\__init__.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\DLLs\_bz2.pyd first seen with mtime 1666616688.0
File D:\Django\.venv\Lib\site-packages\django\db\models\fields\proxy.py first seen with mtime 1727602615.796926
File D:\Django\.venv\Lib\site-packages\django\utils\inspect.py first seen with mtime 1727602615.8916147
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\queues.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\importlib\resources\_legacy.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\apps\__init__.py first seen with mtime 1727602614.0925376
File D:\Django\.venv\Lib\site-packages\django\utils\datastructures.py first seen with mtime 1727602615.884404
File D:\Django\.venv\Lib\site-packages\django\core\checks\security\__init__.py first seen with mtime 1727602615.7102144
File D:\Django\.venv\Lib\site-packages\django\core\management\base.py first seen with mtime 1727602615.727442
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\timeouts.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\core\checks\model_checks.py first seen with mtime 1727602615.705082
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\xml\etree\cElementTree.py first seen with mtime 1666616650.0
File D:\Django\.venv\Lib\site-packages\django_extensions\templatetags\highlighting.py first seen with mtime 1727602646.8719614
File D:\Django\.venv\Lib\site-packages\django\contrib\admin\templatetags\log.py first seen with mtime 1727602614.57911
File D:\Django\.venv\Lib\site-packages\django\core\checks\database.py first seen with mtime 1727602615.703704
File D:\Django\.venv\Lib\site-packages\django\db\backends\postgresql\creation.py first seen with mtime 1727602615.7679963
File D:\Django\.venv\Lib\site-packages\django\utils\ipv6.py first seen with mtime 1727602615.8926146
File D:\Django\.venv\Lib\site-packages\django\utils\dateformat.py first seen with mtime 1727602615.885405
File D:\Django\.venv\Lib\site-packages\django\db\models\fields\__init__.py first seen with mtime 1727602615.794574
File D:\Django\.venv\Lib\site-packages\django\core\serializers\json.py first seen with mtime 1727602615.7414415
File D:\Django\.venv\Lib\site-packages\django\db\models\sql\__init__.py first seen with mtime 1727602615.804679
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\queue.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\social_core\backends\__init__.py first seen with mtime 1727626981.5233994
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\proactor_events.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\types.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\ipaddress.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\utils\translation\__init__.py first seen with mtime 1727602615.9081168
File D:\Django\.venv\Lib\site-packages\django\db\models\constants.py first seen with mtime 1727602615.7861488
File D:\Django\.venv\Lib\site-packages\django\db\models\functions\datetime.py first seen with mtime 1727602615.801677
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\importlib\metadata\_meta.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\email\mime\__init__.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\ast.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\html\entities.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\threads.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django_extensions\admin\widgets.py first seen with mtime 1727602646.7916312
File D:\Django\.venv\Lib\site-packages\django\middleware\http.py first seen with mtime 1727602615.8582828
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\importlib\abc.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\views\__init__.py first seen with mtime 1727602615.9102893
File D:\Django\.venv\Lib\site-packages\django\utils\functional.py first seen with mtime 1727602615.8906136
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\ctypes\__init__.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\db\models\indexes.py first seen with mtime 1727602615.7895656
File D:\Django\.venv\Lib\site-packages\django\db\models\fields\related_lookups.py first seen with mtime 1727602615.7993228
File D:\Django\.venv\Lib\site-packages\django\db\models\functions\window.py first seen with mtime 1727602615.8036766
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\reprlib.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\pprint.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\views\decorators\debug.py first seen with mtime 1727602615.9142897
File D:\Django\.venv\Lib\site-packages\django\db\models\utils.py first seen with mtime 1727602615.7935755
File D:\Django\.venv\Lib\site-packages\django\db\models\functions\math.py first seen with mtime 1727602615.801677
File D:\Django\.venv\Lib\site-packages\django\templatetags\static.py first seen with mtime 1727602615.8724754
File D:\Django\.venv\Lib\site-packages\requests\api.py first seen with mtime 1727626978.8976915
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\__future__.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\template\loaders\cached.py first seen with mtime 1727602615.8702836
File D:\Django\.venv\Lib\site-packages\django\db\models\expressions.py first seen with mtime 1727602615.7881489
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\email\__init__.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\utils\hashable.py first seen with mtime 1727602615.8906136
File D:\Django\.venv\Lib\site-packages\django\utils\module_loading.py first seen with mtime 1727602615.8936148
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\platform.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\db\models\query_utils.py first seen with mtime 1727602615.7925751
File D:\Django\.venv\Lib\site-packages\charset_normalizer\version.py first seen with mtime 1727626978.6317253
File D:\Django\.venv\Lib\site-packages\django\core\checks\translation.py first seen with mtime 1727602615.7060974
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\events.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\admin.py first seen with mtime 1727602614.6779842
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\logging\handlers.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\contrib\admin\templatetags\__init__.py first seen with mtime 1727602614.5770824
File D:\Django\.venv\Lib\site-packages\django\contrib\messages\__init__.py first seen with mtime 1727602615.302377
File D:\Django\.venv\Lib\site-packages\django\db\backends\base\schema.py first seen with mtime 1727602615.7508948
File D:\Django\.venv\Lib\site-packages\django\contrib\staticfiles\views.py first seen with mtime 1727602615.6899412
File D:\Django\.venv\Lib\site-packages\django\db\backends\ddl_references.py first seen with mtime 1727602615.746444
File D:\Django\.venv\Lib\site-packages\django\db\backends\__init__.py first seen with mtime 1727602615.745445
File D:\Django\.venv\Lib\site-packages\django\template\base.py first seen with mtime 1727602615.8612754
File D:\Django\.venv\Lib\site-packages\django\utils\deprecation.py first seen with mtime 1727602615.8882942
File D:\Django\.venv\Lib\site-packages\certifi\__init__.py first seen with mtime 1727626978.8531017
File D:\Django\.venv\Lib\site-packages\dateutil\parser\__init__.py first seen with mtime 1727603103.2000659
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\views.py first seen with mtime 1727864433.603964
File D:\Django\.venv\Lib\site-packages\django_extensions\__init__.py first seen with mtime 1727602646.7841206
File D:\Django\.venv\Lib\site-packages\social_django\views.py first seen with mtime 1727863949.0442467
File D:\Django\.venv\Lib\site-packages\charset_normalizer\utils.py first seen with mtime 1727626978.630725
File D:\Django\.venv\Lib\site-packages\django\db\models\lookups.py first seen with mtime 1727602615.7905731
File D:\Django\.venv\Lib\site-packages\psycopg2\extras.py first seen with mtime 1727603057.6825452
File D:\Django\.venv\Lib\site-packages\psycopg2\__init__.py first seen with mtime 1727603057.6538224
File D:\Django\.venv\Lib\site-packages\django\contrib\staticfiles\management\commands\__init__.py first seen with mtime 1727602615.6909883
File D:\Django\.venv\Lib\site-packages\django\contrib\staticfiles\__init__.py first seen with mtime 1727602615.6859708
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\urls.py first seen with mtime 1727864338.7572012
File D:\Django\.venv\Lib\site-packages\django\conf\global_settings.py first seen with mtime 1727602614.0945385
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\encodings\cp1252.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\social_django\urls.py first seen with mtime 1727863949.0432472
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\bisect.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\utils\tree.py first seen with mtime 1727602615.9056153
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\DLLs\_socket.pyd first seen with mtime 1666616688.0
File D:\Django\.venv\Lib\site-packages\openid\store\nonce.py first seen with mtime 1727626979.2043648
File D:\Django\.venv\Lib\site-packages\django\views\generic\__init__.py first seen with mtime 1727602615.9167907
File D:\Django\.venv\Lib\site-packages\django\template\loaders\base.py first seen with mtime 1727602615.8702836
File D:\Django\.venv\Lib\site-packages\django\urls\exceptions.py first seen with mtime 1727602615.8802376
File D:\Django\.venv\Lib\site-packages\django\db\backends\signals.py first seen with mtime 1727602615.746444
File D:\Django\.venv\Lib\site-packages\sqlparse\filters\__init__.py first seen with mtime 1727602613.5546234
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\selector_events.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\log.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\password_validation.py first seen with mtime 1727602614.682496
File D:\Django\.venv\Lib\site-packages\django_extensions\templatetags\debugger_tags.py first seen with mtime 1727602646.870962
File D:\Django\.venv\Lib\site-packages\django\contrib\admin\actions.py first seen with mtime 1727602614.2902515
File D:\Django\.venv\Lib\site-packages\django\contrib\staticfiles\checks.py first seen with mtime 1727602615.6874578
File D:\Django\.venv\Lib\site-packages\django\urls\resolvers.py first seen with mtime 1727602615.8802376
File D:\Django\.venv\Lib\site-packages\django\dispatch\dispatcher.py first seen with mtime 1727602615.8100257
File D:\Django\.venv\Lib\site-packages\sqlparse\exceptions.py first seen with mtime 1727602613.5495925
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\tokenize.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\DLLs\unicodedata.pyd first seen with mtime 1666616690.0
File D:\Django\.venv\Lib\site-packages\django\db\transaction.py first seen with mtime 1727602615.745445
File D:\Django\.venv\Lib\site-packages\social_core\actions.py first seen with mtime 1727626981.521399
File D:\Django\.venv\Lib\site-packages\django\core\serializers\__init__.py first seen with mtime 1727602615.740442
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\transports.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\importlib\resources\__init__.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\html\parser.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\copy.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\forms\renderers.py first seen with mtime 1727602615.8140254
File D:\Django\.venv\Lib\site-packages\django\contrib\messages\storage\__init__.py first seen with mtime 1727602615.3053775
File D:\Django\.venv\Lib\site-packages\django\db\migrations\operations\special.py first seen with mtime 1727602615.783695
File D:\Django\.venv\Lib\site-packages\social_core\store.py first seen with mtime 1727626981.5223973
File D:\Django\.venv\Lib\site-packages\django\db\models\enums.py first seen with mtime 1727602615.7871492
File D:\Django\.venv\Lib\site-packages\django\db\migrations\migration.py first seen with mtime 1727602615.7781823
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\email\mime\nonmultipart.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\socket.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\urllib3\_request_methods.py first seen with mtime 1727626976.0167859
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\urllib\error.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\sqlparse\engine\__init__.py first seen with mtime 1727602613.551959
File D:\Django\.venv\Lib\site-packages\charset_normalizer\api.py first seen with mtime 1727626978.626726
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\http\cookies.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\concurrent\futures\_base.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\conf\urls\__init__.py first seen with mtime 1727602614.2882507
File D:\Django\.venv\Lib\site-packages\django\template\loaders\app_directories.py first seen with mtime 1727602615.8692849
File D:\Django\.venv\Lib\site-packages\django\contrib\admin\templatetags\admin_modify.py first seen with mtime 1727602614.578082
File D:\Django\.venv\Lib\site-packages\django\contrib\admin\__init__.py first seen with mtime 1727602614.2902515
File D:\Django\.venv\Lib\site-packages\django_extensions\models.py first seen with mtime 1727602646.787122
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\importlib\metadata\_functools.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\core\paginator.py first seen with mtime 1727602615.6949499
File D:\Django\.venv\Lib\site-packages\sqlparse\filters\output.py first seen with mtime 1727602613.5556333
File D:\Django\.venv\Lib\site-packages\django\utils\version.py first seen with mtime 1727602615.9071076
File D:\Django\.venv\Lib\site-packages\django\forms\widgets.py first seen with mtime 1727602615.8150253
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\importlib\metadata\__init__.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\social_core\__init__.py first seen with mtime 1727626981.5204144
File D:\Django\.venv\Lib\site-packages\django\db\models\base.py first seen with mtime 1727602615.7851403
File D:\Django\.venv\Lib\site-packages\urllib3\connectionpool.py first seen with mtime 1727626976.0187926
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\DLLs\_ctypes.pyd first seen with mtime 1666616688.0
File D:\Django\.venv\Lib\site-packages\django\template\defaultfilters.py first seen with mtime 1727602615.862277
File D:\Django\.venv\Lib\site-packages\django\__init__.py first seen with mtime 1727602614.0905373
File D:\Django\.venv\Lib\site-packages\django\db\backends\postgresql\psycopg_any.py first seen with mtime 1727602615.7700033
File D:\Django\.venv\Lib\site-packages\django\core\checks\async_checks.py first seen with mtime 1727602615.7026973
File D:\Django\.venv\Lib\site-packages\django\utils\termcolors.py first seen with mtime 1727602615.8956141
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\typing.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\db\migrations\utils.py first seen with mtime 1727602615.7816944
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\futures.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\importlib\metadata\_text.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\pickle.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\urllib3\__init__.py first seen with mtime 1727626976.014743
File D:\Django\.venv\Lib\site-packages\sqlparse\filters\aligned_indent.py first seen with mtime 1727602613.5546234
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\streams.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\urllib3\_version.py first seen with mtime 1727626976.0167859
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\wsgiref\headers.py first seen with mtime 1666616650.0
File D:\Django\.venv\Lib\site-packages\django\template\backends\base.py first seen with mtime 1727602615.867284
File D:\Django\.venv\Lib\site-packages\django\contrib\admin\checks.py first seen with mtime 1727602614.2912495
File D:\Django\.venv\Lib\site-packages\django\contrib\admin\helpers.py first seen with mtime 1727602614.2940357
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\validators.py first seen with mtime 1727602614.6834962
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\coroutines.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\templatetags\i18n.py first seen with mtime 1727602615.8724754
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\decorators.py first seen with mtime 1727602614.6804962
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\importlib\resources\abc.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\wsgiref\simple_server.py first seen with mtime 1666616650.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\xml\etree\__init__.py first seen with mtime 1666616650.0
File D:\Django\.venv\Lib\site-packages\urllib3\util\response.py first seen with mtime 1727626976.02779
File D:\Django\.venv\Lib\site-packages\django\views\generic\list.py first seen with mtime 1727602615.9188428
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\weakref.py first seen with mtime 1666616650.0
File D:\Django\.venv\Lib\site-packages\django\views\defaults.py first seen with mtime 1727602615.911289
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\DLLs\pyexpat.pyd first seen with mtime 1666616690.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\wsgiref\__init__.py first seen with mtime 1666616650.0
File D:\Django\.venv\Lib\site-packages\urllib3\util\ssltransport.py first seen with mtime 1727626976.0308154
File D:\Django\.venv\Lib\site-packages\django\template\response.py first seen with mtime 1727602615.866285
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\enum.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\DLLs\_zoneinfo.pyd first seen with mtime 1666616688.0
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\apps.py first seen with mtime 1727602614.6784897
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\zipfile.py first seen with mtime 1666616650.0
File D:\Django\.venv\Lib\site-packages\django\contrib\contenttypes\apps.py first seen with mtime 1727602614.841895
File D:\Django\.venv\Lib\site-packages\django\db\models\fields\mixins.py first seen with mtime 1727602615.796926
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\urllib\parse.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\middleware\cache.py first seen with mtime 1727602615.8562756
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\urllib\request.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\importlib\metadata\_adapters.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\db\models\sql\query.py first seen with mtime 1727602615.8080237
File D:\Django\.venv\Lib\site-packages\charset_normalizer\md.cp311-win_amd64.pyd first seen with mtime 1727626978.62873
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\email\mime\base.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\urllib3\response.py first seen with mtime 1727626976.0217931
File D:\Django\.venv\Lib\site-packages\django\contrib\admin\models.py first seen with mtime 1727602614.2940357
File D:\Django\.venv\Lib\site-packages\django\db\migrations\operations\__init__.py first seen with mtime 1727602615.7816944
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\http\client.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\db\backends\base\base.py first seen with mtime 1727602615.7478826
File D:\Django\.venv\Lib\site-packages\django\core\cache\backends\base.py first seen with mtime 1727602615.698414
File D:\Django\.venv\Lib\site-packages\psycopg2\_range.py first seen with mtime 1727603057.6785593
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\re\_compiler.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\db\models\deletion.py first seen with mtime 1727602615.7871492
File D:\Django\.venv\Lib\site-packages\django\shortcuts.py first seen with mtime 1727602614.0915394
File D:\Django\.venv\Lib\site-packages\django\contrib\staticfiles\management\commands\runserver.py first seen with mtime 1727602615.6924372
File D:\Django\.venv\Lib\site-packages\django\contrib\sessions\base_session.py first seen with mtime 1727602615.481816
File D:\Django\.venv\Lib\site-packages\django\views\generic\edit.py first seen with mtime 1727602615.917791
File D:\Django\.venv\Lib\site-packages\django\db\backends\postgresql\__init__.py first seen with mtime 1727602615.7669487
File D:\Django\.venv\Lib\site-packages\django\db\models\sql\subqueries.py first seen with mtime 1727602615.8080237
File D:\Django\.venv\Lib\site-packages\django\core\checks\security\sessions.py first seen with mtime 1727602615.7122135
File D:\Django\.venv\Lib\site-packages\django\db\backends\postgresql\operations.py first seen with mtime 1727602615.7700033
File D:\Django\.venv\Lib\site-packages\django\core\cache\backends\filebased.py first seen with mtime 1727602615.7004128
File D:\Django\.venv\Lib\site-packages\urllib3\connection.py first seen with mtime 1727626976.017793
File D:\Django\.venv\Lib\site-packages\django\core\mail\__init__.py first seen with mtime 1727602615.7211964
File D:\Django\.venv\Lib\site-packages\requests\compat.py first seen with mtime 1727626978.8986907
File D:\Django\.venv\Lib\site-packages\django\http\response.py first seen with mtime 1727602615.8552773
File D:\Django\.venv\Lib\site-packages\django\core\management\commands\__init__.py first seen with mtime 1727602615.729442
File D:\Django\.venv\Lib\site-packages\django\template\loader.py first seen with mtime 1727602615.8652844
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\quopri.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\contrib\sites\requests.py first seen with mtime 1727602615.5913322
File D:\Django\.venv\Lib\site-packages\django\core\checks\caches.py first seen with mtime 1727602615.7026973
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\_compression.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\contrib\staticfiles\handlers.py first seen with mtime 1727602615.688823
File D:\Django\.venv\Lib\site-packages\django\core\servers\basehttp.py first seen with mtime 1727602615.7444432
File D:\Django\.venv\Lib\site-packages\django\templatetags\__init__.py first seen with mtime 1727602615.8712828
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\admin.py first seen with mtime 1727187892.66168
File D:\Django\.venv\Lib\site-packages\psycopg2\_json.py first seen with mtime 1727603057.6538224
File D:\Django\.venv\Lib\site-packages\urllib3\util\retry.py first seen with mtime 1727626976.0287907
File D:\Django\.venv\Lib\site-packages\sqlparse\sql.py first seen with mtime 1727602613.550825
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\wsgiref\util.py first seen with mtime 1666616650.0
File D:\Django\.venv\Lib\site-packages\django\core\files\storage\memory.py first seen with mtime 1727602615.717911
File D:\Django\.venv\Lib\site-packages\urllib3\util\ssl_match_hostname.py first seen with mtime 1727626976.0297935
File D:\Django\.venv\Lib\site-packages\social_django\admin.py first seen with mtime 1727863949.040166
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\xml\__init__.py first seen with mtime 1666616650.0
File D:\Django\.venv\Lib\site-packages\django\db\migrations\exceptions.py first seen with mtime 1727602615.7761824
File D:\Django\.venv\Lib\site-packages\django\contrib\admin\sites.py first seen with mtime 1727602614.2951837
File D:\Django\.venv\Lib\site-packages\django\core\management\color.py first seen with mtime 1727602615.727442
File D:\Django\.venv\Lib\site-packages\openid\association.py first seen with mtime 1727626979.1929836
File D:\Django\.venv\Lib\site-packages\django\urls\utils.py first seen with mtime 1727602615.8813968
File D:\Django\.venv\Lib\site-packages\django\contrib\admin\templatetags\base.py first seen with mtime 1727602614.57911
File D:\Django\.venv\Lib\site-packages\django\db\backends\base\client.py first seen with mtime 1727602615.7478826
File D:\Django\.venv\Lib\site-packages\_distutils_hack\__init__.py first seen with mtime 1727602484.0375962
File D:\Django\.venv\Lib\site-packages\django\utils\crypto.py first seen with mtime 1727602615.884404
File D:\Django\.venv\Lib\site-packages\sqlparse\engine\statement_splitter.py first seen with mtime 1727602613.553207
File D:\Django\.venv\Scripts\cymatics work\cymaticspro\settings.py first seen with mtime 1727867631.8909812
File D:\Django\.venv\Lib\site-packages\django\forms\formsets.py first seen with mtime 1727602615.813025
File D:\Django\.venv\Lib\site-packages\django\db\utils.py first seen with mtime 1727602615.745445
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\DLLs\_hashlib.pyd first seen with mtime 1666616688.0
File D:\Django\.venv\Lib\site-packages\psycopg2\extensions.py first seen with mtime 1727603057.6825452
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\base64.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\urllib3\poolmanager.py first seen with mtime 1727626976.0207932
File D:\Django\.venv\Lib\site-packages\django\apps\registry.py first seen with mtime 1727602614.0925376
File D:\Django\.venv\Lib\site-packages\django\db\models\fields\reverse_related.py first seen with mtime 1727602615.7993228
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\DLLs\_overlapped.pyd first seen with mtime 1666616688.0
File D:\Django\.venv\Lib\site-packages\sqlparse\utils.py first seen with mtime 1727602613.551959
File D:\Django\.venv\Lib\site-packages\django\db\backends\postgresql\features.py first seen with mtime 1727602615.7690058
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\re\__init__.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\copyreg.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\db\models\options.py first seen with mtime 1727602615.791573
File D:\Django\.venv\Lib\site-packages\django_extensions\admin\__init__.py first seen with mtime 1727602646.7906313
File D:\Django\.venv\Lib\site-packages\dateutil\tz\_common.py first seen with mtime 1727603103.202698
File D:\Django\.venv\Lib\site-packages\django_extensions\templatetags\widont.py first seen with mtime 1727602646.8729608
File D:\Django\.venv\Lib\site-packages\idna\core.py first seen with mtime 1727626978.192553
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\ctypes\_endian.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\staggered.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\random.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\urllib3\util\timeout.py first seen with mtime 1727626976.0318234
File D:\Django\.venv\Scripts\cymatics work\manage.py first seen with mtime 1727187892.710296
File D:\Django\.venv\Lib\site-packages\psycopg2\_psycopg.cp311-win_amd64.pyd first seen with mtime 1727603057.6775193
File D:\Django\.venv\Lib\site-packages\django\core\files\__init__.py first seen with mtime 1727602615.7122135
File D:\Django\.venv\Lib\site-packages\django\core\files\uploadhandler.py first seen with mtime 1727602615.7157247
File D:\Django\.venv\Lib\site-packages\charset_normalizer\cd.py first seen with mtime 1727626978.626726
File D:\Django\.venv\Lib\site-packages\django\db\migrations\operations\models.py first seen with mtime 1727602615.783695
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\re\_parser.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\xml\etree\ElementTree.py first seen with mtime 1666616650.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\functools.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\middleware\csrf.py first seen with mtime 1727602615.8582828
File D:\Django\.venv\Lib\site-packages\django\db\models\sql\constants.py first seen with mtime 1727602615.8058388
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\email\charset.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\contrib\admin\exceptions.py first seen with mtime 1727602614.2925608
File D:\Django\.venv\Lib\site-packages\django\core\files\images.py first seen with mtime 1727602615.7122135
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\_weakrefset.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\db\backends\postgresql\introspection.py first seen with mtime 1727602615.7690058
File D:\Django\.venv\Lib\site-packages\django\db\backends\base\validation.py first seen with mtime 1727602615.7518947
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\tempfile.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\asyncio\locks.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\social_core\exceptions.py first seen with mtime 1727626981.521399
File D:\Django\.venv\Lib\site-packages\django\views\decorators\csrf.py first seen with mtime 1727602615.9142897
File D:\Django\.venv\Lib\site-packages\django\template\engine.py first seen with mtime 1727602615.8632772
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\bz2.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\db\models\functions\text.py first seen with mtime 1727602615.802677
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\json\decoder.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\traceback.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\DLLs\_uuid.pyd first seen with mtime 1666616686.0
File D:\Django\.venv\Lib\site-packages\django\db\backends\base\creation.py first seen with mtime 1727602615.7488937
File D:\Django\.venv\Lib\site-packages\django\forms\fields.py first seen with mtime 1727602615.8120258
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\decimal.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\urllib3\fields.py first seen with mtime 1727626976.0197935
File D:\Django\.venv\Lib\site-packages\django\utils\log.py first seen with mtime 1727602615.8936148
File D:\Django\.venv\Lib\site-packages\django\core\management\__init__.py first seen with mtime 1727602615.7264333
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\calendar.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\http\cookiejar.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\datetime.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\social_core\backends\utils.py first seen with mtime 1727626981.5811844
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\keyword.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\dateutil\parser\_parser.py first seen with mtime 1727603103.2014868
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\argparse.py first seen with mtime 1666616648.0
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\numbers.py first seen with mtime 1666616648.0
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\__init__.py first seen with mtime 1727187892.6543336
File D:\Django\.venv\Lib\site-packages\django\utils\duration.py first seen with mtime 1727602615.8882942
File D:\Django\.venv\Lib\site-packages\asgiref\local.py first seen with mtime 1727602613.8908904
File D:\Django\.venv\Lib\site-packages\social_django\__init__.py first seen with mtime 1727863949.0387347
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\opcode.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\django\utils\deconstruct.py first seen with mtime 1727602615.8868651
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\importlib\readers.py first seen with mtime 1666616648.0
File D:\Django\.venv\Lib\site-packages\openid\store\interface.py first seen with mtime 1727626979.2033634
File D:\Django\.venv\Lib\site-packages\sqlparse\filters\right_margin.py first seen with mtime 1727602613.556826
File D:\Django\.venv\Lib\site-packages\django\template\smartif.py first seen with mtime 1727602615.866285
File D:\Django\.venv\Lib\site-packages\django\core\checks\compatibility\__init__.py first seen with mtime 1727602615.7060974
File D:\Django\.venv\Lib\site-packages\django\core\files\uploadedfile.py first seen with mtime 1727602615.7147162
File D:\Django\.venv\Lib\site-packages\django\views\generic\detail.py first seen with mtime 1727602615.917791
File D:\Django\.venv\Lib\site-packages\django\core\handlers\wsgi.py first seen with mtime 1727602615.7201962
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\DLLs\_elementtree.pyd first seen with mtime 1666616688.0
File D:\Django\.venv\Lib\site-packages\django_extensions\templates\django_extensions first seen with mtime 1727602646.8699608
File D:\Django\.venv\Lib\site-packages\django_extensions\templates\django_extensions\graph_models first seen with mtime 1727602646.8689616
File D:\Django\.venv\Lib\site-packages\django_extensions\templates\django_extensions\widgets first seen with mtime 1727602646.8699608
File D:\Django\.venv\Lib\site-packages\django_extensions\templates\django_extensions\graph_models\django2018 first seen with mtime 1727602646.8689616
File D:\Django\.venv\Lib\site-packages\django_extensions\templates\django_extensions\graph_models\original first seen with mtime 1727602646.8699608
File D:\Django\.venv\Lib\site-packages\django_extensions\templates\django_extensions\graph_models\django2018\digraph.dot first seen with mtime 1727602646.8679607
File D:\Django\.venv\Lib\site-packages\django_extensions\templates\django_extensions\graph_models\django2018\label.dot first seen with mtime 1727602646.8679607
File D:\Django\.venv\Lib\site-packages\django_extensions\templates\django_extensions\graph_models\django2018\relation.dot first seen with mtime 1727602646.8689616
File D:\Django\.venv\Lib\site-packages\django_extensions\templates\django_extensions\graph_models\original\digraph.dot first seen with mtime 1727602646.8689616
File D:\Django\.venv\Lib\site-packages\django_extensions\templates\django_extensions\graph_models\original\label.dot first seen with mtime 1727602646.8699608
File D:\Django\.venv\Lib\site-packages\django_extensions\templates\django_extensions\graph_models\original\relation.dot first seen with mtime 1727602646.8699608
File D:\Django\.venv\Lib\site-packages\django_extensions\templates\django_extensions\widgets\foreignkey_searchinput.html first seen with mtime 1727602646.870962
File D:\Django\.venv\Scripts\cymatics work\templates\allproject.html first seen with mtime 1727502651.2618544
File D:\Django\.venv\Scripts\cymatics work\templates\assetd.html first seen with mtime 1727506056.5346272
File D:\Django\.venv\Scripts\cymatics work\templates\assets.html first seen with mtime 1727502655.0062606
File D:\Django\.venv\Scripts\cymatics work\templates\base.html first seen with mtime 1727187892.7798166
File D:\Django\.venv\Scripts\cymatics work\templates\bookd.html first seen with mtime 1727502550.8480933
File D:\Django\.venv\Scripts\cymatics work\templates\budget.html first seen with mtime 1727187892.7808177
File D:\Django\.venv\Scripts\cymatics work\templates\calendar.html first seen with mtime 1727363900.9581215
File D:\Django\.venv\Scripts\cymatics work\templates\clients.html first seen with mtime 1727502707.5142455
File D:\Django\.venv\Scripts\cymatics work\templates\clientsbook.html first seen with mtime 1727502734.3641753
File D:\Django\.venv\Scripts\cymatics work\templates\clientsd.html first seen with mtime 1727502754.0989707
File D:\Django\.venv\Scripts\cymatics work\templates\dashboard.html first seen with mtime 1727626042.720296
File D:\Django\.venv\Scripts\cymatics work\templates\entertainment.html first seen with mtime 1727503041.1301014
File D:\Django\.venv\Scripts\cymatics work\templates\entertainmentd.html first seen with mtime 1727503071.1713264
File D:\Django\.venv\Scripts\cymatics work\templates\expense.html first seen with mtime 1727503103.7230346
File D:\Django\.venv\Scripts\cymatics work\templates\expensed.html first seen with mtime 1727503174.2784226
File D:\Django\.venv\Scripts\cymatics work\templates\expense_cat.html first seen with mtime 1727457690.6672716
File D:\Django\.venv\Scripts\cymatics work\templates\incomed.html first seen with mtime 1727534208.2049406
File D:\Django\.venv\Scripts\cymatics work\templates\incomef.html first seen with mtime 1727534167.181888
File D:\Django\.venv\Scripts\cymatics work\templates\map.html first seen with mtime 1727505401.0737283
File D:\Django\.venv\Scripts\cymatics work\templates\pending_pay.html first seen with mtime 1727505520.5767634
File D:\Django\.venv\Scripts\cymatics work\templates\profile.html first seen with mtime 1727597643.4151845
File D:\Django\.venv\Scripts\cymatics work\templates\project.html first seen with mtime 1727598095.510163
File D:\Django\.venv\Scripts\cymatics work\templates\projectd.html first seen with mtime 1727505592.0291774
File D:\Django\.venv\Scripts\cymatics work\templates\send_otp.html first seen with mtime 1727864672.817589
File D:\Django\.venv\Scripts\cymatics work\templates\status.html first seen with mtime 1727505629.1285536
File D:\Django\.venv\Scripts\cymatics work\templates\verify_otp.html first seen with mtime 1727606478.5736513
File D:\Django\.venv\Lib\site-packages\django_extensions\locale\da\LC_MESSAGES\django.mo first seen with mtime 1727602646.8181477
File D:\Django\.venv\Lib\site-packages\django_extensions\locale\de\LC_MESSAGES\django.mo first seen with mtime 1727602646.8191476
File D:\Django\.venv\Lib\site-packages\django_extensions\locale\el\LC_MESSAGES\django.mo first seen with mtime 1727602646.820148
File D:\Django\.venv\Lib\site-packages\django_extensions\locale\en\LC_MESSAGES\django.mo first seen with mtime 1727602646.8211484
File D:\Django\.venv\Lib\site-packages\django_extensions\locale\es\LC_MESSAGES\django.mo first seen with mtime 1727602646.822148
File D:\Django\.venv\Lib\site-packages\django_extensions\locale\fr\LC_MESSAGES\django.mo first seen with mtime 1727602646.8231478
File D:\Django\.venv\Lib\site-packages\django_extensions\locale\hu\LC_MESSAGES\django.mo first seen with mtime 1727602646.8251495
File D:\Django\.venv\Lib\site-packages\django_extensions\locale\id\LC_MESSAGES\django.mo first seen with mtime 1727602646.8261483
File D:\Django\.venv\Lib\site-packages\django_extensions\locale\it\LC_MESSAGES\django.mo first seen with mtime 1727602646.828149
File D:\Django\.venv\Lib\site-packages\django_extensions\locale\ja\LC_MESSAGES\django.mo first seen with mtime 1727602646.8291492
File D:\Django\.venv\Lib\site-packages\django_extensions\locale\pl\LC_MESSAGES\django.mo first seen with mtime 1727602646.830149
File D:\Django\.venv\Lib\site-packages\django_extensions\locale\pt\LC_MESSAGES\django.mo first seen with mtime 1727602646.8311486
File D:\Django\.venv\Lib\site-packages\django_extensions\locale\pt_BR\LC_MESSAGES\django.mo first seen with mtime 1727602646.8321478
File D:\Django\.venv\Lib\site-packages\django_extensions\locale\ro\LC_MESSAGES\django.mo first seen with mtime 1727602646.8335996
File D:\Django\.venv\Lib\site-packages\django_extensions\locale\ru\LC_MESSAGES\django.mo first seen with mtime 1727602646.8346074
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0007_expense.py first seen with mtime 1727187892.6626804
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\xml\parsers\expat.py first seen with mtime 1666616650.0
File D:\Django\.venv\Lib\site-packages\django\contrib\sessions\migrations\0001_initial.py first seen with mtime 1727602615.5858998
File D:\Django\.venv\Lib\site-packages\PIL\ExifTags.py first seen with mtime 1727603127.969065
File D:\Django\.venv\Lib\site-packages\django\contrib\sessions\backends\__init__.py first seen with mtime 1727602615.483817
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\__init__.py first seen with mtime 1727187892.6730433
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\backends.py first seen with mtime 1727602614.6784897
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0024_remove_project_client.py first seen with mtime 1727187892.6655061
File D:\Django\.venv\Lib\site-packages\django\contrib\admin\migrations\0003_logentry_add_action_flag_choices.py first seen with mtime 1727602614.497724
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\migrations\0007_alter_validators_add_error_messages.py first seen with mtime 1727602614.8378963
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0010_alter_assets_buy_price_alter_assets_quantity_and_more.py first seen with mtime 1727187892.6626804
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0041_alter_project_code.py first seen with mtime 1727187892.6686437
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\migrations\__init__.py first seen with mtime 1727602614.838895
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0012_alter_project_pending_amt_alter_project_profit.py first seen with mtime 1727187892.6640427
File D:\Django\.venv\Lib\site-packages\PIL\TiffTags.py first seen with mtime 1727603128.0072265
File D:\Django\.venv\Lib\site-packages\social_django\migrations\0013_migrate_extra_data.py first seen with mtime 1727863949.0501487
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0021_project_client.py first seen with mtime 1727187892.6655061
File D:\Django\.venv\Lib\site-packages\django\contrib\admin\migrations\0001_initial.py first seen with mtime 1727602614.497724
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0058_project_out_client_project_out_for.py first seen with mtime 1727187892.6717873
File D:\Django\.venv\Lib\site-packages\django\middleware\common.py first seen with mtime 1727602615.8572757
File D:\Django\.venv\Lib\site-packages\social_django\migrations\0006_partial.py first seen with mtime 1727863949.048148
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0038_alter_project_client.py first seen with mtime 1727187892.6686437
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0049_alter_income_options.py first seen with mtime 1727187892.6707516
File D:\Django\.venv\Lib\site-packages\django\contrib\contenttypes\migrations\0002_remove_content_type_name.py first seen with mtime 1727602614.9444132
File D:\Django\.venv\Lib\site-packages\PIL\_binary.py first seen with mtime 1727603128.012157
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0063_alter_project_out_client_alter_project_out_for.py first seen with mtime 1727187892.6730433
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0011_alter_project_outsourcing_amt.py first seen with mtime 1727187892.6640427
File D:\Django\.venv\Lib\site-packages\social_django\migrations\0016_alter_usersocialauth_extra_data.py first seen with mtime 1727863949.051149
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0050_alter_income_project_code.py first seen with mtime 1727187892.6707516
File D:\Django\.venv\Lib\site-packages\PIL\_imaging.cp311-win_amd64.pyd first seen with mtime 1727603128.0271647
File D:\Django\.venv\Lib\site-packages\django\contrib\contenttypes\migrations\__init__.py first seen with mtime 1727602614.9444132
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0037_alter_project_project_number.py first seen with mtime 1727187892.6686437
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0047_remove_income_image_alter_income_project_code.py first seen with mtime 1727187892.6707516
File D:\Django\.venv\Lib\site-packages\social_django\migrations\0005_auto_20160727_2333.py first seen with mtime 1727863949.047137
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0048_alter_income_project_code.py first seen with mtime 1727187892.6707516
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\migrations\0003_alter_user_email_max_length.py first seen with mtime 1727602614.836894
File D:\Django\.venv\Lib\site-packages\django\contrib\sessions\exceptions.py first seen with mtime 1727602615.482818
File D:\Django\.venv\Lib\site-packages\django\db\migrations\graph.py first seen with mtime 1727602615.7771833
File D:\Django\.venv\Lib\site-packages\PIL\Image.py first seen with mtime 1727603127.978823
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0059_alter_project_out_client_alter_project_out_for.py first seen with mtime 1727187892.6730433
File D:\Django\.venv\Lib\site-packages\PIL\_deprecate.py first seen with mtime 1727603128.012157
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0043_alter_project_outsourcing_paid.py first seen with mtime 1727187892.6697452
File D:\Django\.venv\Lib\site-packages\django\contrib\sessions\middleware.py first seen with mtime 1727602615.482818
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0057_alter_assets_image.py first seen with mtime 1727187892.6717873
File D:\Django\.venv\Lib\site-packages\django\contrib\sessions\backends\base.py first seen with mtime 1727602615.484816
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\migrations\0005_alter_user_last_login_null.py first seen with mtime 1727602614.836894
File D:\Django\.venv\Lib\site-packages\PIL\_util.py first seen with mtime 1727603128.0491717
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0001_initial.py first seen with mtime 1727187892.66168
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\migrations\0010_alter_group_name_max_length.py first seen with mtime 1727602614.838895
File D:\Django\.venv\Lib\site-packages\social_django\migrations\0011_alter_id_fields.py first seen with mtime 1727863949.0491483
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0064_calendarevent.py first seen with mtime 1727363900.9491215
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0018_project_client.py first seen with mtime 1727187892.6640427
File D:\Django\.venv\Lib\site-packages\social_django\migrations\0010_uid_db_index.py first seen with mtime 1727863949.0491483
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0028_project_client.py first seen with mtime 1727187892.6665459
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0032_alter_expense_project_code.py first seen with mtime 1727187892.6675527
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\migrations\0001_initial.py first seen with mtime 1727602614.8358946
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0052_alter_income_project_code.py first seen with mtime 1727187892.6707516
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0033_rename_income_income_project_income.py first seen with mtime 1727187892.6675527
File D:\Django\.venv\Lib\site-packages\social_django\migrations\0015_rename_extra_data_new_usersocialauth_extra_data.py first seen with mtime 1727863949.051149
File D:\Django\.venv\Lib\site-packages\django\db\migrations\recorder.py first seen with mtime 1727602615.7796936
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\views.py first seen with mtime 1727602614.6844964
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\migrations\0011_update_proxy_permissions.py first seen with mtime 1727602614.838895
File D:\Django\.venv\Lib\site-packages\cffi\error.py first seen with mtime 1727626979.8549714
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0065_emailotp.py first seen with mtime 1727604289.3545005
File D:\Django\.venv\Lib\site-packages\social_django\migrations\0002_add_related_name.py first seen with mtime 1727863949.0457432
File D:\Django\.venv\Lib\site-packages\django\contrib\sessions\backends\db.py first seen with mtime 1727602615.4858172
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0060_project_latitude_project_longitude_and_more.py first seen with mtime 1727187892.6730433
File D:\Django\.venv\Lib\site-packages\defusedxml\common.py first seen with mtime 1727626978.443513
File D:\Django\.venv\Lib\site-packages\social_django\migrations\0009_auto_20191118_0520.py first seen with mtime 1727863949.0491483
File D:\Django\.venv\Lib\site-packages\django\db\migrations\loader.py first seen with mtime 1727602615.7771833
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\migrations\0004_alter_user_username_opts.py first seen with mtime 1727602614.836894
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0042_alter_project_address_alter_project_expenses_and_more.py first seen with mtime 1727187892.6697452
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0023_alter_project_client.py first seen with mtime 1727187892.6655061
File D:\Django\.venv\Lib\site-packages\PIL\_version.py first seen with mtime 1727603128.0491717
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0040_project_client.py first seen with mtime 1727187892.6686437
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0005_rename_clients_client.py first seen with mtime 1727187892.6626804
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0061_alter_expense_project_expense_and_more.py first seen with mtime 1727187892.6730433
File D:\Django\.venv\Lib\site-packages\django\middleware\security.py first seen with mtime 1727602615.8592775
File D:\Django\.venv\Lib\site-packages\django\contrib\contenttypes\migrations\0001_initial.py first seen with mtime 1727602614.9434133
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0062_project_out_comp_project_out_num.py first seen with mtime 1727187892.6730433
File D:\Django\.venv\Lib\site-packages\defusedxml\ElementTree.py first seen with mtime 1727626978.4415112
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0051_alter_income_project_code.py first seen with mtime 1727187892.6707516
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0026_alter_project_client.py first seen with mtime 1727187892.6665459
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0014_remove_project_count.py first seen with mtime 1727187892.6640427
File D:\Django\.venv\Lib\site-packages\cffi\model.py first seen with mtime 1727626979.8559713
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0017_remove_project_client.py first seen with mtime 1727187892.6640427
File D:\Django\.venv\Lib\site-packages\social_django\migrations\0014_remove_usersocialauth_extra_data.py first seen with mtime 1727863949.0501487
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0004_rename_amount_project_amount_and_more.py first seen with mtime 1727187892.66168
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0046_remove_project_expenses.py first seen with mtime 1727187892.6697452
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0066_delete_emailotp.py first seen with mtime 1727612758.9494019
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0003_project_alter_clients_number.py first seen with mtime 1727187892.66168
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0045_alter_expense_project_code.py first seen with mtime 1727187892.6697452
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0022_alter_project_client.py first seen with mtime 1727187892.6655061
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0067_emailotp.py first seen with mtime 1727612783.45826
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0009_assets_project_outsourcing_amt_and_more.py first seen with mtime 1727187892.6626804
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0025_project_client.py first seen with mtime 1727187892.6655061
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0029_entertainment.py first seen with mtime 1727187892.6665459
File D:\Django\.venv\Lib\site-packages\social_django\migrations\__init__.py first seen with mtime 1727863949.051149
File D:\Django\.venv\Scripts\cymatics work\cymaticspro\wsgi.py first seen with mtime 1727187892.7092955
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\middleware.py first seen with mtime 1727602614.6814961
File D:\Django\.venv\Lib\site-packages\cffi\api.py first seen with mtime 1727626979.8539636
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0013_project_count.py first seen with mtime 1727187892.6640427
File D:\Django\.venv\Lib\site-packages\PIL\ImageMode.py first seen with mtime 1727603127.985832
File D:\Django\.venv\Lib\site-packages\defusedxml\__init__.py first seen with mtime 1727626978.442513
File D:\Django\.venv\Lib\site-packages\PIL\_typing.py first seen with mtime 1727603128.0481718
File C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.0\Lib\xml\parsers\__init__.py first seen with mtime 1666616650.0
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\migrations\0009_alter_user_last_name_max_length.py first seen with mtime 1727602614.8378963
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0030_alter_entertainment_date.py first seen with mtime 1727187892.6665459
File D:\Django\.venv\Lib\site-packages\django\db\migrations\executor.py first seen with mtime 1727602615.7761824
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0053_remove_income_project_code.py first seen with mtime 1727187892.6717873
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0002_alter_clients_email_alter_clients_img_and_more.py first seen with mtime 1727187892.66168
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0056_alter_entertainment_date.py first seen with mtime 1727187892.6717873
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0016_alter_project_client.py first seen with mtime 1727187892.6640427
File D:\Django\.venv\Lib\site-packages\django\contrib\sessions\migrations\__init__.py first seen with mtime 1727602615.5858998
File D:\Django\.venv\Lib\site-packages\social_django\migrations\0012_usersocialauth_extra_data_new.py first seen with mtime 1727863949.0501487
File D:\Django\.venv\Lib\site-packages\django\db\models\sql\compiler.py first seen with mtime 1727602615.8058388
File D:\Django\.venv\Lib\site-packages\django\contrib\admin\migrations\0002_logentry_remove_auto_add.py first seen with mtime 1727602614.497724
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0054_income_project_code.py first seen with mtime 1727187892.6717873
File D:\Django\.venv\Lib\site-packages\PIL\__init__.py first seen with mtime 1727603128.0116472
File D:\Django\.venv\Lib\site-packages\social_django\migrations\0008_partial_timestamp.py first seen with mtime 1727863949.048148
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\migrations\0008_alter_user_username_max_length.py first seen with mtime 1727602614.8378963
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0027_remove_project_client.py first seen with mtime 1727187892.6665459
File D:\Django\.venv\Lib\site-packages\cffi\__init__.py first seen with mtime 1727626979.8518133
File D:\Django\.venv\Lib\site-packages\django\contrib\messages\middleware.py first seen with mtime 1727602615.3033774
File D:\Django\.venv\Lib\site-packages\django\contrib\admin\migrations\__init__.py first seen with mtime 1727602614.497724
File D:\Django\.venv\Lib\site-packages\cffi\lock.py first seen with mtime 1727626979.8559713
File D:\Django\.venv\Lib\site-packages\social_django\fields.py first seen with mtime 1727863949.0411773
File D:\Django\.venv\Lib\site-packages\django\middleware\clickjacking.py first seen with mtime 1727602615.8562756
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0035_project_project_number.py first seen with mtime 1727187892.6675527
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\migrations\0006_require_contenttypes_0002.py first seen with mtime 1727602614.8378963
File D:\Django\.venv\Lib\site-packages\social_django\middleware.py first seen with mtime 1727863949.042235
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0008_remove_expense_note.py first seen with mtime 1727187892.6626804
File D:\Django\.venv\Lib\site-packages\social_django\migrations\0001_initial.py first seen with mtime 1727863949.0457432
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0006_income.py first seen with mtime 1727187892.6626804
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\migrations\0012_alter_user_first_name_max_length.py first seen with mtime 1727602614.838895
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0039_remove_project_client_remove_project_project_number.py first seen with mtime 1727187892.6686437
File D:\Django\.venv\Lib\site-packages\social_django\migrations\0004_auto_20160423_0400.py first seen with mtime 1727863949.047137
File D:\Django\.venv\Lib\site-packages\social_django\migrations\0007_code_timestamp.py first seen with mtime 1727863949.048148
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0055_expense_notes.py first seen with mtime 1727187892.6717873
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0044_alter_income_project_code.py first seen with mtime 1727187892.6697452
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0034_alter_project_code.py first seen with mtime 1727187892.6675527
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0020_remove_project_client.py first seen with mtime 1727187892.6655061
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0019_alter_project_client.py first seen with mtime 1727187892.6655061
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0031_rename_expense_expense_project_expense.py first seen with mtime 1727187892.6675527
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0015_project_client.py first seen with mtime 1727187892.6640427
File D:\Django\.venv\Lib\site-packages\social_django\migrations\0003_alter_email_max_length.py first seen with mtime 1727863949.0457432
File D:\Django\.venv\Scripts\cymatics work\cymaticsapp\migrations\0036_alter_project_project_number.py first seen with mtime 1727187892.6675527
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\migrations\0002_alter_permission_name_max_length.py first seen with mtime 1727602614.8358946
(0.000) SELECT "django_session"."session_key", "django_session"."session_data", "django_session"."expire_date" FROM "django_session" WHERE ("django_session"."expire_date" > '2024-10-02T11:14:04.464455+00:00'::timestamptz AND "django_session"."session_key" = '3fkdscepuw4i2z757dti1s182f9la4mq') LIMIT 21; args=(datetime.datetime(2024, 10, 2, 11, 14, 4, 464455, tzinfo=datetime.timezone.utc), '3fkdscepuw4i2z757dti1s182f9la4mq'); alias=default
"GET / HTTP/1.1" 200 4200
Exception while resolving variable 'name' in template 'unknown'.
Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Django\.venv\Lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    callback, callback_args, callback_kwargs = self.resolve_request(request)
                                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Django\.venv\Lib\site-packages\django\core\handlers\base.py", line 313, in resolve_request
    resolver_match = resolver.resolve(request.path_info)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Django\.venv\Lib\site-packages\django\urls\resolvers.py", line 705, in resolve
    raise Resolver404({"tried": tried, "path": new_path})
django.urls.exceptions.Resolver404: {'tried': [[<URLResolver <URLPattern list> (admin:admin) 'admin/'>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern '' [name='send_otp']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'clients' [name='clients']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'projects/' [name='projects']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_model_data/<str:code>/' [name='get_model_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_model/<str:code>/' [name='edit_model']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'project/<str:code>/' [name='projectd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_model_data/<str:code>/' [name='get_model_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_model/<str:code>/' [name='edit_model']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'delete_project/<int:project_id>/' [name='delete_project']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'income' [name='incomef_view']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_income_data/<int:inc_id>/' [name='get_income_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_income/<int:inc_id>/' [name='edit_income']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'expense' [name='expense']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'assets' [name='assets']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'clientsbook' [name='clientsbook']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'bookd/<str:company>/' [name='bookd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'status' [name='status']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'pending_pay' [name='pending_pay']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'entertainment' [name='entertainment']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'allproject' [name='allproject']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'budget' [name='budget']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'dashboard' [name='dashboard']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_expense_data/<int:exp_id>/' [name='get_expense_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_expense/<int:exp_id>/' [name='edit_expense']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_entertainment_data/<int:ent_id>/' [name='get_entertainment_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_entertainment/<int:ent_id>/' [name='edit_entertainment']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'delete_entertainment/<int:ent_id>/' [name='delete_entertainment']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'entertainmentd/<int:ent_id>/' [name='entertainmentd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_client_data/<int:clt_id>/' [name='get_client_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_client/<int:clt_id>/' [name='edit_client']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'clientd/<str:name>/' [name='clientd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_asset_data/<int:ast_id>/' [name='get_asset_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_asset/<int:ast_id>/' [name='edit_asset']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'assetd/<int:ast_id>/' [name='assetd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'delete_asset/<int:ast_id>/' [name='delete_asset']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_project_codes/' [name='get_project_codes']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'map/' [name='project_map']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'expense/<int:expense_id>/' [name='expense_detail']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'save-expense-notes/' [name='save_expense_notes']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'income/<int:income_id>/' [name='income_detail']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'save-income-notes/' [name='save_income_notes']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'save-item/' [name='save_item']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get-unique-types/' [name='get_unique_types']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get-unique-company/' [name='get_unique_company']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get-unique-client/' [name='get_unique_client']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get-unique-category/' [name='get_unique_category']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'total-expense/' [name='t_exp_in']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'calendar/' [name='calendar_view']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'api/events/' [name='get_events']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'api/events/add/' [name='add_event']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'verification' [name='verify_otp']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'logout' [name='logout']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'profile' [name='profile']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLResolver <module 'social_django.urls' from 'D:\\Django\\.venv\\Lib\\site-packages\\social_django\\urls.py'> (social:social) 'auth/'>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'login/' [name='login']>], [<URLPattern '^media/(?P<path>.*)$'>]], 'path': 'styles.css'}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\template\base.py", line 883, in _resolve_lookup
    current = current[bit]
              ~~~~~~~^^^^^
TypeError: 'URLResolver' object is not subscriptable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\template\base.py", line 893, in _resolve_lookup
    current = getattr(current, bit)
              ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'URLResolver' object has no attribute 'name'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\template\base.py", line 899, in _resolve_lookup
    current = current[int(bit)]
                      ^^^^^^^^
ValueError: invalid literal for int() with base 10: 'name'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\template\base.py", line 906, in _resolve_lookup
    raise VariableDoesNotExist(
django.template.base.VariableDoesNotExist: Failed lookup for key [name] in <URLResolver <URLPattern list> (admin:admin) 'admin/'>
"GET /static/images/logowhite.png HTTP/1.1" 304 0
Exception while resolving variable 'name' in template 'unknown'.
Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Django\.venv\Lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    callback, callback_args, callback_kwargs = self.resolve_request(request)
                                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Django\.venv\Lib\site-packages\django\core\handlers\base.py", line 313, in resolve_request
    resolver_match = resolver.resolve(request.path_info)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Django\.venv\Lib\site-packages\django\urls\resolvers.py", line 705, in resolve
    raise Resolver404({"tried": tried, "path": new_path})
django.urls.exceptions.Resolver404: {'tried': [[<URLResolver <URLPattern list> (admin:admin) 'admin/'>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern '' [name='send_otp']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'clients' [name='clients']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'projects/' [name='projects']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_model_data/<str:code>/' [name='get_model_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_model/<str:code>/' [name='edit_model']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'project/<str:code>/' [name='projectd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_model_data/<str:code>/' [name='get_model_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_model/<str:code>/' [name='edit_model']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'delete_project/<int:project_id>/' [name='delete_project']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'income' [name='incomef_view']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_income_data/<int:inc_id>/' [name='get_income_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_income/<int:inc_id>/' [name='edit_income']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'expense' [name='expense']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'assets' [name='assets']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'clientsbook' [name='clientsbook']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'bookd/<str:company>/' [name='bookd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'status' [name='status']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'pending_pay' [name='pending_pay']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'entertainment' [name='entertainment']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'allproject' [name='allproject']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'budget' [name='budget']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'dashboard' [name='dashboard']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_expense_data/<int:exp_id>/' [name='get_expense_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_expense/<int:exp_id>/' [name='edit_expense']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_entertainment_data/<int:ent_id>/' [name='get_entertainment_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_entertainment/<int:ent_id>/' [name='edit_entertainment']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'delete_entertainment/<int:ent_id>/' [name='delete_entertainment']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'entertainmentd/<int:ent_id>/' [name='entertainmentd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_client_data/<int:clt_id>/' [name='get_client_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_client/<int:clt_id>/' [name='edit_client']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'clientd/<str:name>/' [name='clientd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_asset_data/<int:ast_id>/' [name='get_asset_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_asset/<int:ast_id>/' [name='edit_asset']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'assetd/<int:ast_id>/' [name='assetd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'delete_asset/<int:ast_id>/' [name='delete_asset']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_project_codes/' [name='get_project_codes']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'map/' [name='project_map']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'expense/<int:expense_id>/' [name='expense_detail']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'save-expense-notes/' [name='save_expense_notes']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'income/<int:income_id>/' [name='income_detail']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'save-income-notes/' [name='save_income_notes']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'save-item/' [name='save_item']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get-unique-types/' [name='get_unique_types']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get-unique-company/' [name='get_unique_company']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get-unique-client/' [name='get_unique_client']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get-unique-category/' [name='get_unique_category']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'total-expense/' [name='t_exp_in']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'calendar/' [name='calendar_view']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'api/events/' [name='get_events']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'api/events/add/' [name='add_event']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'verification' [name='verify_otp']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'logout' [name='logout']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'profile' [name='profile']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLResolver <module 'social_django.urls' from 'D:\\Django\\.venv\\Lib\\site-packages\\social_django\\urls.py'> (social:social) 'auth/'>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'login/' [name='login']>], [<URLPattern '^media/(?P<path>.*)$'>]], 'path': 'styles.css'}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\template\base.py", line 883, in _resolve_lookup
    current = current[bit]
              ~~~~~~~^^^^^
TypeError: 'URLResolver' object is not subscriptable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\template\base.py", line 893, in _resolve_lookup
    current = getattr(current, bit)
              ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'URLResolver' object has no attribute 'name'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\template\base.py", line 899, in _resolve_lookup
    current = current[int(bit)]
                      ^^^^^^^^
ValueError: invalid literal for int() with base 10: 'name'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\template\base.py", line 906, in _resolve_lookup
    raise VariableDoesNotExist(
django.template.base.VariableDoesNotExist: Failed lookup for key [name] in <URLResolver <module 'social_django.urls' from 'D:\\Django\\.venv\\Lib\\site-packages\\social_django\\urls.py'> (social:social) 'auth/'>
Not Found: /styles.css
"GET /styles.css HTTP/1.1" 404 17408
File D:\Django\.venv\Lib\site-packages\django\contrib\auth\context_processors.py first seen with mtime 1727602614.6804962
File D:\Django\.venv\Lib\site-packages\django\contrib\sessions\serializers.py first seen with mtime 1727602615.483817
File D:\Django\.venv\Lib\site-packages\django\contrib\messages\context_processors.py first seen with mtime 1727602615.3033774
File D:\Django\.venv\Lib\site-packages\django\contrib\messages\storage\fallback.py first seen with mtime 1727602615.3063772
File D:\Django\.venv\Lib\site-packages\django\contrib\messages\storage\cookie.py first seen with mtime 1727602615.3053775
File D:\Django\.venv\Lib\site-packages\django\template\context_processors.py first seen with mtime 1727602615.8612754
File D:\Django\.venv\Lib\site-packages\django\contrib\messages\storage\session.py first seen with mtime 1727602615.3063772
File D:\Django\.venv\Lib\site-packages\django\contrib\staticfiles\storage.py first seen with mtime 1727602615.688823
Exception while resolving variable 'name' in template 'unknown'.
Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Django\.venv\Lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    callback, callback_args, callback_kwargs = self.resolve_request(request)
                                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Django\.venv\Lib\site-packages\django\core\handlers\base.py", line 313, in resolve_request
    resolver_match = resolver.resolve(request.path_info)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Django\.venv\Lib\site-packages\django\urls\resolvers.py", line 705, in resolve
    raise Resolver404({"tried": tried, "path": new_path})
django.urls.exceptions.Resolver404: {'tried': [[<URLResolver <URLPattern list> (admin:admin) 'admin/'>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern '' [name='send_otp']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'clients' [name='clients']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'projects/' [name='projects']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_model_data/<str:code>/' [name='get_model_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_model/<str:code>/' [name='edit_model']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'project/<str:code>/' [name='projectd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_model_data/<str:code>/' [name='get_model_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_model/<str:code>/' [name='edit_model']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'delete_project/<int:project_id>/' [name='delete_project']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'income' [name='incomef_view']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_income_data/<int:inc_id>/' [name='get_income_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_income/<int:inc_id>/' [name='edit_income']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'expense' [name='expense']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'assets' [name='assets']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'clientsbook' [name='clientsbook']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'bookd/<str:company>/' [name='bookd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'status' [name='status']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'pending_pay' [name='pending_pay']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'entertainment' [name='entertainment']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'allproject' [name='allproject']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'budget' [name='budget']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'dashboard' [name='dashboard']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_expense_data/<int:exp_id>/' [name='get_expense_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_expense/<int:exp_id>/' [name='edit_expense']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_entertainment_data/<int:ent_id>/' [name='get_entertainment_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_entertainment/<int:ent_id>/' [name='edit_entertainment']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'delete_entertainment/<int:ent_id>/' [name='delete_entertainment']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'entertainmentd/<int:ent_id>/' [name='entertainmentd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_client_data/<int:clt_id>/' [name='get_client_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_client/<int:clt_id>/' [name='edit_client']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'clientd/<str:name>/' [name='clientd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_asset_data/<int:ast_id>/' [name='get_asset_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_asset/<int:ast_id>/' [name='edit_asset']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'assetd/<int:ast_id>/' [name='assetd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'delete_asset/<int:ast_id>/' [name='delete_asset']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_project_codes/' [name='get_project_codes']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'map/' [name='project_map']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'expense/<int:expense_id>/' [name='expense_detail']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'save-expense-notes/' [name='save_expense_notes']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'income/<int:income_id>/' [name='income_detail']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'save-income-notes/' [name='save_income_notes']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'save-item/' [name='save_item']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get-unique-types/' [name='get_unique_types']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get-unique-company/' [name='get_unique_company']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get-unique-client/' [name='get_unique_client']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get-unique-category/' [name='get_unique_category']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'total-expense/' [name='t_exp_in']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'calendar/' [name='calendar_view']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'api/events/' [name='get_events']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'api/events/add/' [name='add_event']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'verification' [name='verify_otp']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'logout' [name='logout']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'profile' [name='profile']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLResolver <module 'social_django.urls' from 'D:\\Django\\.venv\\Lib\\site-packages\\social_django\\urls.py'> (social:social) 'auth/'>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'login/' [name='login']>], [<URLPattern '^media/(?P<path>.*)$'>]], 'path': 'favicon.ico'}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\template\base.py", line 883, in _resolve_lookup
    current = current[bit]
              ~~~~~~~^^^^^
TypeError: 'URLResolver' object is not subscriptable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\template\base.py", line 893, in _resolve_lookup
    current = getattr(current, bit)
              ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'URLResolver' object has no attribute 'name'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\template\base.py", line 899, in _resolve_lookup
    current = current[int(bit)]
                      ^^^^^^^^
ValueError: invalid literal for int() with base 10: 'name'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\template\base.py", line 906, in _resolve_lookup
    raise VariableDoesNotExist(
django.template.base.VariableDoesNotExist: Failed lookup for key [name] in <URLResolver <URLPattern list> (admin:admin) 'admin/'>
Exception while resolving variable 'name' in template 'unknown'.
Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Django\.venv\Lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    callback, callback_args, callback_kwargs = self.resolve_request(request)
                                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Django\.venv\Lib\site-packages\django\core\handlers\base.py", line 313, in resolve_request
    resolver_match = resolver.resolve(request.path_info)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Django\.venv\Lib\site-packages\django\urls\resolvers.py", line 705, in resolve
    raise Resolver404({"tried": tried, "path": new_path})
django.urls.exceptions.Resolver404: {'tried': [[<URLResolver <URLPattern list> (admin:admin) 'admin/'>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern '' [name='send_otp']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'clients' [name='clients']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'projects/' [name='projects']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_model_data/<str:code>/' [name='get_model_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_model/<str:code>/' [name='edit_model']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'project/<str:code>/' [name='projectd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_model_data/<str:code>/' [name='get_model_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_model/<str:code>/' [name='edit_model']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'delete_project/<int:project_id>/' [name='delete_project']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'income' [name='incomef_view']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_income_data/<int:inc_id>/' [name='get_income_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_income/<int:inc_id>/' [name='edit_income']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'expense' [name='expense']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'assets' [name='assets']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'clientsbook' [name='clientsbook']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'bookd/<str:company>/' [name='bookd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'status' [name='status']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'pending_pay' [name='pending_pay']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'entertainment' [name='entertainment']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'allproject' [name='allproject']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'budget' [name='budget']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'dashboard' [name='dashboard']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_expense_data/<int:exp_id>/' [name='get_expense_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_expense/<int:exp_id>/' [name='edit_expense']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_entertainment_data/<int:ent_id>/' [name='get_entertainment_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_entertainment/<int:ent_id>/' [name='edit_entertainment']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'delete_entertainment/<int:ent_id>/' [name='delete_entertainment']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'entertainmentd/<int:ent_id>/' [name='entertainmentd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_client_data/<int:clt_id>/' [name='get_client_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_client/<int:clt_id>/' [name='edit_client']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'clientd/<str:name>/' [name='clientd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_asset_data/<int:ast_id>/' [name='get_asset_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_asset/<int:ast_id>/' [name='edit_asset']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'assetd/<int:ast_id>/' [name='assetd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'delete_asset/<int:ast_id>/' [name='delete_asset']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_project_codes/' [name='get_project_codes']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'map/' [name='project_map']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'expense/<int:expense_id>/' [name='expense_detail']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'save-expense-notes/' [name='save_expense_notes']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'income/<int:income_id>/' [name='income_detail']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'save-income-notes/' [name='save_income_notes']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'save-item/' [name='save_item']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get-unique-types/' [name='get_unique_types']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get-unique-company/' [name='get_unique_company']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get-unique-client/' [name='get_unique_client']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get-unique-category/' [name='get_unique_category']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'total-expense/' [name='t_exp_in']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'calendar/' [name='calendar_view']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'api/events/' [name='get_events']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'api/events/add/' [name='add_event']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'verification' [name='verify_otp']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'logout' [name='logout']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'profile' [name='profile']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLResolver <module 'social_django.urls' from 'D:\\Django\\.venv\\Lib\\site-packages\\social_django\\urls.py'> (social:social) 'auth/'>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'login/' [name='login']>], [<URLPattern '^media/(?P<path>.*)$'>]], 'path': 'favicon.ico'}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\template\base.py", line 883, in _resolve_lookup
    current = current[bit]
              ~~~~~~~^^^^^
TypeError: 'URLResolver' object is not subscriptable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\template\base.py", line 893, in _resolve_lookup
    current = getattr(current, bit)
              ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'URLResolver' object has no attribute 'name'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\template\base.py", line 899, in _resolve_lookup
    current = current[int(bit)]
                      ^^^^^^^^
ValueError: invalid literal for int() with base 10: 'name'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\template\base.py", line 906, in _resolve_lookup
    raise VariableDoesNotExist(
django.template.base.VariableDoesNotExist: Failed lookup for key [name] in <URLResolver <module 'social_django.urls' from 'D:\\Django\\.venv\\Lib\\site-packages\\social_django\\urls.py'> (social:social) 'auth/'>
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 17411
(0.000) SELECT "django_session"."session_key", "django_session"."session_data", "django_session"."expire_date" FROM "django_session" WHERE ("django_session"."expire_date" > '2024-10-02T11:14:08.303273+00:00'::timestamptz AND "django_session"."session_key" = '3fkdscepuw4i2z757dti1s182f9la4mq') LIMIT 21; args=(datetime.datetime(2024, 10, 2, 11, 14, 8, 303273, tzinfo=datetime.timezone.utc), '3fkdscepuw4i2z757dti1s182f9la4mq'); alias=default
"GET /auth/login/google-oauth2/ HTTP/1.1" 302 0
File D:\Django\.venv\Lib\site-packages\oauthlib\uri_validate.py first seen with mtime 1727626977.1322927
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth1\__init__.py first seen with mtime 1727626977.1333373
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\__init__.py first seen with mtime 1727626977.141469
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc8628\clients\device.py first seen with mtime 1727626977.1564655
File D:\Django\.venv\Lib\site-packages\requests_oauthlib\oauth2_session.py first seen with mtime 1727626980.226506
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\grant_types\base.py first seen with mtime 1727626977.1534646
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\clients\base.py first seen with mtime 1727626977.1474683
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc8628\clients\__init__.py first seen with mtime 1727626977.1564655
File D:\Django\.venv\Lib\site-packages\social_core\backends\google.py first seen with mtime 1727626981.5469003
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\endpoints\pre_configured.py first seen with mtime 1727626977.1514652
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\clients\service_application.py first seen with mtime 1727626977.1494668
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth1\rfc5849\__init__.py first seen with mtime 1727626977.1344626
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\endpoints\revocation.py first seen with mtime 1727626977.152465
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\errors.py first seen with mtime 1727626977.1434667
File D:\Django\.venv\Lib\site-packages\requests_oauthlib\oauth1_session.py first seen with mtime 1727626980.2255046
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth1\rfc5849\endpoints\resource.py first seen with mtime 1727626977.140468
File D:\Django\.venv\Lib\site-packages\requests_oauthlib\oauth2_auth.py first seen with mtime 1727626980.2255046
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\endpoints\base.py first seen with mtime 1727626977.1504657
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\__init__.py first seen with mtime 1727626977.142468
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\endpoints\authorization.py first seen with mtime 1727626977.1504657
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\tokens.py first seen with mtime 1727626977.1444666
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\clients\mobile_application.py first seen with mtime 1727626977.1484687
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth1\rfc5849\errors.py first seen with mtime 1727626977.1344626
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\endpoints\token.py first seen with mtime 1727626977.152465
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth1\rfc5849\request_validator.py first seen with mtime 1727626977.1354766
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\endpoints\introspect.py first seen with mtime 1727626977.1514652
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\grant_types\__init__.py first seen with mtime 1727626977.1534646
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\clients\__init__.py first seen with mtime 1727626977.146471
File D:\Django\.venv\Lib\site-packages\oauthlib\__init__.py first seen with mtime 1727626977.1312907
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\grant_types\client_credentials.py first seen with mtime 1727626977.1544645
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\grant_types\implicit.py first seen with mtime 1727626977.1544645
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth1\rfc5849\signature.py first seen with mtime 1727626977.1354766
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\request_validator.py first seen with mtime 1727626977.1444666
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth1\rfc5849\endpoints\signature_only.py first seen with mtime 1727626977.140468
File D:\Django\.venv\Lib\site-packages\oauthlib\common.py first seen with mtime 1727626977.1322927
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth1\rfc5849\endpoints\__init__.py first seen with mtime 1727626977.1364756
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth1\rfc5849\utils.py first seen with mtime 1727626977.1354766
File D:\Django\.venv\Lib\site-packages\social_core\backends\oauth.py first seen with mtime 1727626981.5580492
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\clients\web_application.py first seen with mtime 1727626977.1494668
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\endpoints\metadata.py first seen with mtime 1727626977.1514652
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\grant_types\authorization_code.py first seen with mtime 1727626977.1534646
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth1\rfc5849\endpoints\pre_configured.py first seen with mtime 1727626977.1394696
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth1\rfc5849\endpoints\access_token.py first seen with mtime 1727626977.1374698
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\grant_types\refresh_token.py first seen with mtime 1727626977.1544645
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\endpoints\resource.py first seen with mtime 1727626977.152465
File D:\Django\.venv\Lib\site-packages\requests_oauthlib\oauth1_auth.py first seen with mtime 1727626980.2255046
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\endpoints\__init__.py first seen with mtime 1727626977.1504657
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\utils.py first seen with mtime 1727626977.1454666
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\parameters.py first seen with mtime 1727626977.1434667
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\clients\backend_application.py first seen with mtime 1727626977.1474683
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth1\rfc5849\endpoints\request_token.py first seen with mtime 1727626977.1394696
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth1\rfc5849\endpoints\base.py first seen with mtime 1727626977.138468
File D:\Django\.venv\Lib\site-packages\oauthlib\signals.py first seen with mtime 1727626977.1322927
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc8628\__init__.py first seen with mtime 1727626977.1554644
File D:\Django\.venv\Lib\site-packages\requests_oauthlib\__init__.py first seen with mtime 1727626980.2245054
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth1\rfc5849\endpoints\authorization.py first seen with mtime 1727626977.1374698
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\clients\legacy_application.py first seen with mtime 1727626977.1484687
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth1\rfc5849\parameters.py first seen with mtime 1727626977.1344626
File D:\Django\.venv\Lib\site-packages\oauthlib\oauth2\rfc6749\grant_types\resource_owner_password_credentials.py first seen with mtime 1727626977.1554644
(0.016) SELECT "django_session"."session_key", "django_session"."session_data", "django_session"."expire_date" FROM "django_session" WHERE ("django_session"."expire_date" > '2024-10-02T11:14:34.634523+00:00'::timestamptz AND "django_session"."session_key" = 'wk0vpyuc9zi0l8f280a5i7pcfajmqu6t') LIMIT 21; args=(datetime.datetime(2024, 10, 2, 11, 14, 34, 634523, tzinfo=datetime.timezone.utc), 'wk0vpyuc9zi0l8f280a5i7pcfajmqu6t'); alias=default
"GET / HTTP/1.1" 200 4200
Exception while resolving variable 'name' in template 'unknown'.
Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Django\.venv\Lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    callback, callback_args, callback_kwargs = self.resolve_request(request)
                                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Django\.venv\Lib\site-packages\django\core\handlers\base.py", line 313, in resolve_request
    resolver_match = resolver.resolve(request.path_info)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Django\.venv\Lib\site-packages\django\urls\resolvers.py", line 705, in resolve
    raise Resolver404({"tried": tried, "path": new_path})
django.urls.exceptions.Resolver404: {'tried': [[<URLResolver <URLPattern list> (admin:admin) 'admin/'>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern '' [name='send_otp']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'clients' [name='clients']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'projects/' [name='projects']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_model_data/<str:code>/' [name='get_model_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_model/<str:code>/' [name='edit_model']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'project/<str:code>/' [name='projectd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_model_data/<str:code>/' [name='get_model_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_model/<str:code>/' [name='edit_model']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'delete_project/<int:project_id>/' [name='delete_project']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'income' [name='incomef_view']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_income_data/<int:inc_id>/' [name='get_income_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_income/<int:inc_id>/' [name='edit_income']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'expense' [name='expense']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'assets' [name='assets']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'clientsbook' [name='clientsbook']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'bookd/<str:company>/' [name='bookd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'status' [name='status']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'pending_pay' [name='pending_pay']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'entertainment' [name='entertainment']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'allproject' [name='allproject']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'budget' [name='budget']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'dashboard' [name='dashboard']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_expense_data/<int:exp_id>/' [name='get_expense_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_expense/<int:exp_id>/' [name='edit_expense']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_entertainment_data/<int:ent_id>/' [name='get_entertainment_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_entertainment/<int:ent_id>/' [name='edit_entertainment']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'delete_entertainment/<int:ent_id>/' [name='delete_entertainment']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'entertainmentd/<int:ent_id>/' [name='entertainmentd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_client_data/<int:clt_id>/' [name='get_client_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_client/<int:clt_id>/' [name='edit_client']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'clientd/<str:name>/' [name='clientd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_asset_data/<int:ast_id>/' [name='get_asset_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_asset/<int:ast_id>/' [name='edit_asset']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'assetd/<int:ast_id>/' [name='assetd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'delete_asset/<int:ast_id>/' [name='delete_asset']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_project_codes/' [name='get_project_codes']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'map/' [name='project_map']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'expense/<int:expense_id>/' [name='expense_detail']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'save-expense-notes/' [name='save_expense_notes']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'income/<int:income_id>/' [name='income_detail']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'save-income-notes/' [name='save_income_notes']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'save-item/' [name='save_item']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get-unique-types/' [name='get_unique_types']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get-unique-company/' [name='get_unique_company']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get-unique-client/' [name='get_unique_client']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get-unique-category/' [name='get_unique_category']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'total-expense/' [name='t_exp_in']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'calendar/' [name='calendar_view']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'api/events/' [name='get_events']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'api/events/add/' [name='add_event']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'verification' [name='verify_otp']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'logout' [name='logout']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'profile' [name='profile']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLResolver <module 'social_django.urls' from 'D:\\Django\\.venv\\Lib\\site-packages\\social_django\\urls.py'> (social:social) 'auth/'>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'login/' [name='login']>], [<URLPattern '^media/(?P<path>.*)$'>]], 'path': 'styles.css'}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\template\base.py", line 883, in _resolve_lookup
    current = current[bit]
              ~~~~~~~^^^^^
TypeError: 'URLResolver' object is not subscriptable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\template\base.py", line 893, in _resolve_lookup
    current = getattr(current, bit)
              ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'URLResolver' object has no attribute 'name'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\template\base.py", line 899, in _resolve_lookup
    current = current[int(bit)]
                      ^^^^^^^^
ValueError: invalid literal for int() with base 10: 'name'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\template\base.py", line 906, in _resolve_lookup
    raise VariableDoesNotExist(
django.template.base.VariableDoesNotExist: Failed lookup for key [name] in <URLResolver <URLPattern list> (admin:admin) 'admin/'>
Exception while resolving variable 'name' in template 'unknown'.
Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Django\.venv\Lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    callback, callback_args, callback_kwargs = self.resolve_request(request)
                                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Django\.venv\Lib\site-packages\django\core\handlers\base.py", line 313, in resolve_request
    resolver_match = resolver.resolve(request.path_info)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Django\.venv\Lib\site-packages\django\urls\resolvers.py", line 705, in resolve
    raise Resolver404({"tried": tried, "path": new_path})
django.urls.exceptions.Resolver404: {'tried': [[<URLResolver <URLPattern list> (admin:admin) 'admin/'>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern '' [name='send_otp']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'clients' [name='clients']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'projects/' [name='projects']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_model_data/<str:code>/' [name='get_model_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_model/<str:code>/' [name='edit_model']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'project/<str:code>/' [name='projectd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_model_data/<str:code>/' [name='get_model_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_model/<str:code>/' [name='edit_model']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'delete_project/<int:project_id>/' [name='delete_project']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'income' [name='incomef_view']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_income_data/<int:inc_id>/' [name='get_income_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_income/<int:inc_id>/' [name='edit_income']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'expense' [name='expense']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'assets' [name='assets']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'clientsbook' [name='clientsbook']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'bookd/<str:company>/' [name='bookd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'status' [name='status']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'pending_pay' [name='pending_pay']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'entertainment' [name='entertainment']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'allproject' [name='allproject']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'budget' [name='budget']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'dashboard' [name='dashboard']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_expense_data/<int:exp_id>/' [name='get_expense_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_expense/<int:exp_id>/' [name='edit_expense']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_entertainment_data/<int:ent_id>/' [name='get_entertainment_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_entertainment/<int:ent_id>/' [name='edit_entertainment']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'delete_entertainment/<int:ent_id>/' [name='delete_entertainment']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'entertainmentd/<int:ent_id>/' [name='entertainmentd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_client_data/<int:clt_id>/' [name='get_client_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_client/<int:clt_id>/' [name='edit_client']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'clientd/<str:name>/' [name='clientd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_asset_data/<int:ast_id>/' [name='get_asset_data']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'edit_asset/<int:ast_id>/' [name='edit_asset']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'assetd/<int:ast_id>/' [name='assetd']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'delete_asset/<int:ast_id>/' [name='delete_asset']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get_project_codes/' [name='get_project_codes']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'map/' [name='project_map']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'expense/<int:expense_id>/' [name='expense_detail']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'save-expense-notes/' [name='save_expense_notes']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'income/<int:income_id>/' [name='income_detail']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'save-income-notes/' [name='save_income_notes']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'save-item/' [name='save_item']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get-unique-types/' [name='get_unique_types']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get-unique-company/' [name='get_unique_company']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get-unique-client/' [name='get_unique_client']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'get-unique-category/' [name='get_unique_category']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'total-expense/' [name='t_exp_in']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'calendar/' [name='calendar_view']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'api/events/' [name='get_events']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'api/events/add/' [name='add_event']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'verification' [name='verify_otp']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'logout' [name='logout']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'profile' [name='profile']>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLResolver <module 'social_django.urls' from 'D:\\Django\\.venv\\Lib\\site-packages\\social_django\\urls.py'> (social:social) 'auth/'>], [<URLResolver <module 'cymaticsapp.urls' from 'D:\\Django\\.venv\\Scripts\\cymatics work\\cymaticsapp\\urls.py'> (None:None) ''>, <URLPattern 'login/' [name='login']>], [<URLPattern '^media/(?P<path>.*)$'>]], 'path': 'styles.css'}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\template\base.py", line 883, in _resolve_lookup
    current = current[bit]
              ~~~~~~~^^^^^
TypeError: 'URLResolver' object is not subscriptable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\template\base.py", line 893, in _resolve_lookup
    current = getattr(current, bit)
              ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'URLResolver' object has no attribute 'name'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\template\base.py", line 899, in _resolve_lookup
    current = current[int(bit)]
                      ^^^^^^^^
ValueError: invalid literal for int() with base 10: 'name'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Django\.venv\Lib\site-packages\django\template\base.py", line 906, in _resolve_lookup
    raise VariableDoesNotExist(
django.template.base.VariableDoesNotExist: Failed lookup for key [name] in <URLResolver <module 'social_django.urls' from 'D:\\Django\\.venv\\Lib\\site-packages\\social_django\\urls.py'> (social:social) 'auth/'>
Not Found: /styles.css
"GET /styles.css HTTP/1.1" 404 17408
(0.015) SELECT "django_session"."session_key", "django_session"."session_data", "django_session"."expire_date" FROM "django_session" WHERE ("django_session"."expire_date" > '2024-10-02T11:14:36.845256+00:00'::timestamptz AND "django_session"."session_key" = 'wk0vpyuc9zi0l8f280a5i7pcfajmqu6t') LIMIT 21; args=(datetime.datetime(2024, 10, 2, 11, 14, 36, 845256, tzinfo=datetime.timezone.utc), 'wk0vpyuc9zi0l8f280a5i7pcfajmqu6t'); alias=default
"GET /auth/login/google-oauth2/ HTTP/1.1" 302 0
File D:\Django\.venv\Scripts\cymatics work\cymaticspro\settings.py previous mtime: 1727867631.8909812, current mtime: 1727867705.3709843
D:\Django\.venv\Scripts\cymatics work\cymaticspro\settings.py notified as changed. Signal results: [(<function template_changed at 0x000001C6B2F6D440>, None), (<function translation_file_changed at 0x000001C6B41FD9E0>, None)].
D:\Django\.venv\Scripts\cymatics work\cymaticspro\settings.py changed, reloading.
