#!/usr/bin/env python
"""
Database Population Script for Cymatics Pro
This script populates all database tables with realistic sample data
"""

import os
import sys
import django
from datetime import datetime, timedelta, date
from decimal import Decimal
import random
from django.utils import timezone

# Setup Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cymaticspro.settings')
django.setup()

from django.contrib.auth.models import User
from cymaticsapp.models import (
    Client, Outclient, Project, Income, Expense,
    Assets, Entertainment, CalendarEvent, EmailOTP
)

def clear_existing_data():
    """Clear existing data from all tables"""
    print("Clearing existing data...")

    # Clear in reverse dependency order
    EmailOTP.objects.all().delete()
    CalendarEvent.objects.all().delete()
    Entertainment.objects.all().delete()
    Assets.objects.all().delete()
    Expense.objects.all().delete()
    Income.objects.all().delete()
    Project.objects.all().delete()
    Outclient.objects.all().delete()
    Client.objects.all().delete()

    # Keep superuser but clear other users
    User.objects.filter(is_superuser=False).delete()

    # Reset auto-increment sequences to avoid primary key conflicts
    from django.db import connection
    with connection.cursor() as cursor:
        # Get the actual table names from Django
        tables_to_reset = [
            'cymaticsapp_client',
            'cymaticsapp_outclient',
            'cymaticsapp_project',
            'cymaticsapp_income',
            'cymaticsapp_expense',
            'cymaticsapp_assets',
            'cymaticsapp_entertainment',
            'cymaticsapp_calendarevent',
            'cymaticsapp_emailotp'
        ]

        for table in tables_to_reset:
            try:
                # Reset sequence to start from 1
                cursor.execute(f"ALTER SEQUENCE {table}_id_seq RESTART WITH 1;")
                print(f"  ✓ Reset sequence for {table}")
            except Exception as e:
                print(f"  ⚠ Could not reset sequence for {table}: {e}")

    print("✓ Existing data cleared and sequences reset")

def create_users():
    """Create sample users"""
    print("Creating users...")

    users_data = [
        {'username': 'admin', 'email': '<EMAIL>', 'first_name': 'Admin', 'last_name': 'User', 'is_staff': True, 'is_superuser': True},
        {'username': 'photographer1', 'email': '<EMAIL>', 'first_name': 'John', 'last_name': 'Photographer'},
        {'username': 'videographer1', 'email': '<EMAIL>', 'first_name': 'Sarah', 'last_name': 'Videographer'},
        {'username': 'editor1', 'email': '<EMAIL>', 'first_name': 'Mike', 'last_name': 'Editor'},
    ]

    created_users = []
    for user_data in users_data:
        user, created = User.objects.get_or_create(
            username=user_data['username'],
            defaults={
                'email': user_data['email'],
                'first_name': user_data['first_name'],
                'last_name': user_data['last_name'],
                'is_staff': user_data.get('is_staff', False),
                'is_superuser': user_data.get('is_superuser', False),
            }
        )
        if created:
            user.set_password('password123')
            user.save()
            created_users.append(user)

    print(f"✓ Created {len(created_users)} users")
    return created_users

def create_clients():
    """Create sample clients"""
    print("Creating clients...")

    clients_data = [
        {'name': 'John Smith', 'company': 'TechCorp Solutions', 'number': '+1-555-0101', 'email': '<EMAIL>'},
        {'name': 'Emily Johnson', 'company': 'Creative Designs Ltd', 'number': '+1-555-0102', 'email': '<EMAIL>'},
        {'name': 'Michael Brown', 'company': 'Brown & Associates', 'number': '+1-555-0103', 'email': '<EMAIL>'},
        {'name': 'Sarah Davis', 'company': 'Davis Marketing Group', 'number': '+1-555-0104', 'email': '<EMAIL>'},
        {'name': 'David Wilson', 'company': 'Wilson Enterprises', 'number': '+1-555-0105', 'email': '<EMAIL>'},
        {'name': 'Lisa Anderson', 'company': 'Anderson Events', 'number': '+1-555-0106', 'email': '<EMAIL>'},
        {'name': 'Robert Taylor', 'company': 'Taylor Productions', 'number': '+1-555-0107', 'email': '<EMAIL>'},
        {'name': 'Jennifer Martinez', 'company': 'Martinez Media', 'number': '+1-555-0108', 'email': '<EMAIL>'},
        {'name': 'Christopher Lee', 'company': 'Lee Digital Agency', 'number': '+1-555-0109', 'email': '<EMAIL>'},
        {'name': 'Amanda White', 'company': 'White Wedding Planners', 'number': '+1-555-0110', 'email': '<EMAIL>'},
    ]

    created_clients = []
    for client_data in clients_data:
        client = Client.objects.create(**client_data)
        created_clients.append(client)

    print(f"✓ Created {len(created_clients)} clients")
    return created_clients

def create_outclients():
    """Create sample outsourcing clients"""
    print("Creating outsourcing clients...")

    outclients_data = [
        {'name': 'Alex Rodriguez', 'company': 'Rodriguez Photography', 'number': '+1-555-0201', 'email': '<EMAIL>'},
        {'name': 'Maria Garcia', 'company': 'Garcia Video Productions', 'number': '+1-555-0202', 'email': '<EMAIL>'},
        {'name': 'James Thompson', 'company': 'Thompson Drone Services', 'number': '+1-555-0203', 'email': '<EMAIL>'},
        {'name': 'Nicole Clark', 'company': 'Clark Audio Solutions', 'number': '+1-555-0204', 'email': '<EMAIL>'},
        {'name': 'Kevin Lewis', 'company': 'Lewis Lighting Co', 'number': '+1-555-0205', 'email': '<EMAIL>'},
    ]

    created_outclients = []
    for outclient_data in outclients_data:
        outclient = Outclient.objects.create(**outclient_data)
        created_outclients.append(outclient)

    print(f"✓ Created {len(created_outclients)} outsourcing clients")
    return created_outclients

def create_projects(clients, outclients):
    """Create sample projects"""
    print("Creating projects...")

    project_types = ['Wedding Photography', 'Corporate Video', 'Product Photography', 'Event Coverage', 'Portrait Session', 'Commercial Video', 'Real Estate Photography', 'Music Video', 'Documentary', 'Fashion Shoot']
    project_statuses = ['PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'ON_HOLD']
    locations = [
        'New York, NY', 'Los Angeles, CA', 'Chicago, IL', 'Houston, TX', 'Phoenix, AZ',
        'Philadelphia, PA', 'San Antonio, TX', 'San Diego, CA', 'Dallas, TX', 'San Jose, CA'
    ]

    # Coordinates for locations (lat, lng)
    location_coords = {
        'New York, NY': (40.7128, -74.0060),
        'Los Angeles, CA': (34.0522, -118.2437),
        'Chicago, IL': (41.8781, -87.6298),
        'Houston, TX': (29.7604, -95.3698),
        'Phoenix, AZ': (33.4484, -112.0740),
        'Philadelphia, PA': (39.9526, -75.1652),
        'San Antonio, TX': (29.4241, -98.4936),
        'San Diego, CA': (32.7157, -117.1611),
        'Dallas, TX': (32.7767, -96.7970),
        'San Jose, CA': (37.3382, -121.8863),
    }

    created_projects = []

    # Create 25 projects
    for i in range(25):
        client = random.choice(clients)
        project_type = random.choice(project_types)
        status = random.choice(project_statuses)
        location = random.choice(locations)
        lat, lng = location_coords[location]

        # Generate dates (timezone-aware)
        start_date = timezone.now() + timedelta(days=random.randint(-90, 90))
        end_date = start_date + timedelta(days=random.randint(1, 7))

        # Generate amounts
        amount = random.randint(1000, 15000)
        outsourcing = random.choice([True, False])
        outsourcing_amt = random.randint(200, 3000) if outsourcing else 0

        project_data = {
            'name': f'{project_type} - {client.company}',
            'company': client.company,
            'type': project_type,
            'status': status,
            'shoot_start_date': start_date,
            'shoot_end_date': end_date,
            'amount': amount,
            'location': location,
            'latitude': lat,
            'longitude': lng,
            'outsourcing': outsourcing,
            'reference': f'Reference notes for {project_type} project',
            'address': f'{random.randint(100, 9999)} {random.choice(["Main St", "Oak Ave", "Park Blvd", "First St", "Second Ave"])}, {location}',
            'outsourcing_amt': outsourcing_amt,
            'rating': random.randint(1, 5),
        }

        if outsourcing:
            outclient = random.choice(outclients)
            project_data.update({
                'out_for': random.choice(['Photography', 'Videography', 'Audio', 'Lighting', 'Drone']),
                'out_client': outclient.name,
                'outsourcing_paid': random.choice([True, False]),
            })

        # Create project with special handling for the custom save method
        try:
            project = Project(**project_data)
            # The save method will handle client assignment and code generation
            project.save()
            created_projects.append(project)
        except Exception as e:
            print(f"  ⚠ Error creating project: {e}")
            # Try with a simpler approach
            project_data_simple = {
                'name': project_data['name'],
                'company': project_data['company'],
                'type': project_data['type'],
                'status': project_data['status'],
                'amount': project_data['amount'],
                'location': project_data['location'],
            }
            project = Project.objects.create(**project_data_simple)
            created_projects.append(project)

    print(f"✓ Created {len(created_projects)} projects")
    return created_projects

def create_income(projects):
    """Create sample income entries"""
    print("Creating income entries...")

    income_descriptions = [
        'Project payment received',
        'Advance payment',
        'Final payment',
        'Equipment rental income',
        'Consultation fee',
        'Rush delivery fee',
        'Additional services',
        'Licensing fee',
        'Stock photo sales',
        'Workshop income',
    ]

    created_income = []

    # Create project-related income
    for project in projects:
        # Create 1-3 income entries per project
        num_entries = random.randint(1, 3)
        total_received = 0

        for i in range(num_entries):
            if total_received >= project.amount:
                break

            remaining = project.amount - total_received
            # Ensure we don't try to generate more than what's remaining
            min_amount = min(500, remaining)
            max_amount = remaining

            if min_amount > max_amount:
                amount = remaining
            else:
                amount = random.randint(min_amount, max_amount)

            total_received += amount

            income_date = project.shoot_start_date.date() + timedelta(days=random.randint(-30, 30))

            income = Income.objects.create(
                date=income_date,
                description=f'{random.choice(income_descriptions)} - {project.name}',
                amount=amount,
                note=f'Payment for project {project.code}',
                project_income=True,
                project_code=project,
            )
            created_income.append(income)

    # Create non-project income
    for i in range(15):
        income_date = date.today() + timedelta(days=random.randint(-180, 30))
        amount = random.randint(200, 2000)

        income = Income.objects.create(
            date=income_date,
            description=random.choice(income_descriptions),
            amount=amount,
            note='Non-project related income',
            project_income=False,
        )
        created_income.append(income)

    print(f"✓ Created {len(created_income)} income entries")
    return created_income

def create_expenses(projects):
    """Create sample expense entries"""
    print("Creating expense entries...")

    expense_categories = [
        'Equipment', 'Travel', 'Accommodation', 'Food & Beverages', 'Transportation',
        'Software', 'Marketing', 'Office Supplies', 'Insurance', 'Utilities',
        'Professional Services', 'Training', 'Maintenance', 'Fuel', 'Parking'
    ]

    expense_descriptions = [
        'Camera equipment purchase',
        'Lens rental',
        'Hotel accommodation',
        'Flight tickets',
        'Uber/taxi fare',
        'Client dinner',
        'Adobe Creative Suite subscription',
        'Memory cards',
        'Battery replacement',
        'Equipment insurance',
        'Studio rent',
        'Photography workshop',
        'Equipment maintenance',
        'Gas for location shoot',
        'Parking fees',
        'Lighting equipment',
        'Tripod purchase',
        'External hard drive',
        'Business cards printing',
        'Website hosting',
    ]

    created_expenses = []

    # Create project-related expenses
    for project in projects:
        # Create 2-5 expense entries per project
        num_entries = random.randint(2, 5)

        for i in range(num_entries):
            expense_date = project.shoot_start_date.date() + timedelta(days=random.randint(-15, 15))
            category = random.choice(expense_categories)
            description = random.choice(expense_descriptions)
            amount = random.randint(50, 1500)

            expense = Expense.objects.create(
                date=expense_date,
                category=category,
                description=f'{description} - {project.name}',
                amount=amount,
                notes=f'Expense for project {project.code}',
                project_expense=True,
                project_code=project,
            )
            created_expenses.append(expense)

    # Create non-project expenses
    for i in range(30):
        expense_date = date.today() + timedelta(days=random.randint(-180, 30))
        category = random.choice(expense_categories)
        description = random.choice(expense_descriptions)
        amount = random.randint(25, 800)

        expense = Expense.objects.create(
            date=expense_date,
            category=category,
            description=description,
            amount=amount,
            notes='General business expense',
            project_expense=False,
        )
        created_expenses.append(expense)

    print(f"✓ Created {len(created_expenses)} expense entries")
    return created_expenses

def create_assets():
    """Create sample assets"""
    print("Creating assets...")

    assets_data = [
        {'type': 'Camera', 'name': 'Canon EOS R5', 'quantity': Decimal('1'), 'buy_price': Decimal('3899.99'), 'value': 3500},
        {'type': 'Camera', 'name': 'Sony A7 IV', 'quantity': Decimal('1'), 'buy_price': Decimal('2499.99'), 'value': 2200},
        {'type': 'Lens', 'name': 'Canon RF 24-70mm f/2.8L', 'quantity': Decimal('1'), 'buy_price': Decimal('2299.99'), 'value': 2000},
        {'type': 'Lens', 'name': 'Sony FE 85mm f/1.4 GM', 'quantity': Decimal('1'), 'buy_price': Decimal('1799.99'), 'value': 1600},
        {'type': 'Lighting', 'name': 'Profoto B10 Plus', 'quantity': Decimal('2'), 'buy_price': Decimal('1695.00'), 'value': 3000},
        {'type': 'Lighting', 'name': 'Godox AD600Pro', 'quantity': Decimal('1'), 'buy_price': Decimal('899.99'), 'value': 750},
        {'type': 'Tripod', 'name': 'Gitzo GT3543XLS', 'quantity': Decimal('1'), 'buy_price': Decimal('899.99'), 'value': 800},
        {'type': 'Tripod', 'name': 'Manfrotto 055XPRO3', 'quantity': Decimal('2'), 'buy_price': Decimal('299.99'), 'value': 500},
        {'type': 'Audio', 'name': 'Rode VideoMic Pro Plus', 'quantity': Decimal('1'), 'buy_price': Decimal('329.99'), 'value': 280},
        {'type': 'Audio', 'name': 'Zoom H6 Recorder', 'quantity': Decimal('1'), 'buy_price': Decimal('399.99'), 'value': 350},
        {'type': 'Storage', 'name': 'SanDisk Extreme Pro CFexpress', 'quantity': Decimal('4'), 'buy_price': Decimal('199.99'), 'value': 700},
        {'type': 'Storage', 'name': 'LaCie Rugged SSD 2TB', 'quantity': Decimal('3'), 'buy_price': Decimal('399.99'), 'value': 1000},
        {'type': 'Computer', 'name': 'MacBook Pro 16" M2 Max', 'quantity': Decimal('1'), 'buy_price': Decimal('3499.99'), 'value': 3200},
        {'type': 'Monitor', 'name': 'EIZO ColorEdge CG279X', 'quantity': Decimal('1'), 'buy_price': Decimal('2199.99'), 'value': 1900},
        {'type': 'Drone', 'name': 'DJI Mavic 3 Cine', 'quantity': Decimal('1'), 'buy_price': Decimal('4999.99'), 'value': 4500},
    ]

    created_assets = []
    for asset_data in assets_data:
        asset_data['date'] = date.today() + timedelta(days=random.randint(-365, -30))
        asset_data['note'] = f'Professional {asset_data["type"].lower()} for photography/videography work'

        asset = Assets.objects.create(**asset_data)
        created_assets.append(asset)

    print(f"✓ Created {len(created_assets)} assets")
    return created_assets

def create_entertainment():
    """Create sample entertainment entries"""
    print("Creating entertainment entries...")

    entertainment_data = [
        {'type': 'Movie', 'language': 'English', 'rating': 5, 'name': 'The Shawshank Redemption', 'source': 'Netflix'},
        {'type': 'Movie', 'language': 'English', 'rating': 4, 'name': 'Inception', 'source': 'Amazon Prime'},
        {'type': 'Movie', 'language': 'English', 'rating': 5, 'name': 'The Dark Knight', 'source': 'HBO Max'},
        {'type': 'TV Show', 'language': 'English', 'rating': 4, 'name': 'Breaking Bad', 'source': 'Netflix'},
        {'type': 'TV Show', 'language': 'English', 'rating': 5, 'name': 'Game of Thrones', 'source': 'HBO Max'},
        {'type': 'Movie', 'language': 'Spanish', 'rating': 4, 'name': 'Pan\'s Labyrinth', 'source': 'Hulu'},
        {'type': 'Movie', 'language': 'Korean', 'rating': 5, 'name': 'Parasite', 'source': 'Amazon Prime'},
        {'type': 'TV Show', 'language': 'Korean', 'rating': 4, 'name': 'Squid Game', 'source': 'Netflix'},
        {'type': 'Movie', 'language': 'Japanese', 'rating': 5, 'name': 'Spirited Away', 'source': 'Disney+'},
        {'type': 'Documentary', 'language': 'English', 'rating': 4, 'name': 'Free Solo', 'source': 'National Geographic'},
        {'type': 'Documentary', 'language': 'English', 'rating': 5, 'name': 'Won\'t You Be My Neighbor?', 'source': 'Amazon Prime'},
        {'type': 'TV Show', 'language': 'English', 'rating': 4, 'name': 'Stranger Things', 'source': 'Netflix'},
        {'type': 'Movie', 'language': 'French', 'rating': 4, 'name': 'Amélie', 'source': 'Criterion Channel'},
        {'type': 'TV Show', 'language': 'English', 'rating': 5, 'name': 'The Office', 'source': 'Peacock'},
        {'type': 'Movie', 'language': 'English', 'rating': 3, 'name': 'Avengers: Endgame', 'source': 'Disney+'},
    ]

    created_entertainment = []
    for ent_data in entertainment_data:
        ent_data['date'] = timezone.now() + timedelta(days=random.randint(-90, 0))

        entertainment = Entertainment.objects.create(**ent_data)
        created_entertainment.append(entertainment)

    print(f"✓ Created {len(created_entertainment)} entertainment entries")
    return created_entertainment

def create_calendar_events():
    """Create sample calendar events"""
    print("Creating calendar events...")

    event_titles = [
        'Client Meeting - TechCorp',
        'Wedding Shoot - Anderson Events',
        'Equipment Maintenance',
        'Photo Editing Session',
        'Corporate Video Meeting',
        'Portfolio Review',
        'Equipment Shopping',
        'Networking Event',
        'Workshop: Advanced Lighting',
        'Client Consultation Call',
        'Location Scouting',
        'Post-Production Review',
        'Marketing Strategy Meeting',
        'Equipment Rental Pickup',
        'Client Delivery Meeting',
    ]

    created_events = []
    for i in range(20):
        start_time = timezone.now() + timedelta(days=random.randint(-30, 60), hours=random.randint(8, 18))
        end_time = start_time + timedelta(hours=random.randint(1, 4))

        event = CalendarEvent.objects.create(
            title=random.choice(event_titles),
            start_time=start_time,
            end_time=end_time,
        )
        created_events.append(event)

    print(f"✓ Created {len(created_events)} calendar events")
    return created_events

def create_email_otps(users):
    """Create sample email OTP entries"""
    print("Creating email OTP entries...")

    created_otps = []
    for user in users[:3]:  # Create OTPs for first 3 users
        for i in range(random.randint(1, 3)):
            otp = EmailOTP.objects.create(
                user=user,
                otp=f'{random.randint(100000, 999999)}',
                created_at=timezone.now() + timedelta(days=random.randint(-7, 0)),
            )
            created_otps.append(otp)

    print(f"✓ Created {len(created_otps)} email OTP entries")
    return created_otps

def main():
    """Main function to populate the database"""
    print("🚀 Starting database population...")
    print("=" * 50)

    # Clear existing data
    clear_existing_data()

    # Create data in dependency order
    users = create_users()
    clients = create_clients()
    outclients = create_outclients()
    projects = create_projects(clients, outclients)
    income = create_income(projects)
    expenses = create_expenses(projects)
    assets = create_assets()
    entertainment = create_entertainment()
    calendar_events = create_calendar_events()
    email_otps = create_email_otps(users)

    print("=" * 50)
    print("✅ Database population completed successfully!")
    print(f"📊 Summary:")
    print(f"   • Users: {len(users)}")
    print(f"   • Clients: {len(clients)}")
    print(f"   • Outsourcing Clients: {len(outclients)}")
    print(f"   • Projects: {len(projects)}")
    print(f"   • Income Entries: {len(income)}")
    print(f"   • Expense Entries: {len(expenses)}")
    print(f"   • Assets: {len(assets)}")
    print(f"   • Entertainment: {len(entertainment)}")
    print(f"   • Calendar Events: {len(calendar_events)}")
    print(f"   • Email OTPs: {len(email_otps)}")
    print("=" * 50)
    print("🎉 Your Cymatics Pro database is now populated with sample data!")
    print("💡 You can now run the Django server to see the website with data.")

if __name__ == '__main__':
    main()
