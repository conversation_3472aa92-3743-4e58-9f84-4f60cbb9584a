.top-bar {
    background-color: #000;
    width: 100%;
    padding: 10px;
    display: flex;
    align-items: center;
    color: white;
}

body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    display: flex;
    height: 100vh;
    flex-direction: column;
}
.container {
    display: flex;
    width: 100%;
    flex-grow: 1;
}
    .sidebar {
      background-color: #1e1e1e;
      color: white;
      width: 250px;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      transition: width 0.3s;
      position: relative;
  }
  .sidebar.closed {
    width: 60px;
}

/* Icon visibility and border */
.sidebar .toggle-icon {
    position: absolute;
    top: 25px !important; /* Aligned near the top */
    right: -8px; /* Adjusted to be right on the edge line */
    cursor: pointer;
    visibility: hidden;
    border: 3px solid rgba(78, 27, 231, 0.5); /* Light border */
    border-radius: 8px;
    padding: 1px;
    transition: visibility 0.3s ease-in-out, top 0.3s ease-in-out; /* Smooth transitions */
    z-index: 2;
}
#toggle-icon {
    width: 20px;
    height: 20px;
}


/* Adjust position for closed state to avoid overlap */
.sidebar.closed .toggle-icon {
    top: 10px;
    right: -8px; /* Keep it on the edge even when closed */
}

/* Show icon when hovering near the sidebar or over the icon */
.sidebar:hover .toggle-icon, .toggle-icon:hover {
    visibility: visible;
}

.sidebar .logo {
    padding: 20px;
    text-align: center;
}

.sidebar.closed .logo {
    display: none;
}

.sidebar nav ul {
    list-style: none;
    padding: 0;
    width: 100%;
    text-align: center;
}

.sidebar nav ul li {
    padding: 12px 20px;
    cursor: pointer;
    transition: background-color 0.3s, border-left 0.3s;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.sidebar.closed nav ul li {
    justify-content: center;
}

.sidebar nav ul li a {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: white;
    width: 100%;
    font-family: Arial, sans-serif;
}

.sidebar nav ul li a:hover {
    background-color: #555;
    border-left: 4px solid #ffcc00;
}

.menu-icon {
    margin-right: 10px;
    width: 24px;
    height: 24px;
}

.menu-text {
    transition: opacity 0.3s, visibility 0.3s;
    font-family: Arial, sans-serif;
}

.sidebar.closed .menu-text {
    display: none;
}

.sidebar.closed nav ul li:hover {
    background-color: inherit;
}



.asset-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.asset-header img {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    margin-bottom: 20px;
}

.asset-header h1 {
    margin: 0;
    font-size: 2em;
}

.asset-header p {
    margin: 5px 0;
    font-size: 1.2em;
    color: #777;
}



.asset-details-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-top: 20px;
    width: 100%; /* Adjust width as needed */
}

.asset-number {
    font-size: 1.2em;
    margin-bottom: 10px;
}

.asset-details {
    background-color: #fff;
    padding: 20px;
    border-radius: 10px;
    width: 150px;
    text-align: left;
}

.asset-details p {
    margin: 10px 0;
    font-size: 1.2em;
}

.value-box p {
    margin: 0;
}

.value-label {
    color: #777;
    font-size: 0.9em;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 800px;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.close:hover,
.close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}

.modal-content form {
    display: flex;
    flex-direction: column;
}

.modal-content form label {
    margin-top: 10px;
    font-weight: bold;
}

.modal-content form input,
.modal-content form button {
    padding: 10px;
    margin-top: 5px;
    border-radius: 5px;
    border: 1px solid #ccc;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
}

.form-actions button {
    margin-left: 10px;
}

form button[type="submit"],
form button[type="button"] {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

form button[type="submit"] {
    background-color: black;
    color: white;
}

form button[type="button"] {
    background-color: #ccc;
}

.checkbox-container {
    display: flex;
    align-items: center;
}

.checkbox-container input {
    margin-right: 10px;
}