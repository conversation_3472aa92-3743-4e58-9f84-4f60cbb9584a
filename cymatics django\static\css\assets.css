

.main-content {
    flex-grow: 1;
    background-color: #f1f1f1;
    padding: 20px;
    overflow: auto; /* Allow scrolling for main content only */
}
/* Main Sidebar styling */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    display: flex;
    height: 100vh;
    flex-direction: column;
}
.container {
    display: flex;
    width: 100%;
    flex-grow: 1;
}
    .sidebar {
      background-color: #1e1e1e;
      color: white;
      width: 250px;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      transition: width 0.3s;
      position: relative;
  }
  
  .sidebar.closed {
      width: 60px;
  }
  
  /* Icon visibility and border */
  .sidebar .toggle-icon {
      position: absolute;
      top: 25px !important; /* Aligned near the top */
      right: -8px; /* Adjusted to be right on the edge line */
      cursor: pointer;
      visibility: hidden;
      border: 3px solid rgba(78, 27, 231, 0.5); /* Light border */
      border-radius: 8px;
      padding: 1px;
      transition: visibility 0.3s ease-in-out, top 0.3s ease-in-out; /* Smooth transitions */
      z-index: 2;
  }
  #toggle-icon {
      width: 20px;
      height: 20px;
  }
  
  
  /* Adjust position for closed state to avoid overlap */
  .sidebar.closed .toggle-icon {
      top: 10px;
      right: -8px; /* Keep it on the edge even when closed */
  }
  
  /* Show icon when hovering near the sidebar or over the icon */
  .sidebar:hover .toggle-icon, .toggle-icon:hover {
      visibility: visible;
  }
  
  .sidebar .logo {
      padding: 20px;
      text-align: center;
  }
  
  .sidebar.closed .logo {
      display: none;
  }
  
  .sidebar nav ul {
      list-style: none;
      padding: 0;
      width: 100%;
      text-align: center;
  }
  
  .sidebar nav ul li {
      padding: 12px 20px;
      cursor: pointer;
      transition: background-color 0.3s, border-left 0.3s;
      display: flex;
      justify-content: flex-start;
      align-items: center;
  }
  
  .sidebar.closed nav ul li {
      justify-content: center;
  }
  
  .sidebar nav ul li a {
      display: flex;
      align-items: center;
      text-decoration: none;
      color: white;
      width: 100%;
      font-family: Arial, sans-serif;
  }
  
  .sidebar nav ul li a:hover {
      background-color: #555;
      border-left: 4px solid #ffcc00;
  }
  
  .menu-icon {
      margin-right: 10px;
      width: 24px;
      height: 24px;
  }
  
  .menu-text {
      transition: opacity 0.3s, visibility 0.3s;
      font-family: Arial, sans-serif;
  }
  
  .sidebar.closed .menu-text {
      display: none;
  }
  
  .sidebar.closed nav ul li:hover {
      background-color: inherit;
  }


.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.search-bar {
    display: flex;
    align-items: center;
}

.search-bar input {
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    margin-right: 10px;
}

.add-btn {
    padding: 10px 20px;
    background-color: black;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.edit-btn {
    padding: 5px 10px;
    border: none;
    background-color: transparent;
    cursor: pointer;
    margin-left: 10px; 
    align-self: flex-end; 
    font-size: 18px; 
}

.assets-table {
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    flex-grow: 1;
    overflow: hidden; /* Prevent overflow */
}

.asset-item {
    display: flex;
    align-items: center;
    justify-content: space-between; 
    padding: 10px 15px;
    border-bottom: 1px solid #ccc;
    min-height: 60px; 
    cursor: pointer; 
    transition: background-color 0.3s; 
}

.asset-item:hover {
    background-color: #f0f0f0; 
}

.asset-icon {
    margin-right: 20px;
    width: 50px;
    height: 50px;
}

.asset-details {
    display: flex;
    flex-direction: column;
    flex-grow: 1; 
}

.asset-type {
    font-weight: normal;
}

.asset-value {
    font-size: 18px;
    font-weight: bold;
}

.asset-name {
    color: #888;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    margin: 5% auto; 
    padding: 20px;
    border: 1px solid #888;
    width: 400px;
    border-radius: 10px;
}

.modal-content form {
    display: flex;
    flex-direction: column;
}

.modal-content form label {
    margin: 10px 0 5px;
}

.modal-content form input,
.modal-content form select {
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    width: 100%;
    box-sizing: border-box; 
}

.close:hover,
.close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}

.form-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.submit-btn,
.cancel-btn {
    flex: 1;
    padding: 10px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    margin: 0 5px; 
}

.submit-btn {
    background-color: #000;
    color: white;
}

.cancel-btn {
    background-color: #ddd;
    color: black;
}

/* Dropdown Menu Styles */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-content {
    display: none;
    position: absolute;
    background-color: white;
    min-width: 160px;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
    z-index: 1;
    right: 0;
}

.last-item .dropdown-content {
    bottom: 100%; /* Position the dropdown above the button */
    top: auto;    /* Disable the default top alignment */
    transform: translateY(-10px); /* Move it upwards */
}

.dropdown-content button {
    color: black;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    border: none;
    width: 100%;
    text-align: left;
    background: white;
    cursor: pointer;
}

.dropdown-content button:hover {
    background-color: #f1f1f1;
}

.dropdown:hover .dropdown-content {
    display: block;
}

/* Forced Colors Mode styles */
@media (forced-colors: active) {
    body {
        background-color: Canvas; /* System background color */
        color: Text; /* System text color */
    }
    .sidebar {
        background-color: Window; /* System background color for sidebar */
    }
    .modal-content {
        background-color: Window; /* System background color for modal */
        color: Text; /* System text color for modal */
    }
    .add-btn,
    .submit-btn {
        background-color: ButtonFace; /* System button background */
        color: ButtonText; /* System button text color */
    }
    .cancel-btn {
        background-color: ButtonFace; /* System button background */
        color: ButtonText; /* System button text color */
    }
    @media (max-width: 768px) {
        .sidebar {
            width: 180px;
        }
    
        .sidebar.closed {
            width: 50px;
        }
    }
    
}
