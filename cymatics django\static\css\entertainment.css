body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
  display: flex;
  height: 100vh;
  flex-direction: column;
}
.container {
  display: flex;
  width: 100%;
  flex-grow: 1;
}
  .sidebar {
    background-color: #1e1e1e;
    color: white;
    width: 250px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    transition: width 0.3s;
    position: relative;
}

.sidebar.closed {
    width: 60px;
}

/* Icon visibility and border */
.sidebar .toggle-icon {
    position: absolute;
    top: 25px !important; /* Aligned near the top */
    right: -8px; /* Adjusted to be right on the edge line */
    cursor: pointer;
    visibility: hidden;
    border: 3px solid rgba(78, 27, 231, 0.5); /* Light border */
    border-radius: 8px;
    padding: 1px;
    transition: visibility 0.3s ease-in-out, top 0.3s ease-in-out; /* Smooth transitions */
    z-index: 2;
}
#toggle-icon {
    width: 20px;
    height: 20px;
}


/* Adjust position for closed state to avoid overlap */
.sidebar.closed .toggle-icon {
    top: 10px;
    right: -8px; /* Keep it on the edge even when closed */
}

/* Show icon when hovering near the sidebar or over the icon */
.sidebar:hover .toggle-icon, .toggle-icon:hover {
    visibility: visible;
}

.sidebar .logo {
    padding: 20px;
    text-align: center;
}

.sidebar.closed .logo {
    display: none;
}

.sidebar nav ul {
    list-style: none;
    padding: 0;
    width: 100%;
    text-align: center;
}

.sidebar nav ul li {
    padding: 12px 20px;
    cursor: pointer;
    transition: background-color 0.3s, border-left 0.3s;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.sidebar.closed nav ul li {
    justify-content: center;
}

.sidebar nav ul li a {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: white;
    width: 100%;
    font-family: Arial, sans-serif;
}

.sidebar nav ul li a:hover {
    background-color: #555;
    border-left: 4px solid #ffcc00;
}

.menu-icon {
    margin-right: 10px;
    width: 24px;
    height: 24px;
}

.menu-text {
    transition: opacity 0.3s, visibility 0.3s;
    font-family: Arial, sans-serif;
}

.sidebar.closed .menu-text {
    display: none;
}

.sidebar.closed nav ul li:hover {
    background-color: inherit;
}

  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: white;
  }
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: white;
    border-bottom: 1px solid #ddd;
  }
  
  .header h1 {
    font-size: 24px;
  }
  
  .search-add {
    display: flex;
    align-items: center;
  }
  
  .search-add input {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    margin-right: 10px;
  }
  
  .search-add .add-btn {
    padding: 10px 20px;
    border: none;
    background-color: #000000;
    color: white;
    border-radius: 5px;
    cursor: pointer;
  }
  
  .content {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    padding: 20px;
  }
  
  .movie {
    position: relative;
    flex: 1;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f9f9f9;
    cursor: pointer;
    transition: background-color 0.3s;
  }
  
  .movie:hover {
    background-color: #e9e9e9;
  }
  
  .movie p {
    margin: 0;
  }
  
  .options {
    position: absolute;
    top: 20px;
    right: 20px;
  }
  
  .options-btn {
    border: none;
    background: none;
    font-size: 20px;
    cursor: pointer;
  }
  
  .options-menu {
    display: none;
    position: absolute;
    top: 30px;
    right: 0;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 10;
  }
  
  .options-menu button {
    border: none;
    background: none;
    padding: 10px 20px;
    width: 100%;
    text-align: left;
    cursor: pointer;
  }
  
  .options-menu button:hover {
    background-color: #f4f4f4;
  }
  .modal {
    display: none;
    position: fixed;
    z-index: 100;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    padding-top: 60px;
  }
  
  .modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 400px;
    border-radius: 10px;
  }
  
  .close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
  }
  
  .close:hover,
  .close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
  }
  
  
  #editForm, #addForm {
    display: flex;
    flex-direction: column;
    gap: 15px; 
  }
  
  #editForm label, #addForm label {
    margin-bottom: 5px; 
  }
  
  #editForm input, #addForm input, #addForm select {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    width: 100%; 
    box-sizing: border-box;
  }
  
  .form-buttons {
    display: flex;
    gap: 10px; 
  }
  
  .submit-btn,
  .cancel-btn {
    flex: 1; 
    padding: 10px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
  }
  
  .submit-btn {
    background-color: #000;
    color: white;
  }
  
  .cancel-btn {
    background-color: #ddd;
    color: black;
  }
  
  
  .modal {
    display: none;
    position: fixed;
    z-index: 100;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    padding-top: 60px;
  }
  
  .modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 400px;
    border-radius: 10px;
  }
  
  .close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
  }
  
  .close:hover,
  .close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
  }
  
  
  #editForm,
  #addForm {
    display: flex;
    flex-direction: column;
    gap: 15px; 
  }
  
  #editForm label,
  #addForm label {
    margin-bottom: 5px; 
  }
  
  #editForm input,
  #addForm input,
  #addForm select {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    width: 100%; 
    box-sizing: border-box;
  }
  
  .form-buttons {
    display: flex;
    gap: 10px; 
  }
  
  .submit-btn,
  .cancel-btn {
    flex: 1; 
    padding: 10px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
  }
  
  .submit-btn {
    background-color: #000;
    color: white;
  }
  
  .cancel-btn {
    background-color: #ddd;
    color: black;
  }
