body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    display: flex;
    height: 100vh;
    flex-direction: column;
}
.container {
    display: flex;
    width: 100%;
    flex-grow: 1;
}
    .sidebar {
      background-color: #1e1e1e;
      color: white;
      width: 250px;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      transition: width 0.3s;
      position: relative;
  }
  
  .sidebar.closed {
      width: 60px;
  }
  
  /* Icon visibility and border */
  .sidebar .toggle-icon {
      position: absolute;
      top: 25px !important; /* Aligned near the top */
      right: -8px; /* Adjusted to be right on the edge line */
      cursor: pointer;
      visibility: hidden;
      border: 3px solid rgba(78, 27, 231, 0.5); /* Light border */
      border-radius: 8px;
      padding: 1px;
      transition: visibility 0.3s ease-in-out, top 0.3s ease-in-out; /* Smooth transitions */
      z-index: 2;
  }
  #toggle-icon {
      width: 20px;
      height: 20px;
  }
  
  
  /* Adjust position for closed state to avoid overlap */
  .sidebar.closed .toggle-icon {
      top: 10px;
      right: -8px; /* Keep it on the edge even when closed */
  }
  
  /* Show icon when hovering near the sidebar or over the icon */
  .sidebar:hover .toggle-icon, .toggle-icon:hover {
      visibility: visible;
  }
  
  .sidebar .logo {
      padding: 20px;
      text-align: center;
  }
  
  .sidebar.closed .logo {
      display: none;
  }
  
  .sidebar nav ul {
      list-style: none;
      padding: 0;
      width: 100%;
      text-align: center;
  }
  
  .sidebar nav ul li {
      padding: 12px 20px;
      cursor: pointer;
      transition: background-color 0.3s, border-left 0.3s;
      display: flex;
      justify-content: flex-start;
      align-items: center;
  }
  
  .sidebar.closed nav ul li {
      justify-content: center;
  }
  
  .sidebar nav ul li a {
      display: flex;
      align-items: center;
      text-decoration: none;
      color: white;
      width: 100%;
      font-family: Arial, sans-serif;
  }
  
  .sidebar nav ul li a:hover {
      background-color: #555;
      border-left: 4px solid #ffcc00;
  }
  
  .menu-icon {
      margin-right: 10px;
      width: 24px;
      height: 24px;
  }
  
  .menu-text {
      transition: opacity 0.3s, visibility 0.3s;
      font-family: Arial, sans-serif;
  }
  
  .sidebar.closed .menu-text {
      display: none;
  }
  
  .sidebar.closed nav ul li:hover {
      background-color: inherit;
  }


.main-content {
    flex-grow: 1;
    background-color: #f1f1f1;
    padding: 20px;
    position: relative; /* Required for positioning the form */
}
.dropdown {
    position: absolute; /* Position relative to the profile section */
    bottom: 100%; /* Position above the profile section */
    left: 0; /* Align to the left */
    background-color: white; /* Background color of the dropdown */
    border: 1px solid #ccc; /* Border for the dropdown */
    border-radius: 4px; /* Rounded corners */
    z-index: 1000; /* Ensure it appears above other elements */
    width: 160px; /* Set width for the dropdown */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); /* Shadow for a floating effect */
    display: none; /* Initially hidden */
}
.profile-section {
    position: relative; /* Allows positioning of the dropdown */
    padding: 12px 20px; /* Match padding with other menu items */
    cursor: pointer; /* Change cursor on hover */
    transition: background-color 0.3s, border-left 0.3s; /* Smooth transition */
}

.profile-section:hover {
    background-color: #555; /* Background color on hover */
    border-left: 4px solid #ffcc00; /* Left border on hover */
}
.dropdown ul {
    list-style: none; /* Remove default list styles */
    padding: 0; /* Remove padding */
    margin: 0; /* Remove margin */
}

.dropdown li {
    padding: 10px; /* Padding for each item */
    color: black; /* Set text color to black */
    cursor: pointer; /* Change cursor on hover */
}
.dropdown li:hover {
    background-color: #f1f1f1; /* Background on hover */
}

        .left-side {
            display: flex;
            flex-direction: column;
            gap: 20px;
            padding-left: 20px;
            padding-top: 20px;
        }
        .movie-info {
            display: flex;
            flex-direction: column; /* Stack title and details */
            justify-content: flex-start;
        }
        
        .movie-title {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
        }

        .language {
            color: #888;
            font-size: 14px;
            padding-left:11px;
        }

        .rating {
            display: flex;
            align-items: center;
            font-size: 24px;
            padding-top: 350px;
        }

        .rating span {
            color: rgb(0, 0, 0);
            cursor: pointer;
        }

        .rating span.inactive {
            color: #ccc;
        }

        
        .content-wrapper {
            display: flex;
            margin-left: 20px; 
            flex-grow: 1;
        }

      