
  
  .top-banner {
    background-color: black;
    color: white;
    display: flex;
    justify-content: space-around;
    padding: 20px 0;
    margin-bottom: 20px;
  }
  
  .banner-item {
    text-align: center;
  }
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .search-bar {
    display: flex;
    align-items: center;
  }
  
  .search-bar input {
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    margin-right: 10px;
  }
  
  .add-btn {
    padding: 10px 20px;
    background-color: black;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
  }
  
  .income-table {
    background-color: white;
    padding: 20px;
    border-radius: 10px;
  }
  
  table {
    width: 100%;
    border-collapse: collapse;
  }
  
  th, td {
    text-align: left;
    padding: 10px;
    border-bottom: 1px solid #ddd;
  }
  
  th {
    background-color: #f4f4f4;
  }
  
  .action-btn {
    padding: 5px 10px;
    border: none;
    background-color: #ccc;
    border-radius: 5px;
    cursor: pointer;
  }
  
  .footer {
    display: flex;
    justify-content: center;
    padding: 20px;
    background-color: white;
    margin-top: auto;
    border-top: 1px solid #ddd;
  }
  
  .btn {
    padding: 10px 20px;
    width: 500px;
    height: 50px;
    font-size: 25px;
    background-color: black;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
  }
  
  .modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgb(0,0,0);
    background-color: rgba(0,0,0,0.4);
  }
  
  .modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 50%;
    border-radius: 10px;
  }
  
  .close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
  }
  
  .close:hover,
  .close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
  }
  
  .modal-content form {
    display: flex;
    flex-direction: column;
  }
  
  .modal-content form label {
    margin-top: 10px;
  }
  
  .modal-content form input,
  .modal-content form button {
    padding: 10px;
    margin-top: 5px;
    border-radius: 5px;
    border: 1px solid #ccc;
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
  
  .form-actions button {
    margin-left: 10px;
  }
  form button[type="submit"],
  form button[type="button"] {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
  }
  
  form button[type="submit"] {
    background-color: black;
    color: white;
  }
  
  form button[type="button"] {
    background-color: #ccc;
  }
  
  
  .checkbox-container {
    display: flex;
    align-items: center;
  }
  
  .checkbox-container input {
    margin-right: 10px;
  }