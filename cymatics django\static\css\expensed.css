body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    display: flex;
    height: 100vh;
    flex-direction: column;
}
.container {
    display: flex;
    width: 100%;
    flex-grow: 1;
}
.sidebar {
    background-color: #1e1e1e;
    color: white;
    width: 250px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    transition: width 0.3s;
    position: relative;
}

.sidebar.closed {
    width: 60px;
}

/* Icon visibility and border */
.sidebar .toggle-icon {
    position: absolute;
    top: 25px !important; /* Aligned near the top */
    right: -8px; /* Adjusted to be right on the edge line */
    cursor: pointer;
    visibility: hidden;
    border: 3px solid rgba(78, 27, 231, 0.5); /* Light border */
    border-radius: 8px;
    padding: 1px;
    transition: visibility 0.3s ease-in-out, top 0.3s ease-in-out; /* Smooth transitions */
    z-index: 2;
}
#toggle-icon {
    width: 20px;
    height: 20px;
}


/* Adjust position for closed state to avoid overlap */
.sidebar.closed .toggle-icon {
    top: 10px;
    right: -8px; /* Keep it on the edge even when closed */
}

/* Show icon when hovering near the sidebar or over the icon */
.sidebar:hover .toggle-icon, .toggle-icon:hover {
    visibility: visible;
}

.sidebar .logo {
    padding: 20px;
    text-align: center;
}

.sidebar.closed .logo {
    display: none;
}

.sidebar nav ul {
    list-style: none;
    padding: 0;
    width: 100%;
    text-align: center;
}

.sidebar nav ul li {
    padding: 12px 20px;
    cursor: pointer;
    transition: background-color 0.3s, border-left 0.3s;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.sidebar.closed nav ul li {
    justify-content: center;
}

.sidebar nav ul li a {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: white;
    width: 100%;
    font-family: Arial, sans-serif;
}

.sidebar nav ul li a:hover {
    background-color: #555;
    border-left: 4px solid #ffcc00;
}

.menu-icon {
    margin-right: 10px;
    width: 24px;
    height: 24px;
}

.menu-text {
    transition: opacity 0.3s, visibility 0.3s;
    font-family: Arial, sans-serif;
}

.sidebar.closed .menu-text {
    display: none;
}

.sidebar.closed nav ul li:hover {
    background-color: inherit;
}
.main-content {
    flex-grow: 1;
    background-color: #f1f1f1;
    padding: 20px;
    position: relative; /* Required for positioning the form */
}

.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #ddd;
    padding-bottom: 20px;
    margin-bottom: 20px;
}

.header img {
    width: 50px;
    height: 70px;
}

.header h1 {
    margin: 0;
    font-size: 24px;
    flex-grow: 1;
    margin-left: 20px;
}

.date {
    font-size: 17px;
    color: #5e5d5d;
    font-weight: lighter;
}

.header .edit-button {
    background-color: black;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    text-align: center;
    font-size: 16px;
}

.expense-details {
    background-color: #f0f0f0;
    padding: 20px;
    border-radius: 10px;
}

.expense-details h2 {
    margin: 0 0 20px 0;
}

.expense-details p {
    margin: 10px 0;
}

.edit-button {
    display: inline-block;
    padding: 10px 20px;
    background-color: #000;
    color: white;
    border-radius: 5px;
    text-decoration: none;
    border: none;
    cursor: pointer;
}

.edit-button:hover {
    background-color: #555;
}

.header img {
    width: 14%;
    height: 100px;
    object-fit: cover;
    margin-bottom: 10px;
}

.note {
    margin-top: 20px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.note textarea {
    width: 100%;
    border: none;
    padding: 10px;
    font-size: 16px;
    resize: none;
    height: 100px;
}

.note .done-button {
    display: inline-block;
    padding: 10px 20px;
    background-color: #000000;
    color: white;
    border-radius: 5px;
    text-decoration: none;
    border: none;
    cursor: pointer;
    margin-top: 15px;
    margin-left:550px;

}

.note .done-button:hover {
    background-color: #858886;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: #fefefe;
    padding: 20px;
    border-radius: 10px;
    width: 400px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.modal-header h2 {
    margin: 0;
    font-size: 18px;
}

.modal-body {
    display: flex;
    flex-direction: column;
}

.modal-body label {
    margin-bottom: 10px;
    font-weight: bold;
}

.modal-body input,
.modal-body select,
.modal-body textarea {
    margin-bottom: 15px;
    padding: 10px;
    font-size: 16px;
    width: 100%;
    box-sizing: border-box;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
}

.modal-footer button {
    padding: 10px 20px;
    margin-left: 10px;
    border-radius: 5px;
    border: none;
    cursor: pointer;
    font-size: 16px;
}

.modal-footer .submit-btn {
    background-color: #000000;
    color: white;
}

.modal-footer .cancel-btn {
    background-color: #868484;
    color: rgb(0, 0, 0);
}