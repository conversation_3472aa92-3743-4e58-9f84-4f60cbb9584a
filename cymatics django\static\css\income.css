body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    display: flex;
    height: 100vh;
    flex-direction: column;
}
.container {
    display: flex;
    width: 100%;
    flex-grow: 1;
}
    .sidebar {
      background-color: #1e1e1e;
      color: white;
      width: 250px;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      transition: width 0.3s;
      position: relative;
  }
  .sidebar.closed {
    width: 60px;
}

/* Icon visibility and border */
.sidebar .toggle-icon {
    position: absolute;
    top: 25px !important; /* Aligned near the top */
    right: -8px; /* Adjusted to be right on the edge line */
    cursor: pointer;
    visibility: hidden;
    border: 3px solid rgba(78, 27, 231, 0.5); /* Light border */
    border-radius: 8px;
    padding: 1px;
    transition: visibility 0.3s ease-in-out, top 0.3s ease-in-out; /* Smooth transitions */
    z-index: 2;
}
#toggle-icon {
    width: 20px;
    height: 20px;
}


/* Adjust position for closed state to avoid overlap */
.sidebar.closed .toggle-icon {
    top: 10px;
    right: -8px; /* Keep it on the edge even when closed */
}

/* Show icon when hovering near the sidebar or over the icon */
.sidebar:hover .toggle-icon, .toggle-icon:hover {
    visibility: visible;
}

.sidebar .logo {
    padding: 20px;
    text-align: center;
}

.sidebar.closed .logo {
    display: none;
}

.sidebar nav ul {
    list-style: none;
    padding: 0;
    width: 100%;
    text-align: center;
}

.sidebar nav ul li {
    padding: 12px 20px;
    cursor: pointer;
    transition: background-color 0.3s, border-left 0.3s;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.sidebar.closed nav ul li {
    justify-content: center;
}

.sidebar nav ul li a {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: white;
    width: 100%;
    font-family: Arial, sans-serif;
}

.sidebar nav ul li a:hover {
    background-color: #555;
    border-left: 4px solid #ffcc00;
}

.menu-icon {
    margin-right: 10px;
    width: 24px;
    height: 24px;
}

.menu-text {
    transition: opacity 0.3s, visibility 0.3s;
    font-family: Arial, sans-serif;
}

.sidebar.closed .menu-text {
    display: none;
}

.sidebar.closed nav ul li:hover {
    background-color: inherit;
}

.profile-section {
    position: relative; /* Allows positioning of the dropdown */
    padding: 12px 20px; /* Match padding with other menu items */
    cursor: pointer; /* Change cursor on hover */
    transition: background-color 0.3s, border-left 0.3s; /* Smooth transition */
}

.profile-section:hover {
    background-color: #555; /* Background color on hover */
    border-left: 4px solid #ffcc00; /* Left border on hover */
}

.dropdown {
    position: absolute; /* Position relative to the profile section */
    bottom: 100%; /* Position above the profile section */
    left: 0; /* Align to the left */
    background-color: white; /* Background color of the dropdown */
    border: 1px solid #ccc; /* Border for the dropdown */
    border-radius: 4px; /* Rounded corners */
    z-index: 1000; /* Ensure it appears above other elements */
    width: 160px; /* Set width for the dropdown */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); /* Shadow for a floating effect */
    display: none; /* Initially hidden */
}

.dropdown ul {
    list-style: none; /* Remove default list styles */
    padding: 0; /* Remove padding */
    margin: 0; /* Remove margin */
}

.dropdown li {
    padding: 10px; /* Padding for each item */
    color: black; /* Set text color to black */
    cursor: pointer; /* Change cursor on hover */
}

.dropdown li:hover {
    background-color: #f1f1f1; /* Background on hover */
}

.main-content {
  flex-grow: 1;
  background-color: #f1f1f1;
  padding: 20px;
  position: relative; /* Required for positioning the form */
}

.upper-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    background-color: #fff;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
}

.upper-bar .total-income span:first-child {
    font-weight: bold;
    color: #333;
}

.upper-bar .total-income span:last-child {
    font-size: 24px;
    color: green;
}

.chart-section {
    margin-bottom: 30px;
}

.add-income {
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
}

.add-income-btn {
    padding: 15px 30px;
    background-color: #cacaca;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    font-size: 18px;
}

.add-income-btn:hover {
    background-color: #d0d0d0;
}

.header {
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-end;
}

.header form {
    display: flex;
    align-items: center;
}

.header form input[type="text"] {
    padding: 5px;
    border-radius: 5px;
    border: 1px solid #ddd;
    margin-right: 10px;
}

.header form button {
    padding: 5px 10px;
    background-color: #333;
    color: #fff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.income-table {
    flex-grow: 1;
    overflow-y: auto;
}

.income-table table {
    width: 100%;
    border-collapse: collapse;
}

.income-table table th,
.income-table table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.income-table table th {
    background-color: #f4f4f4;
}

.action-btn {
    padding: 5px 10px;
    border: none;
    background-color: #ccc;
    border-radius: 5px;
    cursor: pointer;
  }

.footer {
    display: flex;
    justify-content: center;
    padding: 10px 0;
    background-color: #333;
}

.footer .btn {
    font-size: 24px;
    color: #fff;
    background-color: #B24BF3;
    border: none;
    border-radius: 50%;
    padding: 10px 20px;
    cursor: pointer;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgb(0, 0, 0);
    background-color: rgba(0, 0, 0, 0.4);
    padding-top: 60px;
}

.modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #ddd;
    width: 80%;
    max-width: 500px;
    border-radius: 10px;
    position: relative;
}



.modal-content form {
    display: flex;
    flex-direction: column;
}

.modal-content form label {
    margin-bottom: 10px;
}

.modal-content form input,
.modal-content form button {
    padding: 10px;
    margin-bottom: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.modal-content form .form-actions {
    display: flex;
    justify-content: space-between;
}

.modal-content form .form-actions button {
    padding: 10px 20px;
}
.form-actions button {
    margin-left: 10px;
  }
  form button[type="submit"],
  form button[type="button"] {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    align-items : left;
  }
  
  form button[type="submit"] {
    background-color: black;
    color: white;
  }
  
  form button[type="button"] {
    background-color: #ccc;
  }



.footer {
    display: flex;
    justify-content: center;
    padding: 20px;
    background-color: white;
    margin-top: auto;
    border-top: 1px solid #ddd;
  }

.footer .btn {
    padding: 10px 20px;
    background-color: #000000;
    color: white;
    border:None;
    width: 100px;
    border-radius: 10px;
    cursor: pointer;
    font-size: 24px;

}