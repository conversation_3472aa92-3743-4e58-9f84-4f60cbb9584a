body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    display: flex;
    height: 100vh;
    flex-direction: column;
}

.container {
    display: flex;
    width: 100%;
    flex-grow: 1;
}
.sidebar {
    background-color: #1e1e1e;
    color: white;
    width: 250px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    transition: width 0.3s;
    position: relative;
}
.sidebar.closed {
    width: 60px;
}

/* Icon visibility and border */
.sidebar .toggle-icon {
    position: absolute;
    top: 25px !important; /* Aligned near the top */
    right: -8px; /* Adjusted to be right on the edge line */
    cursor: pointer;
    visibility: hidden;
    border: 3px solid rgba(78, 27, 231, 0.5); /* Light border */
    border-radius: 8px;
    padding: 1px;
    transition: visibility 0.3s ease-in-out, top 0.3s ease-in-out; /* Smooth transitions */
    z-index: 2;
}
#toggle-icon {
    width: 20px;
    height: 20px;
}


/* Adjust position for closed state to avoid overlap */
.sidebar.closed .toggle-icon {
    top: 10px;
    right: -8px; /* Keep it on the edge even when closed */
}

/* Show icon when hovering near the sidebar or over the icon */
.sidebar:hover .toggle-icon, .toggle-icon:hover {
    visibility: visible;
}

.sidebar .logo {
    padding: 20px;
    text-align: center;
}

.sidebar.closed .logo {
    display: none;
}
.sidebar nav ul {
    list-style: none;
    padding: 0;
    width: 100%;
    text-align: center;
}

.sidebar nav ul li {
    padding: 12px 20px;
    cursor: pointer;
    transition: background-color 0.3s, border-left 0.3s;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.sidebar.closed nav ul li {
    justify-content: center;
}

.sidebar nav ul li a {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: white;
    width: 100%;
    font-family: Arial, sans-serif;
}

.sidebar nav ul li a:hover {
    background-color: #555;
    border-left: 4px solid #ffcc00;
}
.menu-icon {
    margin-right: 10px;
    width: 24px;
    height: 24px;
}

.menu-text {
    transition: opacity 0.3s, visibility 0.3s;
    font-family: Arial, sans-serif;
}

.sidebar.closed .menu-text {
    display: none;
}

.sidebar.closed nav ul li:hover {
    background-color: inherit;
}

.main-content {
    flex-grow: 1;
    background-color: #f1f1f1;
    padding: 20px;
    position: relative; /* Required for positioning the form */
}
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header input[type="search"] {
    padding: 5px;
    font-size: 16px;
    width: 200px;
}

.completed-list {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.payment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.payment-item:last-child {
    border-bottom: none;
}

.payment-item img {
    width: 70px;
    height: 70px;
    border-radius: 8px;
    margin-right: 15px;
}

.payment-item-details {
    flex: 1;
    display: flex;
    align-items: center;
}

.payment-item-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.payment-item-subtitle {
    font-size: 14px;
    color: #888;
}

.call-button {
    background-color: #000;
    color: #fff;
    padding: 10px 20px;
    border-radius: 5px;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    margin-right: 10px;
}

.call-button svg {
    margin-right: 5px;
}

.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-button {
    background-color: transparent;
    border: none;
    cursor: pointer;
    font-size: 24px;
    padding: 10px;
}

.dropdown-content {
    display: none;
    position: absolute;
    right: 0;
    background-color: #f9f9f9;
    min-width: 120px;
    box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.2);
    z-index: 1;
    border-radius: 5px;
}

.dropdown-content button {
    color: black;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    background: none;
    border: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
}

.dropdown-content button:hover {
    background-color: #ddd;
}

.dropdown:hover .dropdown-content {
    display: block;
}



#editForm {
display: flex;
flex-direction: column;
}

#editForm label {
margin-top: 10px;
}

#editForm input,
#editForm select {
padding: 8px;
margin-top: 5px;
border: 1px solid #ccc;
border-radius: 4px;
}

#editForm button {
padding: 10px;
margin-top: 20px;
background-color: #000000;
color: #fff;
border: none;
border-radius: 4px;
cursor: pointer;
}

#editForm button:hover {
background-color: #555;
}


  /* Outsourcing Section Styles */
  #outsourcingDetails {
    margin-top: 10px;
}

#outsourcingDetails label {
    display: block;
    margin-top: 10px;
    font-weight: medium;
}

#outsourcingDetails select,
#outsourcingDetails input[type="number"] {
    width: 100%;
    padding: 8px;
    margin-top: 5px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.switch {
    position: relative;
    display: inline-block;
    width: 34px;
    height: 20px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 20px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    border-radius: 50%;
    transition: .4s;
}

input:checked + .slider {
    background-color: #000000;
}

input:checked + .slider:before {
    transform: translateX(14px);
}