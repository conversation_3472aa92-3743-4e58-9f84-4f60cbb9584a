{% load static %}
{% static "images" as baseurl %}
<!doctype html>
<html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link rel="stylesheet" type="text/css" href="https://npmcdn.com/flatpickr/dist/themes/dark.css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.5.0/font/bootstrap-icons.css">
        <title>Assets Dashboard</title>
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 0;
                display: flex;
                height: 100vh;
                flex-direction: column;
            }
            .container {
                display: flex;
                width: 100%;
                flex-grow: 1;
            }
                .sidebar {
                  background-color: #1e1e1e;
                  color: white;
                  width: 250px;
                  display: flex;
                  flex-direction: column;
                  justify-content: flex-start;
                  align-items: center;
                  transition: width 0.3s;
                  position: relative;
              }

              .sidebar.closed {
                  width: 60px;
              }

              /* Icon visibility and border */
              .sidebar .toggle-icon {
                  position: absolute;
                  top: 25px !important; /* Aligned near the top */
                  right: -8px; /* Adjusted to be right on the edge line */
                  cursor: pointer;
                  visibility: hidden;
                  border: 3px solid rgba(78, 27, 231, 0.5); /* Light border */
                  border-radius: 8px;
                  padding: 1px;
                  transition: visibility 0.3s ease-in-out, top 0.3s ease-in-out; /* Smooth transitions */
                  z-index: 2;
              }
              #toggle-icon {
                  width: 20px;
                  height: 20px;
              }


              /* Adjust position for closed state to avoid overlap */
              .sidebar.closed .toggle-icon {
                  top: 10px;
                  right: -8px; /* Keep it on the edge even when closed */
              }

              /* Show icon when hovering near the sidebar or over the icon */
              .sidebar:hover .toggle-icon, .toggle-icon:hover {
                  visibility: visible;
              }

              .sidebar .logo {
                  padding: 20px;
                  text-align: center;
              }

              .sidebar.closed .logo {
                  display: none;
              }

              .sidebar nav ul {
                  list-style: none;
                  padding: 0;
                  width: 100%;
                  text-align: center;
              }

              .sidebar nav ul li {
                  padding: 12px 20px;
                  cursor: pointer;
                  transition: background-color 0.3s, border-left 0.3s;
                  display: flex;
                  justify-content: flex-start;
                  align-items: center;
              }

              .sidebar.closed nav ul li {
                  justify-content: center;
              }

              .sidebar nav ul li a {
                  display: flex;
                  align-items: center;
                  text-decoration: none;
                  color: white;
                  width: 100%;
                  font-family: Arial, sans-serif;
              }

              .sidebar nav ul li a:hover {
                  background-color: #555;
                  border-left: 4px solid #ffcc00;
              }

              .menu-icon {
                  margin-right: 10px;
                  width: 24px;
                  height: 24px;
              }

              .menu-text {
                  transition: opacity 0.3s, visibility 0.3s;
                  font-family: Arial, sans-serif;
              }

              .sidebar.closed .menu-text {
                  display: none;
              }

              .sidebar.closed nav ul li:hover {
                  background-color: inherit;
              }


            .main-content {
                flex-grow: 1;
                background-color: #f1f1f1;
                padding: 20px;
                position: relative; /* Required for positioning the form */
            }
            .profile-section {
                position: relative; /* Allows positioning of the dropdown */
                padding: 12px 20px; /* Match padding with other menu items */
                cursor: pointer; /* Change cursor on hover */
                transition: background-color 0.3s, border-left 0.3s; /* Smooth transition */
            }

            .profile-section:hover {
                background-color: #555; /* Background color on hover */
                border-left: 4px solid #ffcc00; /* Left border on hover */
            }

            .dropdown {
                position: absolute; /* Position relative to the profile section */
                bottom: 100%; /* Position above the profile section */
                left: 0; /* Align to the left */
                background-color: white; /* Background color of the dropdown */
                border: 1px solid #ccc; /* Border for the dropdown */
                border-radius: 4px; /* Rounded corners */
                z-index: 1000; /* Ensure it appears above other elements */
                width: 160px; /* Set width for the dropdown */
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); /* Shadow for a floating effect */
                display: none; /* Initially hidden */
            }

            .dropdown ul {
                list-style: none; /* Remove default list styles */
                padding: 0; /* Remove padding */
                margin: 0; /* Remove margin */
            }

            .dropdown li {
                padding: 10px; /* Padding for each item */
                color: black; /* Set text color to black */
                cursor: pointer; /* Change cursor on hover */
            }

            .dropdown li:hover {
                background-color: #f1f1f1; /* Background on hover */
            }
            .user-icon {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                background-color: #ddd;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                font-size: 18px;
                color: #0e0e0e;
                background-color: #e1ecb8;

            }
            .header {
                display: flex;
                justify-content: flex-end;
                align-items: center;
                margin-bottom: 20px;
            }

            .search-bar {
                display: flex;
                align-items: center;
            }

            .search-bar input {
                padding: 10px;
                border: 1px solid #ccc;
                border-radius: 5px;
                margin-right: 10px;
            }

            .add-btn {
                padding: 10px 20px;
                background-color: black;
                color: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
            }

            .edit-btn {
                padding: 5px 10px;
                border: none;
                background-color: transparent;
                cursor: pointer;
                margin-left: 10px;
                align-self: flex-end;
                font-size: 18px;
            }

            .assets-table {
                background-color: white;
                padding: 20px;
                border-radius: 10px;
                flex-grow: 1;

            }

            .asset-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 10px 15px;
                border-bottom: 1px solid #ccc;
                cursor: pointer;
                transition: background-color 0.3s;
            }

            .asset-item:hover {
                background-color: #f0f0f0;
            }

            .asset-icon {
                margin-right: 20px;
                width: 50px;
                height: 50px;
            }

            .asset-details {
                display: flex;
                flex-direction: column;
                flex-grow: 1;
            }

            .asset-type {
                font-weight: normal;
            }

            .asset-value {
                font-size: 18px;
                font-weight: bold;
            }

            .asset-name {
                color: #888;
            }

            .modal {
                display: none;
                position: fixed;
                z-index: 1;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                overflow: auto;
                background-color: rgba(0, 0, 0, 0.5);
                justify-content: center;
                align-items: center;
            }

            .modal-content {
                background-color: white;
                margin: 5% auto;
                padding: 20px;
                border: 1px solid #888;
                width: 400px;
                border-radius: 10px;
            }

            .modal-content form {
                display: flex;
                flex-direction: column;
            }

            .modal-content form label {
                margin: 10px 0 5px;
            }

            .modal-content form input,
            .modal-content form select {
                padding: 10px;
                border: 1px solid #ccc;
                border-radius: 5px;
                width: 100%;
                box-sizing: border-box;
            }

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    }

            .close:hover,
            .close:focus {
                color: black;
                text-decoration: none;
                cursor: pointer;
            }

            .form-actions {
                display: flex;
                justify-content: space-between;
                margin-top: 20px;
            }

            .submit-btn,
            .cancel-btn {
                flex: 1;
                padding: 10px;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                margin: 0 5px;
            }

            .submit-btn {
                background-color: #000;
                color: white;
            }

            .cancel-btn {
                background-color: #ffffff;
                color: black;
                border: 1px solid #888 !important;
            }

            /* Dropdown Menu Styles */
            .dropdown1 {
                position: relative;
                display: inline-block;
            }

            .dropdown-content1 {
                display: none;
                position: absolute;
                background-color: white;
                min-width: 140px;
                box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
                z-index: 1;
                right: 0;
                border-radius: 8px;
                overflow: auto;
            }

            .last-item .dropdown-content1 {
                bottom: 100%;
                top: auto;
                transform: translateY(-10px);
            }

            .dropdown-content1 button {
                color: black;
                padding: 12px 16px;
                text-decoration: none;
                display: block;
                border: none;
                width: 100%;
                text-align: left;
                background: white;
                cursor: pointer;
                font-weight: 400;
                font-size:15px;
            }

            .dropdown-content1 button:hover {
                background-color: #f1f1f1;
            }

            .dropdown1:hover .dropdown-content1 {
                display: block;
            }

            /* Forced Colors Mode styles */
            @media (forced-colors: active) {
                body {
                    background-color: Canvas; /* System background color */
                    color: Text; /* System text color */
                }
                .sidebar {
                    background-color: Window; /* System background color for sidebar */
                }
                .modal-content {
                    background-color: Window; /* System background color for modal */
                    color: Text; /* System text color for modal */
                }
                .add-btn,
                .submit-btn {
                    background-color: ButtonFace; /* System button background */
                    color: ButtonText; /* System button text color */
                }
                .cancel-btn {
                    background-color: #ffffff;
                    color: black;
                    border: 1px solid #888 !important;
                }
                @media (max-width: 768px) {
                    .sidebar {
                        width: 180px;
                    }

                    .sidebar.closed {
                        width: 50px;
                    }
                }

            }
            .action-btn {
                background-color: transparent;
                border: none;
                font-size: 28px;
                color: black;
                width: 35px;
                height: 35px;
                border-radius: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                line-height: 0;
                margin-left: 10px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <aside class="sidebar">
                <div class="toggle-icon" id="toggle-icon">
                    <img src="{% static 'images/menuleft.png' %}" alt="icon" id="toggle-icon" width="16" height="16">
                </div>
                <div class="logo-container">
                    <img src="{% static './images/logowhite.png' %}" alt="logo" width="50" height="50" class="logo">
                </div>
                <nav>
                    <ul>
                        <li class="menu-item"><a href="{% url 'dashboard' %}"><img src="{% static 'images/dashboard.png' %}" class="menu-icon"><span class="menu-text">Dashboard</span></a></li>
                        <li class="menu-item"><a href="{% url 'projects' %}"><img src="{% static 'images/project.png' %}" class="menu-icon"><span class="menu-text">Project</span></a></li>
                        <li class="menu-item"><a href="{% url 'incomef_view' %}"><img src="{% static 'images/income.png' %}" class="menu-icon"><span class="menu-text">Income</span></a></li>
                        <li class="menu-item"><a href="{% url 'expense' %}"><img src="{% static 'images/expenses.png' %}" class="menu-icon"><span class="menu-text">Expense</span></a></li>
                        <li class="menu-item"><a href="{% url 'calendar' %}"><img src="{% static 'images/calendar.png' %}" class="menu-icon"><span class="menu-text">Calendar</span></a></li>
                        <li class="menu-item"><a href="{% url 'allproject' %}"><img src="{% static 'images/All projects.png' %}" class="menu-icon"><span class="menu-text">All Projects</span></a></li>
                        <li class="menu-item"><a href="{% url 'clientsbook' %}"><img src="{% static 'images/clientsbook.png' %}" class="menu-icon"><span class="menu-text">Clients Book</span></a></li>
                        <li class="menu-item"><a href="{% url 'clients' %}"><img src="{% static 'images/Clients.png' %}" class="menu-icon"><span class="menu-text">Clients</span></a></li>
                        <li class="menu-item"><a href="{% url 'status' %}"><img src="{% static 'images/status.png' %}" class="menu-icon"><span class="menu-text">Status</span></a></li>
                        <li class="menu-item"><a href="{% url 'pending_pay' %}"><img src="{% static 'images/pending.png' %}" class="menu-icon"><span class="menu-text">Pending Payments</span></a></li>
                        <li class="menu-item"><a href="{% url 'project_map' %}"><img src="{% static 'images/map.png' %}" class="menu-icon"><span class="menu-text">Map</span></a></li>
                        <li class="menu-item active"><a href="{% url 'assets' %}"><img src="{% static 'images/Assets.png' %}" class="menu-icon"><span class="menu-text">Assets</span></a></li>
                        <li class="menu-item"><a href="{% url 'budget' %}"><img src="{% static 'images/budget.png' %}" class="menu-icon"><span class="menu-text">Budget</span></a></li>
                        <li class="menu-item"><a href="{% url 'entertainment' %}"><img src="{% static 'images/Entertainment.png' %}" class="menu-icon"><span class="menu-text">Entertainment</span></a></li>
                    </ul>
                </nav>
                <div class="profile-section" id="profileMenu">
                    <div class="user-icon" id="userIcon">
                      <!-- Default content in case JS is not available -->
                      U
                  </div>
                    <span class="menu-text" id="name">{{ user.username }}</span>
                    <div class="dropdown" id="profileDropdown">
                        <ul>
                            <li><a href="{% url 'profile' %}">View Profile</a></li>
                            <li><a href="{% url 'logout_view' %}">Sign Out</a></li>
                        </ul>
                    </div>
                  </div>
            </aside>
            <main class="main-content">
                <header class="header">
                    <div class="search-bar">
                        <!-- search box -->
                        <form method="get" action="{% url 'assets' %}">
                            {% csrf_token %}
                            <input type="text" name="q" placeholder="search..." value="{{ query }}">

                        </form>
                        <button class="add-btn" onclick="openForm()">+ Add</button>
                    </div>
                </header>
                <section class="assets-table">
                    {% for item in objs %}
                    <div class="asset-item" onclick="moveToDetailPage('{{ item.id }}')">
                        {% if item.image %}
                            <img src="{{ item.image.url }}" alt="Asset Icon" class="asset-icon">
                        {% else %}
                            <img src="{% static 'images/Assets.png' %}" alt="Default Asset Icon" class="asset-icon">
                        {% endif %}
                        <div class="asset-details">
                            <div class="asset-type">{{ item.type }}</div>
                            <div class="asset-value">{{ item.value }}</div>
                            <div class="asset-name">{{ item.name }}</div>
                        </div>
                        <div class="dropdown1">
                            <button class="action-btn" onclick="toggleDropdown(event)">
                                <i class="bi bi-three-dots"></i>
                            </button>
                            <div class="dropdown-content1">
                                <button class="edit-Btn" onclick="openEditForm()" data-id="{{ item.id }}">Edit</button>
                                <button class="delete-btn" onclick="deleteAsset()" data-id="{{ item.id }}">Delete</button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </section>
            </main>

            <!-- Add Item Form -->
            <div id="addModal" class="modal">
                <div class="modal-content" onclick="event.stopPropagation();">
                    <span class="close" onclick="closeForm()">&times;</span>
                    <h2>Add Item</h2>
                    <form id="addForm" method="POST" action="{% url 'assets' %}" enctype="multipart/form-data">
                        {% csrf_token %}
                        <label for="date">Date</label>
                        <input type="date" id="date" name="date">

                        <label for="type">Type</label>
                        <select id="type" name="type">
                            <option value="cash">Cash</option>
                            <option value="crypto">Crypto</option>
                            <option value="stocks">Stocks</option>
                            <option value="real_estate">Real Estate</option>
                        </select>

                        <label for="name">Name</label>
                        <input type="text" id="name" name="name">

                        <label for="qty">Qty</label>
                        <input type="number" id="qty" name="qty">

                        <label for="buyprice">Buy Price</label>
                        <input type="number" id="buyprice" name="buyprice">

                        <label for="note">Note</label>
                        <input type="text" id="note" name="note">

                        <label for="image">Image</label>
                        <input type="file" id="image" name="image">

                        <div class="form-actions">
                            <button type="submit" class="submit-btn" id="addBtn">Submit</button>
                            <button type="button" class="cancel-btn" onclick="closeForm()">Cancel</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Edit Item Form -->
            <div id="editModal" class="modal">
                <div class="modal-content" onclick="event.stopPropagation();">
                    <span class="close" onclick="closeEditForm()">&times;</span>
                    <h2>Edit Item</h2>
                    <form id="editForm" method="POST" enctype="multipart/form-data">
                        {% csrf_token %}
                        <label for="editdate">Date</label>
                        <input type="date" id="editdate" name="editdate">
                        <label for="editcategory">Type</label>
                        <input type="text" id="editcategory" name="editcategory">
                        <label for="editName">Name</label>
                        <input type="text" id="editName" name="editName">
                        <label for="editqty">Qty</label>
                        <input type="number" id="editqty" name="editqty">
                        <label for="editbuyprice">Buy Price</label>
                        <input type="number" id="editbuyprice" name="editbuyprice">
                        <label for="editvalue">Value</label>
                        <input type="text" id="editvalue" name="editvalue">
                        <label for="editnote">Note</label>
                        <input type="text" id="editnote" name="editnote">
                        <label for="editimage">Image</label>
                        <input type="file" id="editimage" name="editimage">
                        <p id="image-file-name"></p> <!-- This will show the previously uploaded or newly selected file name -->
                        <div class="form-actions">
                            <button type="submit" class="submit-btn">Submit</button>
                            <button type="button" id="cancelBtn" class="cancel-btn" onclick="closeEditForm()">Cancel</button>
                        </div>
                    </form>
                </div>
            </div>
        </div> <!-- Closing container div -->
        <script src="{% static './js/assets.js' %}"></script>
    </body>
</html>

<script>
    function openForm() {
        document.getElementById("addModal").style.display = "flex";
    }

    function closeForm() {
        document.getElementById("addModal").style.display = "none";
    }

    function openEditForm(event) {
        event.stopPropagation(); // Prevent the click event from bubbling up
        document.getElementById("editForm").style.display = "flex";
    }

    function closeEditForm() {
        document.getElementById("editForm").style.display = "none";
    }

    function deleteAsset(event) {
        const assetItem = event.target.closest('.asset-item');
        assetItem.remove();
    }

    // Close all dropdowns when clicking outside
    document.addEventListener('click', function(event) {
        const dropdowns = document.querySelectorAll('.dropdown-content');
        dropdowns.forEach(dropdown => {
            dropdown.style.display = 'none';
        });
    });

    // Toggle dropdown on button click
    function toggleDropdown(event) {
        event.stopPropagation(); // Prevent the click event from bubbling up
        const dropdownContent = event.target.nextElementSibling;

        // Hide all dropdowns
        const allDropdowns = document.querySelectorAll('.dropdown-content');
        allDropdowns.forEach(dropdown => {
            dropdown.style.display = 'none';
        });

        // Toggle the clicked dropdown
        dropdownContent.style.display = dropdownContent.style.display === 'block' ? 'none' : 'block';
    }
</script>


<script>// add form

    document.getElementById('addBtn').addEventListener('submit', function(event) {
        event.preventDefault();

        var form = this;
        var formData = new FormData(form);

        fetch(form.action, {
            method: form.method,
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (response.headers.get('content-type')?.includes('application/json')) {
                return response.json();
            } else {
                return response.text().then(text => { throw new Error(text) });
            }
        })
        .then(data => {
            if (data.success) {
                form.reset();
                closeForm();
                location.reload();
            } else {
                var errorMessage = document.getElementById("error-message");
                errorMessage.textContent = data.error;
                errorMessage.style.display = "block";
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById("error-message").textContent = 'An error occurred: ' + error.message;
            document.getElementById("error-message").style.display = "block";
        });
    });


</script>


<script>// edit form

    $(document).ready(function() {
        var modal = $('#editModal');
        var span = $('.close');
        var cancelBtn = $('#cancelBtn');

        $.ajaxSetup({
            beforeSend: function(xhr, settings) {
                if (!this.crossDomain) {
                    xhr.setRequestHeader("X-CSRFToken", getCookie('csrftoken'));
                }
            }
        });

        function getCookie(name) {
            var cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                var cookies = document.cookie.split(';');
                for (var i = 0; i < cookies.length; i++) {
                    var cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // Close the modal
        span.on('click', function() {
            modal.hide();
        });

        cancelBtn.on('click', function() {
            modal.hide();
        });

        function convertToDate(dateStr) {
            const date = new Date(dateStr);
            const pad = (num) => (num < 10 ? '0' + num : num);
            const localDate = date.getFullYear() + '-' +
                               pad(date.getMonth() + 1) + '-' +
                               pad(date.getDate());
            return localDate;
        }

        function openModal(ast_id) {
            if (ast_id) {
                $.ajax({
                    url: '/get_asset_data/' + ast_id + '/',
                    method: 'GET',
                    success: function(data) {
                        $('#editForm').attr('data-edit-id', ast_id); // Set the edit code
                        $('#editdate').val(convertToDate(data.date));
                        $('#editcategory').val(data.type);
                        $('#editName').val(data.name);
                        $('#editqty').val(data.qty);
                        $('#editbuyprice').val(data.buy);
                        $('#editvalue').val(data.value);
                        $('#editnote').val(data.note);

                        // Display the previously uploaded file name
                        if (data.image) {
                            $('#image-file-name').text("Previously uploaded file: " + data.image.split('/').pop());
                        } else {
                            $('#image-file-name').text("No file uploaded.");
                        }

                        modal.show();
                    },
                    error: function() {
                        alert('Failed to fetch data. Please try again.');
                    }
                });
            } else {
                $('#editForm').removeAttr('data-edit-id'); // Clear the edit code for new projects
                modal.show();
            }
        }

        // Attach click event to edit buttons
        $('.edit-Btn').on('click', function(event) {
            event.preventDefault(); // Prevent default link behavior
            event.stopPropagation();
            var id = $(this).data('id'); // Get the project code from the button
            openModal(id);
        });

        $('#editForm').on('submit', function(event) {
            event.preventDefault();
            var id = $('#editForm').attr('data-edit-id'); // Get the edit code
            var url = '/edit_asset/' + (id ? id + '/' : ''); // Ensure URL includes code if available

            // Create FormData object
            var formData = new FormData(this);

            $.ajax({
                url: url,
                method: 'POST',
                data: formData,
                processData: false,  // Prevent jQuery from automatically transforming the data into a query string
                contentType: false,  // Let the browser set the content type automatically (needed for file uploads)

                success: function(response) {
                    if (response.success) {
                        alert('Form submitted successfully!');
                        modal.hide();
                        location.reload();  // Reload the page to reflect changes
                    } else {
                        alert('Failed to submit form: ' + response.error);
                    }
                },
                error: function() {
                    alert('An error occurred. Please try again.');
                }
            });
        });

        // Show selected file name when a file is chosen
        $('#image').on('change', function() {
            var fileName = $(this).val().split('\\').pop();  // Get the file name only
            $('#image-file-name').text("Selected file: " + fileName);
        });
    });
</script>

<script> // deletion

    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.delete-btn').forEach(button => {
            button.addEventListener('click', function(event) {
                event.preventDefault();
                event.stopPropagation();
                const astId = this.getAttribute('data-id');
                if (confirm('Are you sure you want to delete this asset?')) {
                    fetch(`/delete_asset/${astId}/`, {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': getCookie('csrftoken'),
                        },
                    }).then(response => {
                        if (response.ok) {
                            window.location.reload();  // Refresh the page to reflect changes
                        } else {
                            alert('Failed to delete the project.');
                        }
                    }).catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred while deleting the project.');
                    });
                }
            });
        });

        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    });
</script>
<script>
    // page navigation from div
    function moveToDetailPage(ast_id) {
        window.location.href = "/assetd/" + ast_id + "/";
    }
</script>
<script>
    // JavaScript to handle dropdown visibility
    const profileMenu = document.getElementById('profileMenu');
    const profileDropdown = document.getElementById('profileDropdown');

    profileMenu.addEventListener('click', function () {
        // Toggle dropdown visibility
        if (profileDropdown.style.display === 'none' || profileDropdown.style.display === '') {
            profileDropdown.style.display = 'block';
        } else {
            profileDropdown.style.display = 'none';
        }
    });

    // Close dropdown if clicked outside
    window.addEventListener('click', function (event) {
        if (!profileMenu.contains(event.target)) {
            profileDropdown.style.display = 'none';
        }
    });
</script>

<script>
    const sidebar = document.querySelector('.sidebar');
    const toggleIcon = document.getElementById('toggle-icon');

    toggleIcon.addEventListener('click', function() {
      if (sidebar.classList.contains('closed')) {
        sidebar.classList.remove('closed');
        toggleIcon.src = "{% static 'images/menuleft.png' %}";  // Change to the "left-chevron" when open
      } else {
        sidebar.classList.add('closed');
        toggleIcon.src = "{% static 'images/menuright.png' %}";  // Change to the "chevron" when closed
      }
    });
  </script>
<script>
    // user icon
    const username = document.getElementById('name').textContent;
    document.querySelector('#userIcon').innerText = username.charAt(0);
  </script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script>
        config={
            enableTime:false,

        }
        flatpickr("input[type=date]",config);
    </script>
