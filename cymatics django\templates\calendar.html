{% load static %}
{% static "images" as baseurl %}
<!DOCTYPE html>
<html lang='en'>
<head>
  <meta charset='utf-8' />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://maxcdn.bootstrapcdn.com/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" type="text/css" href="https://npmcdn.com/flatpickr/dist/themes/dark.css">
  <script src='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.15/index.global.min.js'></script>

  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
  <script src="https://maxcdn.bootstrapcdn.com/bootstrap/5.3.0/js/bootstrap.min.js"></script>
  <title>Calendar</title>
  <style>
    body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 0;
        display: flex;
        height: 100vh;
        flex-direction: column;
    }
    .container {
        display: flex;
        width: 100%;
        flex-grow: 1;
    }
        .sidebar {
          background-color: #1e1e1e;
          color: white;
          width: 250px;
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: center;
          transition: width 0.3s;
          position: relative;
      }
      .sidebar.closed {
        width: 60px;
    }

    /* Icon visibility and border */
    .sidebar .toggle-icon {
        position: absolute;
        top: 25px !important; /* Aligned near the top */
        right: -8px; /* Adjusted to be right on the edge line */
        cursor: pointer;
        visibility: hidden;
        border: 3px solid rgba(78, 27, 231, 0.5); /* Light border */
        border-radius: 8px;
        padding: 1px;
        transition: visibility 0.3s ease-in-out, top 0.3s ease-in-out; /* Smooth transitions */
        z-index: 2;
    }
    #toggle-icon {
        width: 20px;
        height: 20px;
    }


    /* Adjust position for closed state to avoid overlap */
    .sidebar.closed .toggle-icon {
        top: 10px;
        right: -8px; /* Keep it on the edge even when closed */
    }

    /* Show icon when hovering near the sidebar or over the icon */
    .sidebar:hover .toggle-icon, .toggle-icon:hover {
        visibility: visible;
    }

    .sidebar .logo {
        padding: 20px;
        text-align: center;
    }

    .sidebar.closed .logo {
        display: none;
    }

    .sidebar nav ul {
        list-style: none;
        padding: 0;
        width: 100%;
        text-align: center;
    }

    .sidebar nav ul li {
        padding: 12px 20px;
        cursor: pointer;
        transition: background-color 0.3s, border-left 0.3s;
        display: flex;
        justify-content: flex-start;
        align-items: center;
    }

    .sidebar.closed nav ul li {
        justify-content: center;
    }

    .sidebar nav ul li a {
        display: flex;
        align-items: center;
        text-decoration: none;
        color: white;
        width: 100%;
        font-family: Arial, sans-serif;
    }

    .sidebar nav ul li a:hover {
        background-color: #555;
        border-left: 4px solid #ffcc00;
    }

    .menu-icon {
        margin-right: 10px;
        width: 24px;
        height: 24px;
    }

    .menu-text {
        transition: opacity 0.3s, visibility 0.3s;
        font-family: Arial, sans-serif;
    }

    .sidebar.closed .menu-text {
        display: none;
    }

    .sidebar.closed nav ul li:hover {
        background-color: inherit;
    }

    .profile-section {
        position: relative; /* Allows positioning of the dropdown */
        padding: 12px 20px; /* Match padding with other menu items */
        cursor: pointer; /* Change cursor on hover */
        transition: background-color 0.3s, border-left 0.3s; /* Smooth transition */
    }

    .profile-section:hover {
        background-color: #555; /* Background color on hover */
        border-left: 4px solid #ffcc00; /* Left border on hover */
    }

    .dropdown {
        position: absolute; /* Position relative to the profile section */
        bottom: 100%; /* Position above the profile section */
        left: 0; /* Align to the left */
        background-color: white; /* Background color of the dropdown */
        border: 1px solid #ccc; /* Border for the dropdown */
        border-radius: 4px; /* Rounded corners */
        z-index: 1000; /* Ensure it appears above other elements */
        width: 160px; /* Set width for the dropdown */
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); /* Shadow for a floating effect */
        display: none; /* Initially hidden */
    }

    .dropdown ul {
        list-style: none; /* Remove default list styles */
        padding: 0; /* Remove padding */
        margin: 0; /* Remove margin */
    }

    .dropdown li {
        padding: 10px; /* Padding for each item */
        color: black; /* Set text color to black */
        cursor: pointer; /* Change cursor on hover */
    }

    .dropdown li:hover {
        background-color: #f1f1f1; /* Background on hover */
    }

    .main-content {
      flex-grow: 1;
      background-color: #f1f1f1;
      padding: 20px;
      position: relative; /* Required for positioning the form */
    }
    #calendar {
      max-width: 90%;
      margin: 0 auto;
      padding: 20px;
      flex-grow: 1;
    }

    /* Updated Add Event Styles */
    #eventForm {
      display: none;
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
      padding: 20px;
      z-index: 1000;
      width: 300px;
      max-width: 60%;
    }
    #eventForm h3 {
      margin-top: 0;
      text-align: left;
    }
    #eventForm label {
      display: block;
      margin-top: 10px;
    }
    #eventForm input,
    #eventForm select {
      width: 100%;
      padding: 12px;
      margin-top: 5px;
      border: 1px solid #ccc;
      border-radius: 4px;
      box-sizing: border-box; /* Ensure padding is included in width */
    }

   /* Updated Add Event Styles */
.event-form button {
    padding: 10px;
    margin-right: 10px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
    width: 100px; /* Set a fixed width to make both buttons the same size */
    text-align: center;
}

/* Specific Styles for Save and Cancel buttons */
.event-form #saveEvent,
.edit-form #submitEdit {
    background-color: #000000;
    color: white;
}

.event-form #cancelEvent,
.edit-form #cancelEdit {
    background-color: #ffffff;
    color: black;
    border: 1px solid #888 !important;
}

/* Hover effect for both buttons */
.event-form button:hover,
.edit-form button:hover {
    opacity: 0.8;
}


    /* Modal Styles */
    .modal {
        display: none;
        position: fixed;
        z-index: 1;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0,0,0,0.4);
    }
    .modal-content {
        background-color: #fefefe;
        margin: 15% auto;
        padding: 20px;
        border-radius: 8px;
        border: 1px solid #888;
        width: 28%;
    }
    .close {
        color: #aaa;
        float: right;
        font-size: 28px;
        font-weight: bold;
    }
    .close:hover,
    .close:focus {
        color: black;
        text-decoration: none;
        cursor: pointer;
    }
    #editForm {
      display: flex;
      flex-direction: column;
    }
    #editForm label {
      margin-top: 10px;
    }
    #editForm input,
    #editForm select {
      padding: 8px;
      margin-top: 5px;
      border: 1px solid #ccc;
      border-radius: 4px;
    }
    #editForm button[type="submit"] {
      padding: 10px;
      margin-top: 20px;
      background-color: #000000;
      color: #fff;
      border: none;
      border-radius: 4px;
    }
    #editForm button[type="button"] {
      padding: 10px;
      margin-top: 20px;
      background-color: #e8e5e5;
      color: #070707;
      border: none;
      border-radius: 4px;
    }
    #editForm button:hover {
      background-color: #555;
    }
    #editForm button[type="button"] {
        background-color: #ffffff;
    color: black;
    border: 1px solid #888 !important;
      }


/* Overlay */
.overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none; /* Hidden by default */
        }

        /* Event Display Styles */
        .event-title {
            background-color: black;
            color: white;
            padding: 5px;
            border-radius: 5px;
            margin: 0;
            width:100%;
            cursor: pointer; /* Make event title clickable for editing */
            text-align: center; /* Center align the text */

        }
         /* Event Form Styles */

        .event-form input {
            width: 90%;
            padding: 8px;
            margin-bottom: 13px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-weight: 200;
        }

        .event-form button{
            padding: 10px;
            margin-right: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .event-form #saveEvent, .edit-form #submitEdit{

            background-color: #000000;
            color: white;
        }

        .event-form #cancelEvent, .edit-form #cancelEdit {
            background-color: #ffffff;
            color: black;
            border: 1px solid #888 !important;
        }

        .event-form button:hover, .edit-form button:hover {
            opacity: 0.8;
        }

        .fc-event {
        background-color: black !important; /* Black background */
        color: white !important; /* White text for contrast */
        border: none !important; /* Remove any border */
        padding: 5px;
        border-radius: 3px;
    }
    .form-actions {
                display: flex;
                justify-content: flex-end;
                margin-top: 20px;
            }

            .form-actions button {
                margin-left: 10px;
            }

            /* Outsourcing Section Styles */
            #outsourcingDetails {
                margin-top: 10px;
            }

            #outsourcingDetails label {
                display: block;
                margin-top: 10px;
                font-weight: medium;
            }

            #outsourcingDetails select,
            #outsourcingDetails input[type="number"] {
                width: 100%;
                padding: 8px;
                margin-top: 5px;
                border: 1px solid #ccc;
                border-radius: 4px;
            }

            #addOutsourcingDetails {
                margin-top: 10px;
            }

            #addOutsourcingDetails label {
                display: block;
                margin-top: 10px;
                font-weight: medium;
            }

            #addOutsourcingDetails select,
            #addOutsourcingDetails input[type="number"] {
                width: 100%;
                padding: 8px;
                margin-top: 5px;
                border: 1px solid #ccc;
                border-radius: 4px;
            }

            .switch {
                position: relative;
                display: inline-block;
                width: 34px;
                height: 20px;
            }

            .switch input {
                opacity: 0;
                width: 0;
                height: 0;
            }

            .slider {
                position: absolute;
                cursor: pointer;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: #ccc;
                transition: .4s;
                border-radius: 20px;
            }

            .slider:before {
                position: absolute;
                content: "";
                height: 16px;
                width: 16px;
                left: 2px;
                bottom: 2px;
                background-color: white;
                border-radius: 50%;
                transition: .4s;
            }

            input:checked + .slider {
                background-color: #000000;
            }

            input:checked + .slider:before {
                transform: translateX(14px);
            }
    .modal-content h2 {
            text-align: center;
            margin-bottom: 20px;
            font-size:19px;
           }

        #outsourcingAmount {
    width: 100% !important; /* Ensure this width is applied */
    max-width: 100% !important; /* Limit the width */
    box-sizing: border-box; /* Ensure padding is included in the width */
}


 .btn-check {
    display: none; /* Hide the checkbox */
}

.btn-primary {
    background-color: #ffffff !important; /* Ensure white background */
    color: black !important; /* Ensure black text */
    border: 2px solid #ccc;
    border-radius: 10px;
    padding: 10px 15px;
    cursor: pointer;
    margin: 5px; /* Add margin to create gaps */
    transition: background-color 0.3s, color 0.3s, border-color 0.3s, box-shadow 0.3s;
    font-weight: bold;
    font-size: 14px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.btn-primary.active {
    background-color: #000000; /* Even darker blue */
    border-color: #000000; /* Keep the border color */
    color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

#statusButtons {
    display: flex; /* Align buttons in a row */
    justify-content: flex-start; /* Align to the start */
}

.user-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: #ddd;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    font-size: 18px;
    color: #0e0e0e;
    background-color: #e1ecb8;

}
  </style>
</head>
<body>
  <div class="container">
    <aside class="sidebar closed">
      <div class="toggle-icon">
        <img src="{% static 'images/menuleft.png' %}" alt="icon" id="toggle-icon" width="16" height="16">
      </div>
      <div class="logo">
        <img src="{% static 'images/logowhite.png' %}" alt="logo" width="50" height="50">
      </div>
      <nav>
        <ul>
          <li class="menu-item"><a href="{% url 'dashboard' %}"><img src="{% static 'images/dashboard.png' %}" class="menu-icon"><span class="menu-text">Dashboard</span></a></li>
          <li class="menu-item"><a href="{% url 'projects' %}"><img src="{% static 'images/project.png' %}" class="menu-icon"><span class="menu-text">Project</span></a></li>
          <li class="menu-item"><a href="{% url 'incomef_view' %}"><img src="{% static 'images/income.png' %}" class="menu-icon"><span class="menu-text">Income</span></a></li>
          <li class="menu-item"><a href="{% url 'expense' %}"><img src="{% static 'images/expenses.png' %}" class="menu-icon"><span class="menu-text">Expense</span></a></li>
          <li class="menu-item active"><a href="{% url 'calendar' %}"><img src="{% static 'images/calendar.png' %}" class="menu-icon"><span class="menu-text">Calendar</span></a></li>
          <li class="menu-item"><a href="{% url 'allproject' %}"><img src="{% static 'images/All projects.png' %}" class="menu-icon"><span class="menu-text">All Projects</span></a></li>
          <li class="menu-item"><a href="{% url 'clientsbook' %}"><img src="{% static 'images/clientsbook.png' %}" class="menu-icon"><span class="menu-text">Clients Book</span></a></li>
          <li class="menu-item"><a href="{% url 'clients' %}"><img src="{% static 'images/Clients.png' %}" class="menu-icon"><span class="menu-text">Clients</span></a></li>
          <li class="menu-item"><a href="{% url 'status' %}"><img src="{% static 'images/status.png' %}" class="menu-icon"><span class="menu-text">Status</span></a></li>
          <li class="menu-item"><a href="{% url 'pending_pay' %}"><img src="{% static 'images/pending.png' %}" class="menu-icon"><span class="menu-text">Pending Payments</span></a></li>
          <li class="menu-item"><a href="{% url 'project_map' %}"><img src="{% static 'images/map.png' %}" class="menu-icon"><span class="menu-text">Map</span></a></li>
          <li class="menu-item"><a href="{% url 'assets' %}"><img src="{% static 'images/Assets.png' %}" class="menu-icon"><span class="menu-text">Assets</span></a></li>
          <li class="menu-item"><a href="{% url 'budget' %}"><img src="{% static 'images/budget.png' %}" class="menu-icon"><span class="menu-text">Budget</span></a></li>
          <li class="menu-item"><a href="{% url 'entertainment' %}"><img src="{% static 'images/Entertainment.png' %}" class="menu-icon"><span class="menu-text">Entertainment</span></a></li>
        </ul>
      </nav>
      <div class="profile-section" id="profileMenu">
        <div class="user-icon" id="userIcon">
      </div>
        <span class="menu-text" id="name">{{ user.username }}</span>
        <div class="dropdown" id="profileDropdown">
            <ul>
                <li><a href="{% url 'profile' %}">View Profile</a></li>
                <li><a href="{% url 'logout_view' %}">Sign Out</a></li>
            </ul>
        </div>
      </div>

    </aside>

    <div id='calendar'></div>

   <!-- Event Form -->
   <div class="overlay" id="overlay"></div>
   <div class="event-form" id="eventForm">
    <form method="post" action="{% url 'calendar' %}" id="addevent">
        {% csrf_token %}
       <h3>New Event</h3>
       <p id="eventTimeDisplay" style="font-weight:lighter; margin-top:10px;color:#979595"></p>
       <input type="text" id="eventTitle" name="eventTitle" placeholder="Event Title" required>
         <!-- Hidden field to hold the clicked date -->
    <input type="hidden" id="clickedDate" name="clickedDate" value="">
       <button id="saveEvent" type="submit">Save</button>
       <button id="cancelEvent" type="button">Cancel</button>
    </form>
   </div>

<!-- Edit Event Modal -->
<div id="editModal" class="modal">
  <div class="modal-content">
      <span class="close" onclick="closeEditModal()">&times;</span>
      <h2>Edit</h2>
      <form id="editForm" method="post">
          <label for="projectType">Type</label>
          <select id="projectType" name="projectType" >
              <option value="Corporate">Corporate</option>
              <option value="Completed">Wedding</option>

          </select>
          <label for="status">Status</label>
          <div id="statusButtons">
              <input type="checkbox" class="btn-check" id="completedBtn" autocomplete="off" onclick="toggleStatus(this)">
              <label class="btn btn-primary" for="completedBtn">Completed</label>

              <input type="checkbox" class="btn-check" id="ongoingBtn" autocomplete="off" onclick="toggleStatus(this)">
              <label class="btn btn-primary" for="ongoingBtn">Ongoing</label>

              <input type="checkbox" class="btn-check" id="pendingBtn" autocomplete="off" onclick="toggleStatus(this)">
              <label class="btn btn-primary" for="pendingBtn">Pending</label>

              <input type="checkbox" class="btn-check" id="cancelledBtn" autocomplete="off" onclick="toggleStatus(this)">
              <label class="btn btn-primary" for="cancelledBtn">Cancelled</label>
          </div>

          <label for="projectName">Project Name</label>
          <input type="text" id="projectName" name="projectName" >

          <label for="customerCompany">Customer Company</label>
          <input type="text" id="customerCompany" name="customerCompany" >

          <label for="shootStart">Shoot Start</label>
          <input type="datetime-local" id="shootStart" name="shootStart" >

          <label for="shootEnd">Shoot End</label>
          <input type="datetime-local" id="shootEnd" name="shootEnd" >

          <label for="projectAmount">Project Amount</label>
          <input type="number" id="projectAmount" name="projectAmount" value="0">

          <label for="projectLocation">Project Location</label>
          <input type="text" id="projectLocation" name="projectLocation" >

          <!--sandhiya-->
          <div class="form-group">
            <label for="locationLink">Location Link</label>
            <input type="text" id="locationLink" name="locationLink" placeholder="Enter Google Maps link">
            <button type="button" onclick="getAddressFromLink()" style="margin-top:8px">Get Address</button>
        </div>

        <div class="form-group">
            <label for="address">Extracted Address</label>
            <input type="text" id="address" name="address" readonly>
        </div>

           <label for="outsourcing" style="display: flex; align-items: center;">
                Outsourcing
                <label class="switch" style="margin-left: 300px;">
                    <input type="checkbox" id="outsourcing" name="outsourcing" onchange="toggleOutsourcingDetails()">
                    <span class="slider"></span>
                </label>
            </label>

            <div id="outsourcingDetails" style="display: none; margin-top: 10px;">
                <label for="outsourcingFor">Outsourcing For</label>
                <select id="outsourcingFor" name="outsourcingFor">
                    <option >---</option>

                    <option >Photo</option>
                    <option >Video</option>
                    <option >Editor</option>
                    <option >Drone</option>
                    <option >Pilot</option>
                </select>

                <label for="outsourcingAmount" style="margin-top: 10px;white-space:nowrap;">Outsourcing Amount</label>
                <input type="number" id="outsourcingAmount" name="outsourcingAmount">

                <label for="outsourcingCustomer" style="margin-top: 10px;">Outsourcing Customer Name</label>
                <select id="outsourcingCustomer" name="outsourcingCustomer">
                    <option value="">---</option>
                    <!-- dynamic clients display-->
                </select>

                <label for="outsourcingPaid" style="display: flex; align-items: center; margin-top: 10px;white-space:nowrap;">
                    Outsourcing Paid
                    <label class="switch" style="margin-left: 260px;">
                        <input type="checkbox" id="outsourcingPaid" name="outsourcingPaid">
                        <span class="slider"></span>
                    </label>
                </label>
            </div>

          <label for="reference">Reference</label>
          <input type="text" id="reference" name="reference">

          <div class="form-actions">
              <button class="submit" type="submit">Submit</button>
              <button class="button" type="button" id="cancelBtn" onclick="closeEditModal()">Cancel</button>
          </div>
      </form>
  </div>
</div>



<script>
  function toggleOutsourcingDetails() {
      const outsourcingDetails = document.getElementById("outsourcingDetails");
      const toggle = document.getElementById("outsourcing");
      outsourcingDetails.style.display = toggle.checked ? "block" : "none";
  }
</script>


  <script>
    const sidebar = document.querySelector('.sidebar');
    const toggleIcon = document.getElementById('toggle-icon');

    toggleIcon.addEventListener('click', function() {
      if (sidebar.classList.contains('closed')) {
        sidebar.classList.remove('closed');
        toggleIcon.src = "{% static 'images/menuleft.png' %}";  // Change to the "left-chevron" when open
      } else {
        sidebar.classList.add('closed');
        toggleIcon.src = "{% static 'images/menuright.png' %}";  // Change to the "chevron" when closed
      }
    });

</script>


<script>
    document.addEventListener('DOMContentLoaded', function() {
        var calendarEl = document.getElementById('calendar');

        var calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,timeGridDay'
            },
            events: function(fetchInfo, successCallback, failureCallback) {
                fetch('/calendar/events/')  // Make sure this URL is correct
                .then(response => {
                    // Check if the response is OK (status code 200-299)
                    if (!response.ok) {
                        return response.text().then(text => {
                            throw new Error(`HTTP error! status: ${response.status}, response: ${text}`);
                        });
                    }
                    return response.json();
                })
                    .then(data => {
                        // Transform data to match FullCalendar's expected format
                        const events = data.map(event => ({
                            id: event.id,
                            title: event.name,  // Map name to title
                            start: event.shoot_start_date,  // Use shoot_start_date for start
                            end: event.shoot_end_date,
                        }));
                        console.log('Transformed events:', events);  // Debugging: check the transformed data
                        successCallback(events);  // Pass the transformed events to FullCalendar
                    })
                    .catch(error => {
                        console.error('Error fetching events:', error);
                        failureCallback(error);  // Handle any errors
                    });
            },
            displayEventTime: true,  // Show the time of the events if applicable
            dateClick: function(info) {
                console.log('Date clicked:', info.dateStr);
                showEventForm(info.dateStr, info.jsEvent); // Call your existing function
            },
            eventClick: function(info) {
                openEditEventModal(info.event); // Call your existing function
            }
        });

        calendar.render();  // Render the calendar

        // Add the previously defined functions here
        function showEventForm(dateStr, jsEvent) {
            console.log("Clicked DateStr:", dateStr);
            var form = document.getElementById('eventForm');
            var eventTimeDisplay = document.getElementById('eventTimeDisplay');
            var eventTitle = document.getElementById('eventTitle');

            var clickedDateTime = new Date(dateStr);
            console.log("Clicked Date Object:", clickedDateTime); // Log the Date object

            // Set the clicked date as a form attribute
            form.setAttribute('data-clicked-date', clickedDateTime.toISOString());
            console.log("Set Clicked Date ISO String on Form:", clickedDateTime.toISOString()); // Check if ISO date is set

            // Set the clicked date in the hidden input field for form submission
            document.getElementById('clickedDate').value = clickedDateTime.toISOString();
            console.log("Hidden clickedDate value set:", document.getElementById('clickedDate').value); // Check the hidden input field value

            // If the view is 'dayGridMonth' (Month view), set the time to 12:00 AM
            if (calendar.view.type === 'dayGridMonth') {
                clickedDateTime.setHours(0, 0, 0, 0); // Set to 12:00 AM
            }

            // Format the date properly for display
            var formattedDate = clickedDateTime.toLocaleString('en-US', {
                month: 'long', day: 'numeric', year: 'numeric', hour: 'numeric', minute: 'numeric', hour12: true
            });

            eventTimeDisplay.innerHTML = formattedDate;
            form.style.display = 'block';
            eventTitle.value = '';

            // Position the form near the clicked location
            if (jsEvent) {
                form.style.top = (jsEvent.clientY + window.scrollY) + 'px';
                form.style.left = (jsEvent.clientX + window.scrollX) + 'px';
            }

            // Set the clicked date for later usage when saving the event
            form.setAttribute('data-clicked-date', clickedDateTime.toISOString());
            console.log("Set Clicked Date ISO String on Form:", clickedDateTime.toISOString()); // Check if ISO date is set
        }

        function addEvent() {
            var form = document.getElementById('addevent');
            var title = form.querySelector('#eventTitle').value;
            var clickedDateTime = form.getAttribute('data-clicked-date');

            console.log('Attempting to add event...');

            if (title && clickedDateTime) {
                var newEvent = {
                    title: title,
                    start: clickedDateTime,
                    allDay: true
                };

                console.log('New event details:', newEvent);

                calendar.addEvent(newEvent);
                form.style.display = 'none';
                form.reset();
                console.log('New event created:', newEvent);
            }
        }

        function openEditEventModal(event) {
            var editModal = document.getElementById('editModal');
            var editForm = document.getElementById('editForm');

            document.getElementById('projectType').value = event.extendedProps.projectType || '';
            document.getElementById('projectName').value = event.title;
            document.getElementById('customerCompany').value = event.extendedProps.customerCompany || '';
            document.getElementById('shootStart').value = new Date(event.start).toISOString().substring(0, 16);
            document.getElementById('shootEnd').value = new Date(event.end).toISOString().substring(0, 16);
            document.getElementById('projectAmount').value = event.extendedProps.projectAmount || '';
            document.getElementById('projectLocation').value = event.extendedProps.projectLocation || '';
            document.getElementById('outsourcing').checked = event.extendedProps.outsourcing || false;
            document.getElementById('reference').value = event.extendedProps.reference || '';

            editModal.style.display = 'block';
            editForm.setAttribute('data-event-id', event.id);
        }

        function updateEvent() {
            var editForm = document.getElementById('editForm');
            var eventId = editForm.getAttribute('data-event-id');
            var event = calendar.getEventById(eventId);

            if (event) {
                event.setProp('title', document.getElementById('projectName').value);
                event.setExtendedProp('projectType', document.getElementById('projectType').value);
                event.setExtendedProp('customerCompany', document.getElementById('customerCompany').value);
                event.setStart(document.getElementById('shootStart').value);
                event.setEnd(document.getElementById('shootEnd').value);
                event.setExtendedProp('projectAmount', document.getElementById('projectAmount').value);
                event.setExtendedProp('projectLocation', document.getElementById('projectLocation').value);
                event.setExtendedProp('outsourcing', document.getElementById('outsourcing').checked);
                event.setExtendedProp('reference', document.getElementById('reference').value);
            }

            closeEditModal();
        }

        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
        }

        // Save Event button functionality
        document.getElementById('saveEvent').addEventListener('click', function() {
            addEvent();
        });

        // Submit event functionality
        document.getElementById('editForm').addEventListener('submit', function(event) {
            var eventId = this.getAttribute('data-event-id'); // 'this' refers to the form element

    console.log("Event ID:", eventId);
            updateEvent();
        });

        // Cancel button functionality for Add Event form
        document.getElementById('cancelEvent').addEventListener('click', function() {
            document.getElementById('eventForm').style.display = 'none';
        });

        // Close button (&times;) and Cancel button functionality for Edit form
        document.querySelector('.close').addEventListener('click', closeEditModal);
        document.getElementById('cancelBtn').addEventListener('click', closeEditModal);



// Add form ///////////////////////////////////
document.getElementById('addevent').addEventListener('submit', function(event) {
    event.preventDefault(); // Prevent the default form submission

    var form = this;
    var clickedDateTime = document.getElementById('clickedDate').value; // Retrieve from hidden field
    console.log("Retrieved Clicked Date from Hidden Field:", clickedDateTime); // Debugging: Log the clicked date

    // Proceed with fetch if the date is valid
    if (clickedDateTime) {
        // Create a JSON object from the form data
        var jsonData = {
            clickedDate: clickedDateTime,
            // Include other form fields as needed
        };

        // Iterate through the form elements to add to the jsonData
        Array.from(form.elements).forEach(element => {
            if (element.name) {
                jsonData[element.name] = element.value;
            }
        });

        // Debugging: Log the entire jsonData object
        console.log("Prepared JSON Data to Send:", JSON.stringify(jsonData, null, 2)); // Pretty print JSON for readability

        // Check if the required fields are present
        if (!jsonData.eventTitle || !jsonData.clickedDate) { // Replace with your actual required field names
            console.error('Error: Title or time is missing in the data.');
            return; // Stop execution if required fields are missing
        }

        fetch(form.action, {
            method: form.method,
            body: JSON.stringify(jsonData), // Convert to JSON string
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok: ' + response.statusText);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                console.log("Event successfully added:", data); // Debugging: Log success message
                form.reset();
                location.reload(); // Reload to show the new event
            } else {
                console.error('Server Error:', data.error); // Log server error
            }
        })
        .catch(error => {
            console.error('Fetch Error:', error); // Log fetch error
        });
    } else {
        console.error('Error: Clicked date not found.');
    }
});

    });

</script>

<script>
    $(document).ready(function() {
        var modal = $('#editModal');
        var span = $('.close');
        var cancelBtn = $('#cancelBtn');

        // CSRF setup
        $.ajaxSetup({
            beforeSend: function(xhr, settings) {
                if (!this.crossDomain) {
                    xhr.setRequestHeader("X-CSRFToken", getCookie('csrftoken'));
                }
            }
        });

        function getCookie(name) {
            var cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                var cookies = document.cookie.split(';');
                for (var i = 0; i < cookies.length; i++) {
                    var cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // Close the modal when clicking on <span> (x) or cancel button
        span.on('click', function() {
            modal.hide();
        });

        cancelBtn.on('click', function() {
            modal.hide();
        });

        // Function to open the modal and populate it with data
        function openModal(code) {

            function convertToDateTimeLocalDMY(dateStr) {
                console.log("edit form function");
                const date = new Date(dateStr);
                const pad = (num) => (num < 10 ? '0' + num : num);

                // Ensure correct format for 'dd-mm-yyyyTHH:MM'
                const localDateTime = pad(date.getDate()) + '-' +  // Day first
                                      pad(date.getMonth() + 1) + '-' +  // Month second
                                      date.getFullYear() + ' ' +  // Year third
                                      pad(date.getHours()) + ':' +  // Hours
                                      pad(date.getMinutes());  // Minutes

                return localDateTime;
            }

            function setStatusForEditForm(status) {
                console.log("Setting status for edit form:", status);
                var buttons = document.querySelectorAll('#statusButtons .btn-check');

                buttons.forEach(function(btn) {
                    if (btn.value === status) {
                        btn.checked = true;  // Check the correct checkbox
                        var label = document.querySelector('label[for="' + btn.id + '"]');
                        label.classList.add('active');  // Highlight this button
                        console.log("Checking button for status:", btn.value);
                    } else {
                        btn.checked = false;  // Uncheck other buttons
                        var label = document.querySelector('label[for="' + btn.id + '"]');
                        label.classList.remove('active');  // Remove highlight from others
                        console.log("Unchecking button:", btn.value);
                    }
                });
            }

            if (code) {
                $.ajax({
                    url: '/get_model_data/' + code + '/',
                    method: 'GET',
                    success: function(data) {


                        $('#editForm').attr('data-event-id', code); // Set the edit code
                        $('#projectType').val(data.projectType).change();
                        $('#projectName').val(data.projectName);
                        $('#customerCompany').val(data.customerCompany);
                        $('#shootStart').val(convertToDateTimeLocalDMY(data.shootStart));
                        $('#shootEnd').val(convertToDateTimeLocalDMY(data.shootEnd));
                        $('#projectAmount').val(data.projectAmount);
                        $('#projectLocation').val(data.projectLocation);
                        $('#address').val(data.address);
                        $('#outsourcing').prop('checked', data.outsourcing);

                        if (data.outsourcing) {
                            $('#outsourcingDetails').show(); // Show the outsourcing details if the checkbox is checked
                        } else {
                            $('#outsourcingDetails').hide(); // Hide the outsourcing details if not checked
                        }

                        $('#reference').val(data.reference);
                        $('#outsourcingFor').val(data.outfor).change();
                        $('#outsourcingAmount').val(data.outamt);
                        $('#outsourcingCustomer').val(data.outcus).change();
                        $('#outsourcingPaid').prop('checked', data.outpaid);

                        console.log("just before the function...")
                        setStatusForEditForm(data.projectStatus);

                        modal.show();
                    },
                    error: function() {
                        alert('Failed to fetch data. Please try again.');
                    }
                });
            } else {
                $('#editForm').removeAttr('data-event-id'); // Clear the edit code for new projects
                modal.show();
            }
        }
        // Attach click event to edit buttons
        $('.edit-btn').on('click', function(event) {
            event.stopPropagation();
            event.preventDefault(); // Prevent default link behavior
            var code = $(this).data('code'); // Get the project code from the button
            openModal(code);
        });

        $('#editForm').on('submit', function(event) {
            event.preventDefault();

            // Convert the date from dd-mm-yyyy to yyyy-mm-dd
            var dateField = $('#shootStart');  // Select the date field
            var dateValue = dateField.val();
            var convertedDate = convertToBackendDateTime(dateValue);
            dateField.val(convertedDate);  // Set the new converted date value back to the field


            // Convert the date from dd-mm-yyyy to yyyy-mm-dd
            var dateField = $('#shootEnd');  // Select the date field
            var dateValue = dateField.val();
            var convertedDate = convertToBackendDateTime(dateValue);
            dateField.val(convertedDate);  // Set the new converted date value back to the field

            var code = $('#editForm').attr('data-event-id'); // Get the edit code
            var url = '/edit_model/' + (code ? code + '/' : ''); // Ensure URL includes code if available

            var formData = $(this).serializeArray(); // Serialize the form data
            var statusValue; // Variable to hold the selected status

    // Find the checked checkbox for the status
    $('#statusButtons .btn-check:checked').each(function() {
        statusValue = $(this).val(); // Get the value of the checked checkbox
    });

    if (statusValue) {
        // Add the selected status to the form data
        formData.push({ name: 'projectStatus', value: statusValue });
    }


            $.ajax({
                url: url,
                method: 'POST',
                data: formData,


                success: function(response) {
                    if (response.success) {
                        alert('Form submitted successfully!');
                        modal.hide();
                        location.reload();  // Reload the page to reflect changes
                    } else {
                        alert('Failed to submit form: ' + response.error);
                    }
                },
                error: function() {
                    alert('An error occurred. Please try again.');
                }
            });
        });
    });


</script>

<script>
    function convertToBackendDateTime(dateTimeStr) {
        console.log("Input dateTimeStr:", dateTimeStr); // Log the input value

        // Split the datetime string into date and time parts using 'T' as the delimiter
        const [datePart, timePart] = dateTimeStr.split(' ');  // Correctly splitting on 'T'

        console.log("After splitting - Date part:", datePart, "Time part:", timePart); // Log the split parts

        const parts = datePart.split('-');  // Split the date part (dd-mm-yyyy)
        console.log("Split date parts - Day:", parts[0], "Month:", parts[1], "Year:", parts[2]); // Log the split date parts

        // Rearrange to yyyy-mm-dd and concatenate with the time part
        const backendDateTime = `${parts[2]}-${parts[1]}-${parts[0]} ${timePart}`;

        console.log("Converted backend datetime format:", backendDateTime); // Log the final converted value

        return backendDateTime;
    }
</script>

  <script> //front end
    function toggleStatus(checkbox) {
        var buttons = document.querySelectorAll('#statusButtons .btn-check');

        if (checkbox.checked) {
            buttons.forEach(function(btn) {
                if (btn !== checkbox) {
                    btn.checked = false;
                }
            });
        }

        buttons.forEach(function(btn) {
            var label = document.querySelector('label[for="' + btn.id + '"]');
            if (btn.checked) {
                label.classList.add('active');
            } else {
                label.classList.remove('active');
            }
        });
    }

    function closeEditModal() {
        document.getElementById('editModal').style.display = 'none';
    }
  </script>



  <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

      <script> // front end
          config={
              enableTime:true,

          }
          flatpickr("input[type=datetime-local]",config);
      </script>

      <!-- Places Autocomplete -->

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script>
    $.getScript("https://maps.googleapis.com/maps/api/js?key=AIzaSyAqIalCM0rqsXeHiyRP4ImBbVgqwMSv8D8&libraries=places")
    .done(function(script, textStatus) {
        google.maps.event.addDomListener(window, "load", initAutoComplete);
    });

    let autocomplete;

    function initAutoComplete() {
        autocomplete = new google.maps.places.Autocomplete(
            document.getElementById('projectLocation'),
            {
                // No 'types' filter for broader searches (places, establishments, addresses, etc.)
                componentRestrictions: {'country': 'in'} // Restrict to India (or change country if needed)
            }
        );

        autocomplete.addListener('place_changed', onPlaceChanged);
    }

    function onPlaceChanged() {
        var place = autocomplete.getPlace();

        if (!place.geometry) {
            document.getElementById('projectLocation').placeholder = "*Begin typing address or place name";
            return;
        }

        // Retrieve latitude and longitude
        var latitude = place.geometry.location.lat();
        var longitude = place.geometry.location.lng();

        // Populate hidden fields with latitude and longitude
        $('#latitude').val(latitude);
        $('#longitude').val(longitude);

        // Optionally, retrieve more address components as before
        var num = '', route = '', town = '', county = '', country = '', postalCode = '';
        for (var i = 0; i < place.address_components.length; i++) {
            for (var j = 0; j < place.address_components[i].types.length; j++) {
                if (place.address_components[i].types[j] === "street_number") {
                    num = place.address_components[i].long_name;
                }
                if (place.address_components[i].types[j] === "route") {
                    route = place.address_components[i].long_name;
                }
                if (place.address_components[i].types[j] === "locality") {
                    town = place.address_components[i].long_name;
                }
                if (place.address_components[i].types[j] === "administrative_area_level_2") {
                    county = place.address_components[i].long_name;
                }
                if (place.address_components[i].types[j] === "country") {
                    country = place.address_components[i].long_name;
                }
                if (place.address_components[i].types[j] === "postal_code") {
                    postalCode = place.address_components[i].long_name;
                }
            }
        }

        console.log(`Latitude: ${latitude}, Longitude: ${longitude}`);
        console.log(`Address: ${num} ${route}, Town: ${town}, Country: ${country}`);
    }
</script>

<!--Location Link-->
<script>
    async function resolveShortUrl(shortUrl) {
        try {
            // Call the Django view to resolve the short URL
            const response = await fetch(`/resolve-url?url=${encodeURIComponent(shortUrl)}`);
            const data = await response.json();

            if (data.resolved_url) {
                return data.resolved_url; // Return the resolved URL
            } else {
                console.error('Error resolving URL:', data.error);
                return null;
            }
        } catch (error) {
            console.error('Error resolving short URL:', error);
            return null;
        }
    }

    function extractPlaceNameAndCoordinates(link) {
        // Updated regex to correctly extract place name and latitude/longitude
        const regex = /\/maps\/place\/([^\/]+)\/@(-?\d+\.\d+),(-?\d+\.\d+)/;
        const match = link.match(regex);
        if (match) {
            const placeName = decodeURIComponent(match[1]).replace(/\+/g, ' '); // Decode and replace + with spaces
            const lat = parseFloat(match[2]);
            const lng = parseFloat(match[3]);
            return { placeName, coordinates: { lat, lng } };
        }
        return null;
    }

    async function getAddressFromLink() {
        const locationLink = document.getElementById("locationLink").value;
        const addressField = document.getElementById("address");

        try {
            // Resolve the URL if it is a short URL
            const resolvedUrl = locationLink.includes('goo.gl') ? await resolveShortUrl(locationLink) : locationLink;

            if (resolvedUrl) {
                // Extract the place name and coordinates from the resolved link
                const placeInfo = extractPlaceNameAndCoordinates(resolvedUrl);
                console.log("ex co: ",placeInfo);
                if (placeInfo) {
                    // Call initMap with the coordinates
                    //initMap(placeInfo.coordinates);
                    addressField.value = 'Fetching address...'; // Optional: Indicate address fetching
                    const address = await geocodeLatLng(placeInfo.coordinates);
                    addressField.value = address; // Update the address field with the fetched address
                } else {
                    addressField.value = 'Invalid link or no coordinates found';
                }
            } else {
                addressField.value = 'Failed to resolve URL';
            }
        } catch (error) {
            console.error('Error fetching address:', error);
            addressField.value = 'Error fetching address: ' + error.message;
        }
    }

    function geocodeLatLng(latlng) {
        return new Promise((resolve, reject) => {
            const geocoder = new google.maps.Geocoder();
            const { lat, lng } = latlng;

            const latlngObj = {
                lat: parseFloat(latlng.lat),
                lng: parseFloat(latlng.lng)
            };
            geocoder.geocode({ location: latlngObj }, (results, status) => {
                if (status === 'OK') {
                    if (results[0]) {
                        resolve(results[0].formatted_address); // Resolve with formatted address
                    } else {
                        reject('No results found');
                    }
                } else {
                    reject('Geocoder failed due to: ' + status);
                }
            });
        });
    }

</script>
<script>
    // user icon
    const username = document.getElementById('name').textContent;
    document.querySelector('#userIcon').innerText = username.charAt(0);
</script>
<script>
    // JavaScript to handle dropdown visibility
    const profileMenu = document.getElementById('profileMenu');
    const profileDropdown = document.getElementById('profileDropdown');

    profileMenu.addEventListener('click', function () {
        // Toggle dropdown visibility
        if (profileDropdown.style.display === 'none' || profileDropdown.style.display === '') {
            profileDropdown.style.display = 'block';
        } else {
            profileDropdown.style.display = 'none';
        }
    });

    // Close dropdown if clicked outside
    window.addEventListener('click', function (event) {
        if (!profileMenu.contains(event.target)) {
            profileDropdown.style.display = 'none';
        }
    });
</script>
</body>
</html>
