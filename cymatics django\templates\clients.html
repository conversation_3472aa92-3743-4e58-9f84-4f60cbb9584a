{% load static %}
{% static "images" as baseurl %}
<!doctype html>
<html>
    <head>
        <meta charset="UTF-8">
	    <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
	    <title>Clients Page</title>
        <link rel="stylesheet" href="{% static './css/clients.css' %}">
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    </head>
    <style>

        .button-container {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 10px;
            margin-bottom: 20px;
              }
        .search-bar {
            display: flex;
            align-items: center;
            position: relative;
            width: 200px;
            margin-right:50px;
        }

        .search-bar i {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #ccc;
            pointer-events: none;
        }

        .search-bar input {
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            padding-left: 30px;
            background-color: #ffffff;
            width: 100%;
        }
        .button-container button:hover {
            background-color: #717070;
        }

        .new-btn {
            background-color: #333;
            color: white;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
            border-radius: 5px;
            display: flex;
            align-items: center;
            font-weight: 500;
            font-size: 15px;
            gap: 2px;
        }

        .outclients {
            background-color: #333;
            color: white;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
            border-radius: 5px;
            display: flex;
            align-items: center;
            font-weight: 500;
            font-size: 15px;
        }


 .button-icon {
     margin-right: 5px;
     width: 16px;
     height: 16px;
 }

 .options-icon {
     width: 24px;
     height: 24px;
     cursor: pointer;
     position: absolute;
     right: 15px;
     top: 50%;
     transform: translateY(-50%);
 }
         /* Modal Styles */
 .modal {
     display: none;
     position: fixed;
     z-index: 1;
     left: 0;
     top: 0;
     width: 100%;
     height: 100%;
     overflow: auto;
     background-color: rgba(0,0,0,0.4);
 }

 .modal-content {
     background-color: #fefefe;
     margin: 15% auto;
     padding: 20px;
     border: 1px solid #888;
     width: 400px;
     border-radius: 10px;
 }
 .modal-content h3 {
  text-align: center;
  font-size: 20px; /* Adjust as needed */
  margin-bottom: 20px;
 }
 .close {
     color: #aaa;
     float: right;
     font-size: 28px;
     font-weight: bold;
 }

 .close:hover,
 .close:focus {
     color: black;
     text-decoration: none;
     cursor: pointer;
 }

 .modal-content form {
     display: flex;
     flex-direction: column;
 }

 .modal-content form label {
     margin-top: 10px;
 }

 .modal-content form input,
 .modal-content form button {
     padding: 10px;
     margin-top: 5px;
     border-radius: 5px;
     border: 1px solid #ccc;
 }

 .form-actions {
     display: flex;
     justify-content: flex-end;
     margin-top: 20px;
 }

 .form-actions button {
     margin-left: 10px;
 }

 form button[type="submit"],
   form button[type="button"] {
     flex: 1;
  padding: 10px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  margin: 0 5px;
   }

 form button[type="submit"] {
     background-color: black;
     color: white;
 }

 form button[type="button"] {
    background-color: #ffffff;
    color: rgb(0, 0, 0);
    border: 1px solid #888;
 }


    .profile-section {
     position: relative;
     padding: 12px 20px;
     cursor: pointer;
     transition: background-color 0.3s, border-left 0.3s;
    }
    .profile-section:hover {
     background-color: #555;
     border-left: 4px solid #ffcc00;
    }
    .dropdown {
     position: absolute;
     bottom: 100%;
     left: 0;
     background-color: white;
     border: 1px solid #ccc;
     border-radius: 4px;
     z-index: 1000;
     width: 160px;
     box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
     display: none;
    }
    .dropdown li {
     padding: 10px;
     color: black;
     cursor: pointer;
    }
    .dropdown li:hover {
     background-color: #f1f1f1;
    }
    .user-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background-color: #ddd;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        font-size: 18px;
        color: #0e0e0e;
        background-color: #e1ecb8;

    }



     </style>
    <body>

        <div class="container">
            <aside class="sidebar">
                <div class="toggle-icon">
                    <img src="{% static 'images/menuleft.png' %}" alt="icon" id="toggle-icon" width="16" height="16">
                </div>
                <div class="logo">
                    <img src="{% static 'images/logowhite.png' %}" alt="logo" width="50" height="50">
                </div>
                <nav>
                    <ul>
                        <li class="menu-item"><a href="{% url 'dashboard' %}"><img src="{% static 'images/dashboard.png' %}" class="menu-icon"><span class="menu-text">Dashboard</span></a></li>
                        <li class="menu-item"><a href="{% url 'projects' %}"><img src="{% static 'images/project.png' %}" class="menu-icon"><span class="menu-text">Project</span></a></li>
                        <li class="menu-item"><a href="{% url 'incomef_view' %}"><img src="{% static 'images/income.png' %}" class="menu-icon"><span class="menu-text">Income</span></a></li>
                        <li class="menu-item"><a href="{% url 'expense' %}"><img src="{% static 'images/expenses.png' %}" class="menu-icon"><span class="menu-text">Expense</span></a></li>
                        <li class="menu-item"><a href="{% url 'calendar' %}"><img src="{% static 'images/calendar.png' %}" class="menu-icon"><span class="menu-text">Calendar</span></a></li>
                        <li class="menu-item"><a href="{% url 'allproject' %}"><img src="{% static 'images/All projects.png' %}" class="menu-icon"><span class="menu-text">All Projects</span></a></li>
                        <li class="menu-item"><a href="{% url 'clientsbook' %}"><img src="{% static 'images/clientsbook.png' %}" class="menu-icon"><span class="menu-text">Clients Book</span></a></li>
                        <li class="menu-item active"><a href="{% url 'clients' %}"><img src="{% static 'images/Clients.png' %}" class="menu-icon"><span class="menu-text">Clients</span></a></li>
                        <li class="menu-item"><a href="{% url 'status' %}"><img src="{% static 'images/status.png' %}" class="menu-icon"><span class="menu-text">Status</span></a></li>
                        <li class="menu-item"><a href="{% url 'pending_pay' %}"><img src="{% static 'images/pending.png' %}" class="menu-icon"><span class="menu-text">Pending Payments</span></a></li>
                        <li class="menu-item"><a href="{% url 'project_map' %}"><img src="{% static 'images/map.png' %}" class="menu-icon"><span class="menu-text">Map</span></a></li>
                        <li class="menu-item"><a href="{% url 'assets' %}"><img src="{% static 'images/Assets.png' %}" class="menu-icon"><span class="menu-text">Assets</span></a></li>
                        <li class="menu-item"><a href="{% url 'budget' %}"><img src="{% static 'images/budget.png' %}" class="menu-icon"><span class="menu-text">Budget</span></a></li>
                        <li class="menu-item"><a href="{% url 'entertainment' %}"><img src="{% static 'images/Entertainment.png' %}" class="menu-icon"><span class="menu-text">Entertainment</span></a></li>
                    </ul>
                </nav>
                <div class="profile-section" id="profileMenu">
                    <div class="user-icon" id="userIcon">
                      <!-- Default content in case JS is not available -->
                      U
                  </div>

                    <span class="menu-text" id="name">{{ user.username }}</span>
                    <div class="dropdown" id="profileDropdown">
                        <ul>
                            <li><a href="{% url 'profile' %}">View Profile</a></li>
                            <li><a href="{% url 'logout_view' %}">Sign Out</a></li>
                        </ul>
                    </div>
                  </div>
                </aside>
             <main class="main-content">
                <div class="button-container">
                <div class="search-bar">

    <!-- search box -->
    <form method="get" action="{% url 'clients' %}">
        {% csrf_token %}

        <input type="text" name="q" placeholder="Search" value="{{ query }}">
            <i class="fa-solid fa-magnifying-glass"></i>
        </div>
    </form>
    <button class="new-btn" onclick="openNewClientForm()">
        <i class="fa-solid fa-plus add"></i> <b>New</b></button>
            <button class="outclients" onclick="moveToOutClients()" >Outsourcing Clients</button>

        </div>

            <table class="clients-table">
                <tr>
                    <th>Client</th>
                    <th>Company</th>
                    <th>Phone Number</th>

                    <th>Actions</th>
                </tr>
                {% for item in objs %}

                <tr  onclick="moveToDpage('{{ item.name }}')" style="cursor: pointer;">
                    <td>{{ item.name }}</td>
                    <td>{{ item.company }}</td>
                    <td>{{ item.number }}</td>

                    <td>
                        <img src="{% static 'images/option.png' %}" alt="Options" class="options-icon" data-id="{{ item.id }}" onclick="openEditClientForm()">
                        <div class="client-actions">
                            <button class="call-btn"><img src="{% static 'images/call.png' %}" alt="Call Icon" class="button-icon">Call</button>
                            <button class="share-btn"><img src="{% static 'images/send.png' %}" alt="Share Icon" class="button-icon">Share</button>
                        </div>
                    </td>

                </tr>
                {% endfor %}

            </table>
        </main>
    </div>

    <!-- Modal for New Client Form -->
    <div id="newClientModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeNewClientForm()">&times;</span>
            <h3>New Client</h3>
            <form id="newClientForm" method="post" action="{% url 'clients' %}">
                {% csrf_token %}
                <label for="newName">Name</label>
                <input type="text" id="newName" name="newName" required>

                <label for="newCompany">Company</label>
                <input type="text" id="newCompany" name="newCompany" required>

                <label for="newEmail">Email</label>
                <input type="email" id="newEmail" name="newEmail" required>

                <label for="newNumber">Phone Number</label>
                <input type="text" id="newNumber" name="newNumber">

                <label for="newImage">Image</label>
                <input type="file" id="newImage" name="newImage">

                <div class="form-actions">
                    <button type="submit" id="addBtn">Add Client</button>
                    <button type="button" onclick="closeNewClientForm()">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal for Edit Client Form -->
    <div id="editClientModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeEditClientForm()">&times;</span>
            <h3>Edit Client</h3>
            <form id="editClientForm">
                <label for="editName">Name</label>
                <input type="text" id="editName" name="editName" required>

                <label for="editCompany">Company</label>
                <input type="text" id="editCompany" name="editCompany" required>

                <label for="editEmail">Email</label>
                <input type="email" id="editEmail" name="editEmail" required>

                <label for="editNumber">Phone Number</label>
                <input type="text" id="editNumber" name="editNumber">

                <label for="editImage">Image</label>
                <input type="file" id="editImage" name="editImage">
                <P id="image-file-name"></p>



                <div class="form-actions">
                    <button type="submit">Submit</button>
                    <button type="button" id ="cancelBtn" onclick="closeEditClientForm()">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function openNewClientForm() {
            document.getElementById('newClientModal').style.display = 'block';
        }

        function closeNewClientForm() {
            document.getElementById('newClientModal').style.display = 'none';
        }

        function openEditClientForm() {
            document.getElementById('editClientModal').style.display = 'block';
        }

        function closeEditClientForm() {
            document.getElementById('editClientModal').style.display = 'none';
        }

        // Close modals when clicking outside
        window.onclick = function(event) {
            if (event.target == document.getElementById('newClientModal')) {
                closeNewClientForm();
            }
            if (event.target == document.getElementById('editClientModal')) {
                closeEditClientForm();
            }
        }
    </script>
    <script>
        // move tot total expense
        function moveToOutClients() {
            window.location.href = "/outclients";
        }
    </script>

    <script>// add form

        document.getElementById('addBtn').addEventListener('submit', function(event) {
            event.preventDefault();

            var form = this;
            var formData = new FormData(form);

            fetch(form.action, {
                method: form.method,
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (response.headers.get('content-type')?.includes('application/json')) {
                    return response.json();
                } else {
                    return response.text().then(text => { throw new Error(text) });
                }
            })
            .then(data => {
                if (data.success) {
                    form.reset();
                    closeNewClientForm();
                    location.reload();
                } else {
                    var errorMessage = document.getElementById("error-message");
                    errorMessage.textContent = data.error;
                    errorMessage.style.display = "block";
                }
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById("error-message").textContent = 'An error occurred: ' + error.message;
                document.getElementById("error-message").style.display = "block";
            });
        });


    </script>


    <script>// edit form

        $(document).ready(function() {
            var modal = $('#editClientModal');
            var span = $('.close');
            var cancelBtn = $('#cancelBtn');

            $.ajaxSetup({
                beforeSend: function(xhr, settings) {
                    if (!this.crossDomain) {
                        xhr.setRequestHeader("X-CSRFToken", getCookie('csrftoken'));
                    }
                }
            });

            function getCookie(name) {
                var cookieValue = null;
                if (document.cookie && document.cookie !== '') {
                    var cookies = document.cookie.split(';');
                    for (var i = 0; i < cookies.length; i++) {
                        var cookie = cookies[i].trim();
                        if (cookie.substring(0, name.length + 1) === (name + '=')) {
                            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                            break;
                        }
                    }
                }
                return cookieValue;
            }

            // Close the modal
            span.on('click', function() {
                modal.hide();
            });

            cancelBtn.on('click', function() {
                modal.hide();
            });


            function openModal(clt_id) {
                if (clt_id) {
                    $.ajax({
                        url: '/get_client_data/' + clt_id + '/',
                        method: 'GET',
                        success: function(data) {
                            $('#editClientForm').attr('data-edit-id', clt_id); // Set the edit code
                            $('#editName').val(data.name);
                            $('#editCompany').val(data.company);                                             ////////////// edittype - id, data.type -> type views getting variable name
                            $('#editEmail').val(data.email);
                            $('#editNumber').val(data.number);  // Corrected to match field name

                            // Display the previously uploaded file name
                            if (data.image) {
                                $('#image-file-name').text("Previously uploaded file: " + data.image.split('/').pop());
                            } else {
                                $('#image-file-name').text("No file uploaded.");
                            }

                            modal.show();
                        },
                        error: function() {
                            alert('Failed to fetch data. Please try again.');
                        }
                    });
                } else {
                    $('#editClientForm').removeAttr('data-edit-id'); // Clear the edit code for new projects
                    modal.show();
                }
            }

            // Attach click event to edit buttons
            $('.options-icon').on('click', function(event) {
                event.preventDefault(); // Prevent default link behavior
                event.stopPropagation();

                var id = $(this).data('id'); // Get the project code from the button
                openModal(id);
            });

            $('#editClientForm').on('submit', function(event) {
                event.preventDefault();
                var id = $('#editClientForm').attr('data-edit-id'); // Get the edit code
                var url = '/edit_client/' + (id ? id + '/' : ''); // Ensure URL includes code if available

                // Create FormData object
                var formData = new FormData(this);

                $.ajax({
                    url: url,
                    method: 'POST',
                    data: formData,
                    processData: false,  // Prevent jQuery from automatically transforming the data into a query string
                    contentType: false,  // Let the browser set the content type automatically (needed for file uploads)

                    success: function(response) {
                        if (response.success) {
                            alert('Form submitted successfully!');
                            modal.hide();
                            location.reload();  // Reload the page to reflect changes
                         } else {
                            alert('Failed to submit form: ' + response.error);
                        }
                    },
                    error: function() {
                        alert('An error occurred. Please try again.');
                    }
                });
            });
            // Show selected file name when a file is chosen
            $('#newImage').on('change', function() {
                var fileName = $(this).val().split('\\').pop();  // Get the file name only
                $('#image-file-name').text("Selected file: " + fileName);
            });
        });

    </script>
    <script>
        function moveToDpage(name){
            window.location.href = "/clientd/" + name + "/";
        }
    </script>
    <script>
        const sidebar = document.querySelector('.sidebar');
        const toggleIcon = document.getElementById('toggle-icon');

        toggleIcon.addEventListener('click', function() {
          if (sidebar.classList.contains('closed')) {
            sidebar.classList.remove('closed');
            toggleIcon.src = "{% static 'images/menuleft.png' %}";  // Change to the "left-chevron" when open
          } else {
            sidebar.classList.add('closed');
            toggleIcon.src = "{% static 'images/menuright.png' %}";  // Change to the "chevron" when closed
          }
        });

    <script>
        // user icon
        const username = document.getElementById('name').textContent;
        document.querySelector('#userIcon').innerText = username.charAt(0);
      </script>
      <script>
        // JavaScript to handle dropdown visibility
        const profileMenu = document.getElementById('profileMenu');
        const profileDropdown = document.getElementById('profileDropdown');

        profileMenu.addEventListener('click', function () {
            // Toggle dropdown visibility
            if (profileDropdown.style.display === 'none' || profileDropdown.style.display === '') {
                profileDropdown.style.display = 'block';
            } else {
                profileDropdown.style.display = 'none';
            }
        });

        // Close dropdown if clicked outside
        window.addEventListener('click', function (event) {
            if (!profileMenu.contains(event.target)) {
                profileDropdown.style.display = 'none';
            }
        });
    </script>
    <script>
        // user icon
        const username = document.getElementById('name').textContent;
        document.querySelector('#userIcon').innerText = username.charAt(0);
      </script>
    </body>
</html>