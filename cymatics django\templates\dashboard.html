{% load static %}
{% static "images" as baseurl %}
<!doctype html>
<html>
    <head>
        <meta charset="UTF-8">
	    <meta name="viewport" content="width=device-width, initial-scale=1.0">
	    <title>Dashboard</title>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

        <style>
            .chart-container {
                width: 25%;
                max-width: 600px;
                margin: auto;
            }
            #expensesList {
                margin-top: 20px;
                text-align: left;
            }
            #expensesDetails {
                list-style-type: none;
                padding: 0;
            }
            #expensesDetails li {
                margin: 5px 0;
            }
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 0;
                display: flex;
                height: 100vh;
                flex-direction: column;
            }
            .container {
                display: flex;
                width: 100%;
                flex-grow: 1;
                justify-content:space-between;
            }
            .sidebar {
                background-color: #1e1e1e;
                color: white;
                width: 250px;
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
                align-items: center;
                transition: width 0.3s;
                position: relative;
            }
            .sidebar.closed {
                width: 60px;
            }
            .sidebar .toggle-icon {
                position: absolute;
                top: 25px !important;
                right: -8px;
                cursor: pointer;
                visibility: hidden;
                border: 3px solid rgba(78, 27, 231, 0.5);
                border-radius: 8px;
                padding: 1px;
                transition: visibility 0.3s ease-in-out, top 0.3s ease-in-out;
                z-index: 2;
            }
            #toggle-icon {
                width: 20px;
                height: 20px;
            }
            .sidebar.closed .toggle-icon {
                top: 10px;
                right: -8px;
            }
            .sidebar:hover .toggle-icon, .toggle-icon:hover {
                visibility: visible;
            }
            .sidebar .logo {
                padding: 20px;
                text-align: center;
            }
            .sidebar.closed .logo {
                display: none;
            }
            .sidebar nav ul {
                list-style: none;
                padding: 0;
                width: 100%;
                text-align: center;
            }
            .sidebar nav ul li {
                padding: 12px 20px;
                cursor: pointer;
                transition: background-color 0.3s, border-left 0.3s;
                display: flex;
                justify-content: flex-start;
                align-items: center;
            }
            .sidebar.closed nav ul li {
                justify-content: center;
            }
            .sidebar nav ul li a {
                display: flex;
                align-items: center;
                text-decoration: none;
                color: white;
                width: 100%;
                font-family: Arial, sans-serif;
            }
            .sidebar nav ul li a:hover {
                background-color: #555;
                border-left: 4px solid #ffcc00;
            }
            .menu-icon {
                margin-right: 10px;
                width: 24px;
                height: 24px;
            }
            .menu-text {
                transition: opacity 0.3s, visibility 0.3s;
                font-family: Arial, sans-serif;
            }
            .sidebar.closed .menu-text {
                display: none;
            }
            .sidebar.closed nav ul li:hover {
                background-color: inherit;
            }
            .main-content {
                flex-grow: 1;
                background-color: #f1f1f1;
                padding: 20px;
                overflow-y: auto;
         }
            .dropdown ul {
                   list-style: none;
                padding: 0;
                margin: 0;
            }
            .profile-section {
                position: relative;
                padding: 12px 20px;
                cursor: pointer;
                transition: background-color 0.3s, border-left 0.3s;
            }
            .profile-section:hover {
                background-color: #555;
                border-left: 4px solid #ffcc00;
            }
            .dropdown {
                position: absolute;
                bottom: 100%;
                left: 0;
                background-color: white;
                border: 1px solid #ccc;
                border-radius: 4px;
                z-index: 1000;
                width: 160px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                display: none;
            }
            .dropdown li {
                padding: 10px;
                color: black;
                cursor: pointer;
            }
            .dropdown li:hover {
                background-color: #f1f1f1;
            }


.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.header .stats {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    margin-bottom: 20px;
}

.header .stat {
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    flex: 1;
    margin-right: 20px;
    margin-bottom: 20px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.header .stat:hover {
    background-color: #e0e0e0;
}

.header .stat:last-child {
    margin-right: 0;
}

.header .stat span {
    display: block;
    font-size: 14px;
    color: #666;
}

.header .stat strong {
    font-size: 24px;
    color: #000;
}

html, body {
    height: 100%;
    margin: 0;
}
.button-container {
    display: flex; /* Ensures the buttons are aligned horizontally */
    justify-content: space-around; /* Spreads buttons evenly */
}

.button {
    background-color: #d3d3d3; /* Mild grey background */
    color: #000; /* Text color */
    width: 200px; /* Adjust width as needed */
    height: 70px; /* Adjust height as needed */
    border: none; /* Remove default border */
    border-radius: 8px; /* Slightly rounded corners */
    display: flex; /* Use flexbox to center the content */
    flex-direction: column; /* Icon and text in column layout */
    justify-content: center; /* Center content vertically */
    align-items: center; /* Center content horizontally */
    font-size: 16px; /* Adjust text size */
    font-weight:Medium; /* Make text bold */
    cursor: pointer; /* Adds pointer cursor on hover */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Subtle shadow */
    transition: background-color 0.3s ease; /* Smooth hover effect */
    row-gap: 3px;
    margin:10px;
    margin-left: 50px;
}

.button:hover {
    background-color: #c0c0c0; /* Slightly darker grey on hover */
}

.button img {
    width: 32px; /* Adjust icon size */
    height: 32px; /* Ensure consistent height */
    margin-bottom: 3px; /* Space between icon and text */
}

.payment-pending {
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    margin-top: 2px;
}

.payment-pending h2 {
    margin-top: 0;
}

.payment-pending table {
    width: 100%;
    border-collapse: collapse;
}

.payment-pending tr {
    cursor: pointer; /* Change cursor to pointer for rows */
}

.payment-pending tr:hover {
    background-color: #f9f9f9; /* Light background on hover */
}

.payment-pending table td {
    padding: 15px;
    border-bottom: 1px solid #ddd;
}

.payment-pending table td:last-child {
    text-align: right;
}

.pagination {
display: flex;
justify-content: center;
align-items: center;
margin: 20px;
}

.arrow {
background-color: #f1f1f1;
border: none;
padding: 10px;
cursor: pointer;
}

.page-numbers {
display: flex;
margin: 0 10px;
}

.page {
padding: 8px;
margin: 0 5px;
cursor: pointer;
background-color: #f1f1f1;
border-radius: 3px;
}

.page.active {
background-color: #050505;
color: white;
}

.page:hover {
background-color: #ddd;
}




@media (max-width: 768px) {
    .container {
        flex-direction: column;
    }


    .header .stats, .boxes-container {
        flex-direction: column;
    }

    .header .stat, .box {
        margin-right: 0;
        width: 100%;
    }

    .payment-pending table td {
        display: block;
        width: 100%;
        text-align: left;
        padding: 10px 5px;
    }

    .payment-pending table td:last-child {
        text-align: left;
    }
}

@media (max-width: 480px) {
    .sidebar nav ul li {
        width: 100%;
    }
}

.upcoming-shoot {
    background-color: #fff;
    padding: 20px;
    border-radius: 10px;
    margin-top: 20px; /* Add margin to separate from other content */
}

.upcoming-shoot h2 {
    margin-top: 0;
}

.shoot-item {
    display: flex;
    align-items: center;
    padding: 10px;
    background-color: #f4f4f4;
    margin-bottom: 10px;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.shoot-item:hover {
    background-color: #e0e0e0;
}

.shoot-item img {
    width: 40px;
    height: 40px;
    margin-right: 10px;
    border-radius: 5px;
}

.shoot-details {
    flex-grow: 1;
}

.shoot-details p {
    margin: 0;
    font-size: 14px;
}

.shoot-details p.company {
    font-weight: bold;
}

.arrow-icon {
    width: 15px !important; /* Adjusted width */
    height: 15px !important; /* Adjusted height */
}
.project-box {
    border: 1px solid #ccc;
    border-radius: 8px;
    background-color: #f9f9f9;
    transition: transform 0.3s, background-color 0.3s;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.project-box:hover {
    transform: scale(1.05);
    background-color: #e0e0e0;
}

.map-area {
    width: 100%;
    height: 140px; /* Adjusted height for the map area */
    background-color: #ddd;
    border-radius: 4px;
    margin-bottom: 10px; /* Space below the map */
}

.project-name {
    font-size: 16px;
    font-weight: bold;
    text-align: center; /* Center text */
    margin: 0;
}

.project-status {
    font-size: 14px;
    color: #555;
    text-align: center; /* Center text */
    margin: 0;
}
.project-pending-section {
    margin-top: 20px;
    padding: 20px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.project-pending-section h2 {
    font-size: 24px;
    margin-bottom: 10px;
    text-align: left;
}

.pending-projects {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* Three boxes per row */
    gap: 15px; /* Space between boxes */
}

.project-box {
    border: 1px solid #ccc;
    border-radius: 8px;
    background-color: #f9f9f9;
    transition: transform 0.3s, background-color 0.3s;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.project-box:hover {
    transform: scale(1.05);
    background-color: #e0e0e0;
}

.map-area {
    width: 100%;
    height: 120px; /* Adjusted height for the map area */
    background-color: #ddd;
    border-radius: 4px;
    margin-bottom: 10px; /* Space below the map */
    display: flex;
    justify-content: center;
    align-items: center;
}

.map-area img {
    max-width: 100%; /* Responsive image */
    max-height: 100%; /* Responsive image */
    object-fit: cover; /* Cover the area */
}

.project-name {
    font-size: 16px;
    font-weight: bold;
    text-align: left; /* Align text to the left */
    margin: 0;
}

.project-status {
    font-size: 14px;
    color: #555;
    text-align: left; /* Align text to the left */
    margin: 0;
}

.project-namep2 {
    text-align: left; /* Align text to the left */
}

@media (max-width: 768px) {
    .pending-projects {
        grid-template-columns: repeat(2, 1fr); /* Two boxes per row on smaller screens */
    }
}

@media (max-width: 480px) {
    .pending-projects {
        grid-template-columns: 1fr; /* One box per row on mobile screens */
    }
}
.user-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: #ddd;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    font-size: 18px;
    color: #0e0e0e;
    background-color: #e1ecb8;

}
@media (max-width: 768px) {
    .item {
        flex: 1 1 100%; /* Full-width on smaller screens */
    }
}
</style>


    </head>
    <body>

        <div class="container">
            <aside class="sidebar">
                <div class="toggle-icon">
                    <img src="{% static 'images/menuleft.png' %}" alt="icon" id="toggle-icon" width="16" height="16">
                </div>
                <div class="logo">
                  <img src="{% static './images/logowhite.png' %}" alt="logo" width="50" height="50">
                </div>
                <nav>
                     <ul>
                    <li class="menu-item active"><a href="{% url 'dashboard' %}"><img src="{% static 'images/dashboard.png' %}" class="menu-icon"><span class="menu-text">Dashboard</span></a></li>
                    <li class="menu-item"><a href="{% url 'projects' %}"><img src="{% static 'images/project.png' %}" class="menu-icon"><span class="menu-text">Project</span></a></li>
                    <li class="menu-item"><a href="{% url 'incomef_view' %}"><img src="{% static 'images/income.png' %}" class="menu-icon"><span class="menu-text">Income</span></a></li>
                    <li class="menu-item"><a href="{% url 'expense' %}"><img src="{% static 'images/expenses.png' %}" class="menu-icon"><span class="menu-text">Expense</span></a></li>
                    <li class="menu-item"><a href="{% url 'calendar' %}"><img src="{% static 'images/calendar.png' %}" class="menu-icon"><span class="menu-text">Calendar</span></a></li>
                    <li class="menu-item"><a href="{% url 'allproject' %}"><img src="{% static 'images/All projects.png' %}" class="menu-icon"><span class="menu-text">All Projects</span></a></li>
                    <li class="menu-item"><a href="{% url 'clientsbook' %}"><img src="{% static 'images/clientsbook.png' %}" class="menu-icon"><span class="menu-text">Clients Book</span></a></li>
                    <li class="menu-item"><a href="{% url 'clients' %}"><img src="{% static 'images/Clients.png' %}" class="menu-icon"><span class="menu-text">Clients</span></a></li>
                    <li class="menu-item"><a href="{% url 'status' %}"><img src="{% static 'images/status.png' %}" class="menu-icon"><span class="menu-text">Status</span></a></li>
                    <li class="menu-item"><a href="{% url 'pending_pay' %}"><img src="{% static 'images/pending.png' %}" class="menu-icon"><span class="menu-text">Pending Payments</span></a></li>
                    <li class="menu-item"><a href="{% url 'project_map' %}"><img src="{% static 'images/map.png' %}" class="menu-icon"><span class="menu-text">Map</span></a></li>
                    <li class="menu-item"><a href="{% url 'assets' %}"><img src="{% static 'images/Assets.png' %}" class="menu-icon"><span class="menu-text">Assets</span></a></li>
                    <li class="menu-item"><a href="{% url 'budget' %}"><img src="{% static 'images/budget.png' %}" class="menu-icon"><span class="menu-text">Budget</span></a></li>
                    <li class="menu-item"><a href="{% url 'entertainment' %}"><img src="{% static 'images/Entertainment.png' %}" class="menu-icon"><span class="menu-text">Entertainment</span></a></li>
                </ul>
                </nav>
                <div class="profile-section" id="profileMenu">
                    <div class="user-icon" id="userIcon">

                  </div>

                    <span class="menu-text" id="name">{{ user.username }}</span>
                    <div class="dropdown" id="profileDropdown">
                        <ul>
                            <li><a href="{% url 'profile' %}">View Profile</a></li>
                            <li><a href="{% url 'logout_view' %}">Sign Out</a></li>
                        </ul>
                    </div>
                  </div>
            </aside>

            <main class="main-content">
                <header class="header">
                    <div class="stats">
                        <div class="stat">
                            <span>Income</span>
                            <strong>₹{{ tincome }}</strong>
                        </div>
                        <div class="stat">
                            <span>Total Expense</span>
                            <strong>₹{{ texpense }}</strong>
                        </div>
                        <div class="stat">
                            <span>Current Balance</span>
                            <strong>₹{{ curbal }}</strong>
                        </div>
                        <div class="stat">
                            <span>Pending</span>
                            <strong>₹{{ tpending }}</strong><br>
                            {{pending_count}}
                        </div>
                    </div>
                    <div class="button-container">
                        <button class="button">
                            <img src="{% static 'images/clientsd.png' %}" alt="Clients Icon">
                            Clients
                        </button>
                        <button class="button">
                            <img src="{% static 'images/map-p.png' %}" alt="Map Icon">
                            Map
                        </button>
                        <button class="button">
                            <img src="{% static 'images/cald.png' %}" alt="Calendar Icon">
                            Calendar
                        </button>
                        <button class="button">
                            <img src="{% static 'images/statusd.png' %}" alt="Status Icon">
                            Status
                        </button>
                    </div>

                </header>

                <section class="upcoming-shoot">
                    {% if todayshoot %}<!-- today shoot -->
                    <h2>Today Shoot</h2>
                    {% for item in todayshoot %}
                    <div class="shoot-item" onclick="moveToDetailPage('{{ item.code }}')">
                        {% if item.location %}
                        <img src="" alt="Map" class="map" id="today-map-{{ forloop.counter }}" data-address="{{ item.location }}" data-map-url="">
                        {% else %}
                        <img src="{% static 'images/map.png' %}" alt="Map" class="map">
                        {% endif %}
                        <div class="shoot-details">
                            <p class="company">{{ item.company }}</p>
                            <p>{{ item.name }}</p>
                            <p>{{ item.shoot_start_date }}</p>
                        </div>
                        <i class="fa-solid fa-chevron-right arrow-icon"></i>
                    </div>
                    {% endfor %}
                    {% endif %}

                    {% if weekshoot %}<!-- upcoming week shoot -->
                    <h2>Upcoming Shoot</h2>
                    {% for item in weekshoot %}
                    <div class="shoot-item" onclick="moveToDetailPage('{{ item.code }}')">
                        {% if item.location %}
                        <img src="" alt="Map" class="map" id="week-map-{{ forloop.counter }}" data-address="{{ item.location }}" data-map-url="">
                        {% else %}
                        <img src="{% static 'images/map.png' %}" alt="Map" class="map">
                        {% endif %}
                        <div class="shoot-details">
                            <p class="company">{{ item.company }}</p>
                            <p>{{ item.name }}</p>
                            <p>{{ item.shoot_start_date }}</p>
                        </div>
                        <i class="fa-solid fa-chevron-right arrow-icon"></i>
                    </div>
                    {% endfor %}
                    {% endif %}
                </section>


        <!-- payment pending-->
        <div class="payment-pending">
            <h2>Pending Payments</h2>
            <table id="data-table">

                <tbody>
                    {% for item in page_obj %}
                    <tr onclick="moveToDetailPage('{{ item.code }}')">
                        <td>{{item.name}}</td>
                        <td>₹{{item.pending_amt}}</td>
                        <td>{{item.company}}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- pagination code -->
        <div class="pagination">
            <button id="prev" class= "arrow" {% if not page_obj.has_previous %}disabled{% endif %}>←</button>
            <span id="page-info">
                {{ page_obj.number }}
            </span>
            <button id="next" class="arrow" {% if not page_obj.has_next %}disabled{% endif %}>→</button>
        </div>






        <div class="project-pending-section">
            <h2>Projects Pending</h2>
            <div class="pending-projects">

                <!-- pending projects-->
                {% for item in pendpage_obj %}
                <div class="project-box" onclick="moveToDetailPage('{{ item.code }}')">
                    <div class="map-area">
                        {% if item.location %}
                        <img src="" alt="Map" class="map" id="pending-map-{{ forloop.counter }}" data-address="{{ item.location }}" data-map-url="">
                        {% else %}
                        <img src="{% static 'images/map.png' %}" alt="Map" class="map">
                        {% endif %}
                    </div>
                    <p class="project-name">{{ item.company }}</p>
                    <p class="project-namep2">{{ item.name }}</p>
                    <p class="project-status">Status: {{ item.status }}</p>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- pagination code -->
        <div class="paginationp">
            <button id="prevp" class= "arrow" {% if not pendpage_obj.has_previous %}disabled{% endif %}>←</button>
            <span id="page-infop">
              {{ pendpage_obj.number }}
            </span>
            <button id="nextp" class="arrow" {% if not pendpage_obj.has_next %}disabled{% endif %}>→</button>
        </div>


        <script> // pagination code
            function loadPendPage(page) {
                $.ajax({
                    url: window.location.pathname + '?page=' + page + '&data_type=pending_projects',
                    type: 'GET',
                    headers: {'X-Requested-With': 'XMLHttpRequest'},
                    dataType: 'json',
                    success: function (data) {
                        if (!data || !data.itemsp) {
                            console.error('Received invalid data from server');
                            return;
                        }
                        var projectContainer = $('.pending-projects');
                        projectContainer.empty();

                        $.each(data.itemsp, function (index, item) {
                            // Determine the image source - use project image if available, otherwise use default map image
                            var imageSrc = item.image_url || "{% static 'images/map.png' %}";

                            projectContainer.append(`
                            <div class="project-box" onclick="moveToDetailPage('${item.code}')">
                                <div class="map-area">
                                    <img src="${imageSrc}" alt="Project Image" class="map" data-address="${item.location || ''}" data-map-url="">
                                </div>
                                <p class="project-name">${item.company || ''}</p>
                                <p class="project-namep2">${item.name || ''}</p>
                                <p class="project-status">Status: ${item.status || ''}</p>
                            </div>
                        `);
                        });

                        $('#page-infop').text( data.page_numberp);
                        $('#prevp').prop('disabled', !data.has_previousp);
                        $('#nextp').prop('disabled', !data.has_nextp);
                        $('#prevp').data('page', data.previous_page_numberp);
                        $('#nextp').data('page', data.next_page_numberp);


            },

            error: function(jqXHR, textStatus, errorThrown) {
                console.error('Error loading data:', textStatus, errorThrown);
                alert("Error loading data. Please try again.");
            }
                });
            }

            $(document).ready(function() {
                $('#prevp, #nextp').click(function (e) {
                    e.preventDefault();
                    var page = $(this).data('page');
                    if (page) {
                        loadPendPage(page);
                    } else {
                        console.warn('No page number available for', this.id, 'button');
                    }
                });

                // Load initial page
                loadPendPage(1);
            });
        </script>


        <script> // when there is no need of pagination , we need to hide it...
            $(document).ready(function() {
                $('#prevp, #nextp').click(function (e) {
                    e.preventDefault();
                    var page = $(this).data('page');
                    if (page) {
                        loadPendPage(page);
                    } else {
                        console.warn('No page number available for', this.id, 'button');
                    }
                });

                // Load initial page
                loadPendPage(1);

                // Hide pagination if both buttons are disabled
                function togglePaginationVisibilityp() {
                    if ($('#prevp').is(':disabled') && $('#nextp').is(':disabled')) {
                        $('.paginationp').hide();
                    } else {
                        $('.paginationp').show();
                    }
                }

                // Check the visibility when the page is loaded
                togglePaginationVisibilityp();
            });
            </script>







        <!--bar plot for income and expense -->
        <h3>Monthly income and expense</h3>
        <canvas id="inexChart" width="800" height="300"></canvas>

        <!--line plot for total projects -->
        <h3>Projects</h3>
        <canvas id="procChart" width="800" height="300"></canvas>

        <div>
            <h1>Expenses</h1>
        </div>
        <div class="chart-container">
            <canvas id="expensesChart" width="400" height="400"></canvas>
        </div>

        <div class="payment-pending">
            <table>
                {% for category, amount in category_expenses.items %}

                <tr onclick="movetototalexpense()">
                    <td>{{ category }}</td>
                    <td>₹{{ amount }}</td>
                </tr>

                {% endfor %}

            </table>
        </div>


        <!-- expense bar chart -->
        <div class="chartcontainer">
            <canvas id="monthlyExpensesChart" width="800" height="500"></canvas>
        </div>

        <!-- category chart for expense -->
        <div class="chartcontainer">
            <canvas id="categorychart" width="800" height="300"></canvas>
        </div>


        </main>
    </div>
    <script>
        const sidebar = document.querySelector('.sidebar');
        const toggleIcon = document.getElementById('toggle-icon');

        toggleIcon.addEventListener('click', function() {
          if (sidebar.classList.contains('closed')) {
            sidebar.classList.remove('closed');
            toggleIcon.src = "{% static 'images/menuleft.png' %}";  // Change to the "left-chevron" when open
          } else {
            sidebar.classList.add('closed');
            toggleIcon.src = "{% static 'images/menuright.png' %}";  // Change to the "chevron" when closed
          }
        });
      </script>
    <script>
        // JavaScript to handle dropdown visibility
        const profileMenu = document.getElementById('profileMenu');
        const profileDropdown = document.getElementById('profileDropdown');

        profileMenu.addEventListener('click', function () {
            // Toggle dropdown visibility
            if (profileDropdown.style.display === 'none' || profileDropdown.style.display === '') {
                profileDropdown.style.display = 'block';
            } else {
                profileDropdown.style.display = 'none';
            }
        });

        // Close dropdown if clicked outside
        window.addEventListener('click', function (event) {
            if (!profileMenu.contains(event.target)) {
                profileDropdown.style.display = 'none';
            }
        });
    </script>
    <script> // pagination code
        function loadPage(page) {
            $.ajax({
                url: window.location.pathname + '?page=' + page + '&data_type=projects',
                type: 'GET',
                headers: {'X-Requested-With': 'XMLHttpRequest'},
                dataType: 'json',
                success: function (data) {
                    if (!data || !data.items) {
                        console.error('Received invalid data from server');
                        return;
                    }
                    var tbody = $('#data-table tbody');
                    tbody.empty();

                    $.each(data.items, function (index, item) {
                        tbody.append('<tr onclick="moveToDetailPage(\'' + item.code + '\')">' +
                            '<td>' + (item.name || '') + '</td>' +
                            '<td>₹' + (item.pending_amt || 0) + '</td>' +
                            '<td>' + (item.company || '') + '</td>' +
                            '</tr>');
                    });

                    $('#page-info').text(data.page_number );
                    $('#prev').prop('disabled', !data.has_previous);
                    $('#next').prop('disabled', !data.has_next);
                    $('#prev').data('page', data.previous_page_number);
                    $('#next').data('page', data.next_page_number);


        },

        error: function(jqXHR, textStatus, errorThrown) {
            console.error('Error loading data:', textStatus, errorThrown);
            alert("Error loading data. Please try again.");
        }
            });
        }

        $(document).ready(function() {
            $('#prev, #next').click(function (e) {
                e.preventDefault();
                var page = $(this).data('page');
                if (page) {
                    loadPage(page);
                } else {
                    console.warn('No page number available for', this.id, 'button');
                }
            });

            // Load initial page
            loadPage(1);
        });
    </script>
    <script>
        $(document).ready(function() {
            $('#prev, #next').click(function (e) {
                e.preventDefault();
                var page = $(this).data('page');
                if (page) {
                    loadPage(page);
                } else {
                    console.warn('No page number available for', this.id, 'button');
                }
            });

            // Load initial page
            loadPage(1);

            // Hide pagination if both buttons are disabled
            function togglePaginationVisibility() {
                if ($('#prev').is(':disabled') && $('#next').is(':disabled')) {
                    $('.pagination').hide();
                } else {
                    $('.pagination').show();
                }
            }

            // Check the visibility when the page is loaded
            togglePaginationVisibility();
        });
        </script>

        <script>// income and expense
            document.addEventListener('DOMContentLoaded', (event) => {
                const ctx = document.getElementById('inexChart').getContext('2d');
                const months = JSON.parse('{{ months|escapejs }}');
                const incomeValues = JSON.parse('{{ income_values|escapejs }}');
                const expenseValues = JSON.parse('{{ expense_values|escapejs }}');

                const inexChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: months,
                        datasets: [
                            {
                                label: 'Income',
                                data: incomeValues,
                                backgroundColor: 'rgba(0,0,0)',
                                borderWidth: 1,
                                borderRadius: 6,
                                barThickness: 21,

                            },
                            {
                                label: 'Expense',
                                data: expenseValues,
                                backgroundColor: '#80557b',
                                borderWidth: 1,
                                borderRadius: 5,
                                barThickness: 21
                            }
                        ]
                    },
                    options: {
                        interaction: {
                            mode: 'index',
                            intersect: false
                        },
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'top',
                                align: 'start',
                                labels: {
                                    usePointStyle: true,
                                    pointStyle: 'rectRounded',
                                    color: 'black',
                                    boxWidth: 20,
                                    boxHeight: 20
                                }
                             },
                            tooltip: {
                                callbacks: {
                                    title: function(tooltipItems) {
                                        // Show the month
                                        return months[tooltipItems[0].dataIndex];
                                    },
                                    label: function(tooltipItem) {
                                        const datasetLabel = tooltipItem.dataset.label || '';
                                        const dataValue = tooltipItem.raw;
                                        return `${datasetLabel}: ${dataValue.toFixed(2)}`;
                                    },


                                },
                                displayColors: true,

                            }
                        },
                        scales: {
                            x: {
                                stacked: true,
                                ticks: {
                                    autoSkip: false,
                                    display : true
                                },
                                barPercentage: 0.8,
                                actegoryPercentage: 0.5

                            },

                            y: {
                                stacked: true,
                                beginAtZero: true
                            }
                        }
                    }
                });
            });
        </script>


        <script>// number of projects
            document.addEventListener('DOMContentLoaded', (event) => {
                const ctx = document.getElementById('procChart').getContext('2d');
                const months = JSON.parse('{{ months|escapejs }}');
                const pro_c = JSON.parse('{{ pro_c|escapejs }}');

                const procChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: months,
                        datasets: [
                            {
                                label: 'Total projects',
                                data: pro_c,
                                backgroundColor: 'rgba(0,0,0)',
                                borderColor: 'rgba(0,0,0)',
                                usePointStyle: true,
                                pointStyle: 'rectRounded',
                                borderWidth: 2,
                                fill: false,
                                tension: 0.1

                            }

                        ]
                    },
                    options: {
                        interaction: {
                            intersect : false

                        },
                        responsive: true,
                        plugins: {
                            legend: {
                                display: false //remove legend

                             },
                            tooltip: {
                                backgroundColor: 'rgba(255, 255, 255,1)',  // Light background color
                                titleColor: 'black',
                                bodyColor: 'black',
                                borderColor: '#ccc',     //  Add a border color for clarity
                                borderWidth: 1,          // Set border width
                                caretColor: '#000',      // Color of the caret (arrow) below the tooltip

                                callbacks: {
                                    title: function(tooltipItems) {
                                        // Show the month
                                        return months[tooltipItems[0].dataIndex];
                                    },
                                    label: function(tooltipItem) {
                                        // Show both values
                                        const month = months[tooltipItem.dataIndex];
                                        const pro_cn = pro_c[tooltipItem.dataIndex];
                                        return `Total Projects: ${pro_cn}`;

                                    }

                                },
                                displayColors: true,

                            }
                        },
                        scales: {
                            x: {
                                ticks: {
                                    autoSkip: false,
                                    display : true
                                },

                            },

                            y: {
                                stacked: false,
                                beginAtZero: true
                            }
                        }
                    }
                });
            });
        </script>

        <script> // expense pie chart

            document.addEventListener('DOMContentLoaded', (event) => {
                const ctx = document.getElementById('expensesChart').getContext('2d');
                const categories = JSON.parse('{{ pie_cat|escapejs }}');
                const amounts = JSON.parse('{{ pie_amt|escapejs }}');

                const totalAmount = amounts.reduce((sum, value) => sum + value, 0);

                const backgroundColors = [
                'rgb(217,155,130,1)',
                'rgb(77,103,91)',
                'rgb(217,130,169,1)',
                'rgb(4,107,133)',
                'rgb(112,152,175)',
                'rgb(140,184,191)',
                'rgb(243,231,150,1)',
                'rgb(164,132,142)',
                'rgb(76,148,145)',
                'rgb(150,243,197,1)',
                'rgba(101,93,139,255)',
                'rgb(243,150,152,1)',
                'rgb(243,233,81,1)',
                'rgba(128,85,123,255)',
                'rgb(130,194,217,1)',
                'rgb(143,130,217,1)'
            ];

                const originalBackgroundColors = [...backgroundColors];

                const expensesChart = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: categories,
                        datasets: [{
                            data: amounts,
                            backgroundColor: backgroundColors,
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                display: false,
                                usePointStyle: true,
                                pointStyle:'rectRounded'
                            },
                            tooltip: {

                                callbacks: {
                                    label: function(tooltipItem) {
                                        const label = categories[tooltipItem.dataIndex];
                                        const value = amounts[tooltipItem.dataIndex];
                                        const percentage = ((value / totalAmount) * 100).toFixed(2);
                                        return `${label}: ${value.toFixed(2)} ${percentage}%`;
                                    }
                                },
                                displayColors: true,
                                boxWidth: 12,
                                boxHeight: 12,
                            }
                        },

                    }
                });
            });
        </script>


        <!-- expense bar chart  first-->

        <script>
            document.addEventListener('DOMContentLoaded', (event) => {
                const ctx = document.getElementById('monthlyExpensesChart').getContext('2d');
                const months = JSON.parse('{{ month_name|escapejs }}');
                const datasets = JSON.parse('{{ datasets|escapejs }}');

                const categoryColors = [
                    'rgb(20,97,103)',  // Color 1
                    'rgb(45,179,190,1)',  // Color 2
                    'rgb(121,90,128)',  // Color 3
                    'rgb(189,121,202,1)',  // Color 4
                    'rgb(120,97,57)',  // Color 5
                    'rgb(177,142,79,1)',  // Color 6
                    'rgb(161,161,161,1)',  // Color 7
                    'rgb(158,138,142)',  // Color 8
                    'rgb(186,143,153,1)',   // Color 9
                    'rgb(176,183,183)',  // Color 10
                    'rgb(79,64,59)',  // Color 11
                    'rgb(157,131,124,1)',   // Color 12
                    'rgb(181,172,150)',    // Color 13
                    'rgb(60,148,156)',   // Color 14
                    'rgb(116,187,194,1)',   // Color 15
                    'rgb(98,204,169,1)'   // Color 16
                ];

                datasets.forEach((dataset, index) => {
                    dataset.backgroundColor = categoryColors[index % categoryColors.length];
                    dataset.barThickness = 21;
                    dataset.borderRadius = 6;
                    dataset.borderColor = 'rgba(0,0,0,0.1)'; // Example border color
                    dataset.borderWidth = 0.5;
                });

                const monthlyExpensesChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: months,
                        datasets: datasets
                    },
                    options: {
                        interaction: {
                            mode: 'index',
                            intersect: false
                        },
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top',
                                align: 'start',
                                labels: {
                                    usePointStyle: true,
                                    pointStyle: 'rectRounded',
                                    color: 'black',
                                    boxWidth: 20,
                                    boxHeight: 20
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    title: function(tooltipItems) {
                                        return tooltipItems[0].label;  // Display the month
                                    },
                                    beforeBody: function(tooltipItems) {
                                        return 'Expenses:';
                                    },
                                    label: function(context) {
                                        const label = context.dataset.label || '';
                                        const value = context.raw;
                                        return `${label}: ${value.toFixed(2)}`;
                                    },
                                    footer: function(tooltipItems) {
                                        let total = 0;
                                        tooltipItems.forEach(function(tooltipItem) {
                                            total += tooltipItem.raw;
                                        });
                                        return `Total: ${total.toFixed(2)}`;
                                    }
                                },
                                displayColors: true,
                                callbacks: {
                                    labelColor: function(context) {
                                        return {
                                            backgroundColor: context.dataset.backgroundColor
                                        };
                                    }
                                }
                            }
                        },
                        scales: {
                            x: {
                                stacked: true
                            },
                            y: {
                                stacked: true,
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return `${value}`;
                                    }
                                }
                            }
                        }
                    }
                });
            });
        </script>



        <script> // category chart second
                document.addEventListener('DOMContentLoaded', function () {
                    const ctx = document.getElementById('categorychart').getContext('2d');

                    const labels = JSON.parse('{{ cat_bar2|escapejs }}');

                    const monthNames = [
                        'January', 'February', 'March', 'April', 'May', 'June',
                        'July', 'August', 'September', 'October', 'November', 'December'
                    ];

                    const monthColors = [
                    'rgb(20,97,103)',  // Color 1
                    'rgb(45,179,190,1)',  // Color 2
                    'rgb(121,90,128)',  // Color 3
                    'rgb(189,121,202,1)',  // Color 4
                    'rgb(120,97,57)',  // Color 5
                    'rgb(177,142,79,1)',  // Color 6
                    'rgb(60,148,156)',  // Color 7
                    'rgb(158,138,142)',  // Color 8
                    'rgb(186,143,153,1)',   // Color 9
                    'rgb(176,183,183)',  // Color 10
                    'rgb(79,64,59)',  // Color 11
                    'rgb(157,131,124,1)',
                    ];

                    const rawData = JSON.parse('{{ data_bar2|escapejs }}');
                    const datasets = [];

                    for (let monthIndex in rawData) {
                        const month = parseInt(monthIndex) - 1; // Convert monthIndex to integer and adjust for zero-based index
                        datasets.push({
                            label: monthNames[month],
                            backgroundColor: monthColors[month],
                            data: rawData[monthIndex],
                            borderColor: 'rgba(0,0,0,0.1)', // Example border color
                            borderWidth: 0.5,
                        });
                    }

                    const data = {
                        labels: labels,
                        datasets: datasets
                    };

                    const config = {
                        type: 'bar',
                        data: data,
                        options: {
                            interaction: {
                                mode: 'index',
                                intersect: false,
                            },
                            responsive: true,
                            plugins: {
                                legend:{
                                    position: 'top',
                                    align: 'start',
                                    labels:{
                                        usePointStyle: true,
                                        pointStyle: 'rectRounded',
                                        color: 'black',
                                        boxWidth: 20,
                                        boxHeight: 20
                                    }
                                }

                            },
                            scales: {
                                x: {
                                    stacked: true,
                                },
                                y: {
                                    stacked: true,
                                }
                            },
                            barThickness: 20, // Set the fixed bar thickness
                            borderRadius: 6,
                        }
                    };

                    const expenseChart = new Chart(ctx, config);
                });
            </script>

            <script>
                // page navigation from div
                function moveToDetailPage(code) {
                    window.location.href = "/project/" + code + "/";
                }
            </script>

            <script>
                // move tot total expense
                function movetototalexpense() {
                    window.location.href = "/total-expense/";
                }
            </script>




<!-- map image display-->
<script>
    function fetchStaticMap(address, elementId) {
        const apiKey = 'AIzaSyAqIalCM0rqsXeHiyRP4ImBbVgqwMSv8D8'; // Replace with your actual Google Maps API key
        const mapUrl = `https://maps.googleapis.com/maps/api/staticmap?center=${encodeURIComponent(address)}&zoom=14&size=400x300&maptype=roadmap&markers=color:red%7C${encodeURIComponent(address)}&key=${apiKey}`;

        // Set the map URL to the image element and data attribute
        const mapElement = document.getElementById(elementId);
        if (mapElement) {
            mapElement.src = mapUrl;
            mapElement.setAttribute('data-map-url', mapUrl);
        }
    }

    document.addEventListener('DOMContentLoaded', function () {
        const mapElements = document.querySelectorAll('img.map[data-address]');
        mapElements.forEach((element) => {
            const address = element.getAttribute('data-address');
            const elementId = element.id;
            fetchStaticMap(address, elementId);
        });
    });
</script>
<script>
    // user icon
    const username = document.getElementById('name').textContent;
    document.querySelector('#userIcon').innerText = username.charAt(0);
</script>



    </body>
</html>