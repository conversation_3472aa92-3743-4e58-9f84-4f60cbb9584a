{% load static %}
{% static "images" as baseurl %}
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Details</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<style>
    body {
        margin: 0;
        font-family: Arial, sans-serif;
        background-color: #f5f5f5;
        display: flex;
        height: 100vh;
    }
    .container {
        display: flex;
        width: 100%;
    }
    .sidebar {
    background-color: #1e1e1e;
    color: white;
    width: 250px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
}

.sidebar .logo {
    padding: 20px;
    text-align: center;
}

.menu-title {
    padding: 10px 0;
    text-align: center;
}

.sidebar nav ul {
    list-style: none;
    padding: 0;
    width: 100%;
}

.sidebar nav ul li {
    padding: 12px 20px;
    cursor: pointer;
    transition: background-color 0.3s, color 0.3s, border-left 0.3s;
    text-align: left;
    display: flex;
    align-items: center;
}

.sidebar nav ul li:hover {
    background-color: #333;
    color: #fff;
    border-left: 4px solid #fff;
}

.menu-icon {
    margin-right: 10px;
    width: 24px;
    height: 24px;
}
    .main-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        background-color: white;
    }
    .project-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;
        background-color: white;
        border-bottom: 1px solid #ddd;
    }
    .project-header h4 {
        margin: 0;
    }
    .project-title {
        font-size: 30px;
        font-weight: bold;
        margin-right: 80px;
        margin-bottom: 10px;
        margin-top: 10px;
    }
    .project-status {
        color: rgb(0, 0, 0);
        margin-right: 20px;
        margin-bottom: 10px;
    }
    .edit-button {
        background-color: #000;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        margin-top: 20px;
    }
    .project-details {
        background-color: white;
        padding: 20px;
        border-radius: 8px;
        margin-top: 20px;
    }
    .project-details img {
        width: 40%;
        height: 300px;
        object-fit: cover;
        margin-bottom: 10px;
    }
    .project-info {
        margin-top: 20px;
    }
    .info-row {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        border-bottom: 1px solid #eee;
        align-items: center;
    }
    .info-row:last-child {
        border-bottom: none;
    }
    .info-label {
        font-weight: bold;
    }
    .info-llabel {
        font-weight:medium;
        margin-top: -10px;
        color:#9a9595;
    }
    .received-bar-container {
        width: 100%;
        background-color: #e0e0e0;
        border-radius: 4px;
        overflow: hidden;
        margin: 5px 0;
        height: 13px;
    }
    .received-bar {
        height: 15px;
        background-color: #030303;
        width: 70%;
        text-align: right;
        padding-right: 5px;
        color: white;
        line-height: 15px;
    }
    .contact-buttons {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 50px;
        margin-top: 10px;
        margin-left:1000px;
    }
    .contact-buttons button {
        padding: 10px 20px;
        margin-left: 10px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }
    .contact-buttons button.call,
    .contact-buttons button.sms,
    .contact-buttons button.map,
    .contact-buttons button.copy {
        background-color: #000;
        color: white;
    }
    .file {
        display: inline-flex;
        justify-content: space-between;
        padding: 1px;
        border: 1px solid #ccc;
        border-radius: 2px;
        margin-top: 5px;
        align-items: center;
        color: #000;
    }
    .file img {
        width: 50%;
        height: 30px;
        object-fit: cover;
        margin-bottom: 5px;
        padding: 20px;
    }

    .rating {
        display: flex;
        justify-content: space-between;
        padding: 10px;
        border: 1px solid #ccc;
        border-radius: 10px;
        margin-top: 30px;
        align-items: center;
        padding-right: 400px;
        flex-direction: column;
        width: 200px;

    }
    .expense{
        display: flex;
        justify-content: space-between;
        padding: 20px;
        border: 1px solid #ccc;
        border-radius: 10px;
        margin-top: 30px;
        align-items: center;
        padding-right: 100px;
    }
    .expense1{
        display: flex;
        justify-content: space-between;
        padding: 20px;
        border: 1px solid #ccc;
        border-radius: 10px;
        margin-top: 30px;
        align-items: center;
        padding-right: 100px;

    }
    .expense2{
        display: flex;
        justify-content: space-between;
        padding: 20px;
        border: 1px solid #ccc;
        border-radius: 10px;
        margin-top: 30px;
        align-items: center;
        padding-right: 100px;

    }

    .profit{
        display: flex;
        justify-content: space-between;
        padding: 20px;
        border: 1px solid #ccc;
        border-radius: 10px;
        margin-top: 30px;
        align-items: center;
        padding-right: 30px;
        margin-right:620px;
        font-size: 25px;
        color:#000000;

    }
    .profit1 span{
        color:#ababab;
        font-size: medium;
    }

    .indiv{
        padding-left: 35px;
        justify-content: space-between;
        border-radius: 4px;
    }
    .rating .stars {
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .rating .stars img {
        font-size: 10px;
        color: #616161;
        margin-right: 5px;
        height: 50px;
        width: 51%;
    }
    /* Modal styles */
    .modal {
        display: none;
        position: fixed;
        z-index: 1;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0, 0, 0, 0.4);
    }
    .modal-content {
        background-color: #fefefe;
        margin: 10% auto;
        padding: 20px;
        border: 1px solid #888;
        width: 400px;
        border-radius: 8px;
    }
    .close {
        color: #aaa;
        float: right;
        font-size: 28px;
        font-weight: bold;
    }
    .close:hover,
    .close:focus {
        color: black;
        text-decoration: none;
        cursor: pointer;
    }

    .expense-container {
display: flex;
flex-direction: column; /* Stack items vertically */
padding: 20px;
border: 1px solid #242424;
margin: 20px;
border-radius: 10px;
background-color: #000000;
}

.expense-item {
display: flex;
align-items: center; /* Align items vertically center */
padding: 10px 0;
border-bottom: 1px solid #333; /* Optional: Add border between items */
position: relative; /* Position relative for the arrow positioning */
}

.expense-item:last-child {
border-bottom: none; /* Remove border for the last item */
}

.expense-container .icon img {
width: 50px;
height: 50px;
margin-right: 15px; /* Space between icon and details */
}

.expense-container .expense-details {
flex: 1; /* Take up remaining space */
display: flex;
flex-direction: column;
align-items: flex-start;
}

.expense-container .arrow {
font-size: 20px;
color: #ebe8e8;
margin-left: 15px; /* Space between details and arrow */
position: absolute; /* Position absolute for correct placement */
right: 0; /* Align to the right */
}



    .icon {
        width: 50px;
        height: 50px;
        margin-right: 25px;
    }
    .icon img {
        width: 100%;
        height: 100%;
        margin-right: 25px;
    }
    .expense-details {
        flex: 1;
        margin-right: auto;
    }
    .exd{
        display: flex;
        justify-content: space-between;
        padding: 10px;
        border-bottom: 1px solid #333;
        font-size: 18px;
        color: white;
        font-weight: bold;
        margin-bottom: 10px;
    }


    .amount {
        font-size: 10px;
        font-weight:medium;
        color: white;
    }
    .category {
        font-size: 17px;
        color: white;
    }
    .subcategory {
        font-size: 15px;
        color: #aaa;
    }
    .sub{
        font-size: 17px;
        color: #fff;
    }
    .arrow a {
        color: inherit;
        text-decoration: none;
    }

    .arrow {
        font-size: 20px;
        color: #ebe8e8;
        margin-left: 15px;
        position: absolute;
        right: 0;
    }

    .form-actions {
        display: flex;
        justify-content: flex-end;
        margin-top: 20px;
    }
    .form-actions button {
        margin-left: 10px;
    }
    #editForm {
        display: flex;
        flex-direction: column;
    }
    #editForm label {
        margin-top: 10px;
    }
    #editForm input,
    #editForm select {
        padding: 8px;
        margin-top: 5px;
        border: 1px solid #ccc;
        border-radius: 4px;
    }
    #editForm button {
        padding: 10px;
        margin-top: 20px;
        background-color: #000000;
        color: #fff;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }
    #editForm button:hover {
        background-color: #555;
    }
    #editForm button[type="button"] {
        background-color: white; /* White background for cancel button */
        color: black; /* Black text for cancel button */
    }
.close {
color: #aaa;
float: right;
font-size: 28px;
font-weight: bold;
}

.close:hover,
.close:focus {
color: black;
text-decoration: none;
cursor: pointer;
}


    .info-row {
        display: flex;
        justify-content: space-between;
        padding: 30px 0;
        border-bottom: 1px solid #eee;
    }

    .info-row2 {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        border-bottom: 1px solid #eee;
    }

    .container1 {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        max-width: 800px;
        margin: auto;
    }
    .inforow {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 10px;
        padding-top: 10px;
    }
    .inforow .text {
        display: flex;
        flex-direction: column;
    }
    .inforow .text .name {
        font-size: 14px;
        color:#9a9595;
    }
    .inforow.text.phone-number{
        font-size: 19px;
        color: #000000;
        margin-top: 10px;
        font-weight: bolder;
    }
    .inforow .text .location {
        font-size: 14px;
        color:#9a9595;
        margin-top: 10px;
    }

    .inforow button {
        background-color: #e4e1e1;
        color: rgb(0, 0, 0);
        border: none;
        padding: 5px 15px;
        border-radius: 25px;
        cursor: pointer;
        margin-left: 10px;

    }
    .inforow button img {
        width: 20px; /* Adjust icon size */
        height: 20px; /* Adjust icon size */
        margin-right: 10px; /* Space between icon and text */
        padding-left:10px;
        margin-bottom: auto;
    }

    .filesrow {
display: flex;
justify-content: space-between;
align-items: center;
margin-top: 20px; /* Changed margin to only top */
}

.filesrow button {
display: flex;
align-items: center; /* Center the icon and text vertically */
justify-content: center; /* Center the icon and text horizontally */
color: white; /* Text color for buttons */
border: 1px solid #d0d0d0; /* Border for buttons */
padding: 10px 20px; /* Padding for buttons */
border-radius: 4px; /* Rounded corners */
cursor: pointer; /* Cursor on hover */
flex: 1; /* Allow buttons to grow */
margin: 0 5px; /* Space between buttons */
font-weight: bold;
}

.files-button {
background-color: black; /* Black background for Files button */
}

.share-button,
.copy-button {
background-color: white; /* White background for Share and Copy buttons */
color: black; /* Text color for Share and Copy buttons */
}

.filesrow button img {
width: 20px; /* Adjust icon size */
height: 20px; /* Adjust icon size */
margin-right: 10px; /* Space between icon and text */
}

.filesrow button:last-child {
margin-right: 0; /* No margin on the last button */
}
    .info-row:last-child {
        border-bottom: none;
    }
    .infolabel {
        font-weight: 200;
        font-size: 16px;
        margin-top: 18px;
        margin-left: 22px;
        color:#616161;
    }
    .inform{
        display: flex;
        justify-content: space-between;
        padding: 15px 0;
        border-bottom: 1px solid #eee;
        font-size:small;
        color:#555;
    }
    .inform-label {
        font-weight: bold;
        margin-top: 25px;
        font-size:medium;
        color:#000
    }
    .info{
display: flex;
justify-content: space-between; /* Distributes items evenly with space in between */
padding: 25px 0;
border-bottom: 1px solid #eee;
}
.info-item {
flex: 1; /* Allows items to grow and fill the available space equally */
text-align: center; /* Centers the text within each item */
font-size: large;
font-weight: 550;
color: #242424;
}

.info-item2 {
flex: 1; /* Allows items to grow and fill the available space equally */
text-align: center; /* Centers the text within each item */
color:#9a9595;
}
    .rating {
        display: flex;
        justify-content: space-between;
        padding: 5px;
        border: none;
        border-radius: 4px;
        margin-top: 20px;
        align-items: center;

    }


    .rating .stars {
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .rating .stars i {
        font-size: 30px;
        color: #ccc;
        margin-right: 5px;
        cursor: pointer;
    }
    .rating .stars i.filled {
        color: rgb(0, 0, 0);}

        .button img {
        width: 50%;
        height: 30px;
        object-fit: cover;
        margin-bottom: 5px;
        padding: 20px;
    }
    .container2 {
        background-color: #fff;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        border: 1px solid #ccc;
        width: 100%;
        max-width: 1000px;
        padding-top: 30px;
        margin-top:18px;
        padding-right:170px;
        margin-top:30px;

    }
    .info-row1 {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        margin-top:10px;
    }
    .info-item1 {
        font-size: 16px;
        color: #333;
    }
    .info-item1 span {
        font-weight: medium;
        color:#9a9595;
    }
    .project-header .map-image {
        width: 30%;
        height: auto;
        margin-right: 20px;
    }
    .row {
        display: flex;
        align-items: center;
    }
    .info-llabel {
font-weight: medium;
margin-top: -10px;
color:#9a9595;
 }

 .info-llabel1{
    color:#2c2c2c;
 }

/* Outsourcing Section Styles */
#outsourcingDetails {
margin-top: 10px;
}

#outsourcingDetails label {
display: block;
margin-top: 10px;
font-weight: medium;
}

#outsourcingDetails select,
#outsourcingDetails input[type="number"] {
width: 100%;
padding: 8px;
margin-top: 5px;
border: 1px solid #ccc;
border-radius: 4px;
}

.switch {
position: relative;
display: inline-block;
width: 34px;
height: 20px;
}

.switch input {
opacity: 0;
width: 0;
height: 0;
}

.slider {
position: absolute;
cursor: pointer;
top: 0;
left: 0;
right: 0;
bottom: 0;
background-color: #ccc;
transition: .4s;
border-radius: 20px;
}

.slider:before {
position: absolute;
content: "";
height: 16px;
width: 16px;
left: 2px;
bottom: 2px;
background-color: white;
border-radius: 50%;
transition: .4s;
}

input:checked + .slider {
background-color: #000000;
}

input:checked + .slider:before {
transform: translateX(14px);
}

/* use location styles */

.toggle-container {
    display: flex;
    align-items: center;
    margin: 10px 0;
}
.toggle {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
    margin-left: 10px; /* Space between the label and the toggle switch */
}
.toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

input:checked + .sliderr {
    background-color: #2196F3;
}
input:checked + .sliderr:before {
    transform: translateX(26px);
}
.sliderr {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}
.sliderr:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

</style>

</head>
<body>

    <body>
        <div class="container">
          <aside class="sidebar">
              <div class="logo">
                  <img src="{% static './images/logowhite.png' %}" alt="logo" width="50" height="50">
              </div>
              <nav>
                <ul>
                    <li><a href="{% url 'dashboard' %}"><img src="{% static './images/dashboard.png' %}" alt="dashboard icon" class="menu-icon">Dashboard</a></li>
                    <li><a href="{% url 'projects' %}"><img src="{% static './images/Project.png' %}" class="menu-icon">Project</a></li>
                    <li><a href="{% url 'incomef_view' %}"><img src="{% static './images/Income.png' %}" alt="Income Icon" class="menu-icon">Income</a></li>
                    <li><a href="{% url 'expense' %}"><img src="{% static './images/expenses.png' %}" alt="Expenses Icon" class="menu-icon">Expense</a></li>
                    <li><a href="{% url 'calendar_view' %}"><img src="{% static './images/calendar.png' %}" alt="Calendar Icon" class="menu-icon">Calendar</a></li>
                    <li><a href="{% url 'allproject' %}"><img src="{% static './images/All projects.png' %}" alt="All Projects Icon" class="menu-icon">All Projects</a></li>
                    <li><a href="{% url 'clientsbook' %}"><img src="{% static './images/Client books.png' %}" alt="Clients Book Icon" class="menu-icon">Clients Book</a></li>
                    <li><a href="{% url 'clients' %}"><img src="{% static './images/Clients.png' %}" alt="Clients Icon" class="menu-icon">Clients</a></li>
                    <li><a href="{% url 'status' %}"><img src="{% static './images/Status.png' %}" alt="Status Icon" class="menu-icon">Status</a></li>
                    <li><a href="{% url 'pending_pay' %}"><img src="{% static './images/pending.png' %}" alt="Pending Payments Icon" class="menu-icon">Pending Payments</a></li>
                    <li><a href="{% url 'project_map' %}"><img src="{% static './images/maps-and-flags.png' %}" alt="Map Icon" class="menu-icon">Map</a></li>
                    <li><a href="{% url 'assets' %}"><img src="{% static './images/Assets.png' %}" alt="Assets Icon" class="menu-icon">Assets</a></li>
                    <li><a href="{% url 'budget' %}"><img src="{% static './images/budget.png' %}" alt="Budget Icon" class="menu-icon">Budget</a></li>
                    <li><a href="{% url 'entertainment' %}"><img src="{% static './images/Entertainment.png' %}" src= alt="Entertainment Icon" class="menu-icon">Entertainment</a></li>
                </ul>
              </nav>
          </aside>


            <div class="main-content">
                <div class="project-header">
                    <div class="row">
                        {% if objs.image %}
                            <img src="{{objs.image.url}}" alt="Map" class="map-image">
                        {% else %}
                            <img src="{% static 'images/Project.png' %}" alt="Map" class="map-image">
                        {% endif %}
                        <div>
                            <h4>{{ objs.code }}</h4>
                            <div class="project-title">{{ objs.name }}</div>
                            <div class="project-status">{{objs.status }}</div>

                            <button class="edit-button" data-code="{{objs.code}}">Edit</button>
                        </div>
                    </div>
                </div>

                <div class="project-details">
                    <div class="project-info">
                        <div class="info-row">
                            <div class="info-llabel" style="margin-top: -50px;">Pending Amount<br>
                            <div class="info-llabel1"><b>{{objs.pending_amt}}</b></div></div>
                        </div>

                        <div class="info-row">
                            <div class="info-label">Received Amount</div>
                            <div class="received-bar-container">
                                <div class="received-bar" id="receivedBar">50%</div>
                            </div>
                        </div>
                        <div class="info">
                            <div class="info-item2">{{objs.type }}</div>
                            <div class="info-item">{{objs.company }}</div>
                            <div class="info-item"><p id="projectAmountDis">{{objs.amount }} </p></div>
                        </div>

                        <div class="inforow">
                            <div class="text">
                                <div class="phone-number"><b>{{objs.client.number }}</b></div>
                                <div class="name">{{objs.client.name}}</div>
                            </div>
                            <div>
                                <button><img src="telephone.png" alt="Call Icon"><b>Call</b></button>
                                <button><img src="communication.png" alt="SMS Icon"><b>SMS</b></button>
                            </div>
                        </div>
                        <div class="inforow">
                            <div class="text">
                                <div class="loc"><b>Location</b></div>
                                <div class="location">{{objs.location }}</div>
                            </div>
                            <div>
                                <button><img src="map.png" alt="Map Icon" class="menu-icon"><b>Map</b></button>
                                <button><img src="clone.png" alt="Copy Icon"><b>Copy</b></button>
                            </div>
                        </div>

                        <div class="filesrow">
                            <button class="files-button">
                                <img src="folder (2).png" alt="File Icon" class="file-icon">
                            </button>
                            <button class="share-button">
                                <img src="share (1).png" alt="Share Icon" class="file-icon">Share
                            </button>
                            <button class="copy-button">
                                <img src="clone.png" alt="Copy Icon" class="file-icon">Copy
                            </button>
                        </div>
                    </div>


                    <div class="container2">
                        <div class="info-row1">
                            <div class="info-item1">
                                <span>Shoot Start</span> <br>{{objs.shoot_start_date}}
                            </div>
                            <div class="info-item1">
                                <span>Shoot End</span> <br>{{objs.shoot_end_date}}
                            </div>
                            <div class="info-item1">
                                <span>Received Amount</span> <br><p id="receivedAmountDis">₹{{objs.received_amt}} </p>
                            </div>
                        </div>

                        {% if not objs.outsourcing %}
                        <div class="info-row1">
                            <div class="info-item1">
                                <span>Expense</span> <br>₹{{totalex}}
                            </div>
                        </div>
                        </div>
                        {% endif %}

                        {% if objs.outsourcing %}
                        <div class="info-row1">
                            <div class="info-item1">
                                <span>Expense</span> <br>₹{{totalex}}
                            </div>
                            <div class="info-item1">
                                <span>Outsourcing Amount</span> <br>₹{{objs.outsourcing_amt}}
                            </div>
                            <div class="info-item1">
                                <span>Outsourcing Paid</span> <br>
                                {% if objs.outsourcing_paid %}
                                {{objs.outsourcing_paid}}
                                {% else %}
                                False
                                {% endif %}
                            </div>
                        </div>

                        <div class="info-row1">
                            <div class="info-item1">
                                <span>Outsourcing Company</span> <br>{{objs.out_comp}}
                            </div>
                            <div class="info-item1">
                                <span>Outsourcing phone</span> <br>{{objs.out_num}}
                            </div>
                            <div class="info-item1">
                                <span>Outsourcing Customer Name</span> <br>{{objs.out_client}}
                            </div>
                        </div>

                    </div>
                    <span>{{objs.out_client}}</span><br>
                    <span>Outsourcing Customer Name</span>
                    {% endif %}

                    <div class="profit">
                        <div class="profit1">
                            <span>Profit</span><br>
                            <b>₹{{objs.profit}}</b></div>
                    </div>

                    <div class="info-row">
                        <div class="infolabel">Your rating</div>
                        <div class="info-value">
                            <div class="rating">
                                <div class="stars">
                                    <i class="star" data-value="1">&#9733;</i>
                                    <i class="star" data-value="2">&#9733;</i>
                                    <i class="star" data-value="3">&#9733;</i>
                                    <i class="star" data-value="4">&#9733;</i>
                                    <i class="star" data-value="5">&#9733;</i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>



            <div class="expense-container">

                <div class="exd">
                    <div class="sub">Expenses <br>₹{{totalex}}</div>
                </div>

                {% for expense in expenses %}
                <div class="expense-item" onclick="moveToExpensePage('{{ expense.id }}')" style="cursor: pointer;">
                <div class="icon">
                    <img src="fuel.png" alt="Fuel Icon">
                </div>
                    <div class="expense-details">

                    <div class="amount">₹{{expense.amount}}</div>
                    <div class="category">{{expense.category}}</div>
                    <div class="subcategory">{{expense.description}}</div>
                </div>
                <div class="arrow"><a href="expense-details.html">></a></div>
            </div>
                {% endfor %}


        </div>

    <!-- Modal for Edit Form -->
<div id="editModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeEditModal()">&times;</span>
        <form id="editForm" method="post">
            <label for="projectType">Type:</label>
            <select id="projectType" name="projectType" required>
                <option value="">---</option>
                <!-- dynamic type dispaly -->
            </select>

            <label for="projectStatus">Status:</label>
            <select id="projectStatus" name="projectStatus" required>
                <option value="">---</option>

                <option value="Completed">Completed</option>
                <option value="Pending">Pending</option>
                <option value="Ongoing">Ongoing</option>
                <option value="Cancelled">Cancelled</option>
            </select>

            <label for="projectName">Project Name</label>
            <input type="text" id="projectName" name="projectName" required>

            <label for="customerCompany">Customer Company</label>
            <input type="text" id="customerCompany" name="customerCompany" required>

            <label for="shootStart">Shoot Start</label>
            <input type="datetime-local" id="shootStart" name="shootStart" required>

            <label for="shootEnd">Shoot End</label>
            <input type="datetime-local" id="shootEnd" name="shootEnd" required>

            <label for="projectAmount">Project Amount</label>
            <input type="number" id="projectAmount" name="projectAmount" required>

            <label for="projectLocation">Project Location</label>
            <input type="text" id="projectLocation" name="projectLocation" required>

            <!--sandhiya-->
            <div class="form-group toggle-container">
                <label for="useLocation">Use Location</label>
                <label class="toggle">
                    <input type="checkbox" id="useLocation" name="uselocation">
                    <span class="sliderr"></span>
                </label>
            </div>


            <label for="outsourcing" style="display: flex; align-items: center;">
                Outsourcing
                <label class="switch" style="margin-left: 250px;">
                    <input type="checkbox" id="outsourcing" name="outsourcing" onchange="toggleOutsourcingDetails()">
                    <span class="slider"></span>
                </label>
            </label>

            <div id="outsourcingDetails" style="display: none; margin-top: 10px;">
                <label for="outsourcingFor">Outsourcing For</label>
                <select id="outsourcingFor" name="outsourcingFor">
                    <option >---</option>

                    <option >Photo</option>
                    <option >Video</option>
                    <option >Editor</option>
                    <option >Drone</option>
                    <option >Pilot</option>
                </select>

                <label for="outsourcingAmount" style="margin-top: 10px;">Outsourcing Amount</label>
                <input type="number" id="outsourcingAmount" name="outsourcingAmount">

                <label for="outsourcingCustomer" style="margin-top: 10px;">Outsourcing Customer Name</label>
                <select id="outsourcingCustomer" name="outsourcingCustomer">
                    <option value="">---</option>
                    <!-- dynamic clients display-->
                </select>

                <label for="outsourcingPaid" style="display: flex; align-items: center; margin-top: 10px;">
                    Outsourcing Paid
                    <label class="switch" style="margin-left: 250px;">
                        <input type="checkbox" id="outsourcingPaid" name="outsourcingPaid">
                        <span class="slider"></span>
                    </label>
                </label>
            </div>



            <label for="reference">Reference:</label>
            <input type="text" id="reference" name="reference">
            <div class="form-actions">
                <button type="submit">Submit</button>
                <button type="button" id="cancelBtn" onclick="closeEditModal()">Cancel</button>
            </div>
        </form>
    </div>
</div>

<script>
    function toggleOutsourcingDetails() {
        const outsourcingDetails = document.getElementById("outsourcingDetails");
        const toggle = document.getElementById("outsourcing");
        outsourcingDetails.style.display = toggle.checked ? "block" : "none";
    }
</script>


<script>// received amount

    document.addEventListener('DOMContentLoaded', function() {
        const stars = document.querySelectorAll('.star');
        stars.forEach(star => {
            star.addEventListener('click', function() {
                const value = this.getAttribute('data-value');
                stars.forEach(s => {
                    if (s.getAttribute('data-value') <= value) {
                        s.classList.add('filled');
                    } else {
                        s.classList.remove('filled');
                    }
                });
            });
        });

        // Get the modal
        var modal = document.getElementById("editModal");

        // Get the button that opens the modal
        var btn = document.querySelector(".edit-button");

        // Get the <span> element that closes the modal
        var span = document.getElementsByClassName("close")[0];

        // Get the cancel button
        var cancelBtn = document.getElementById("cancelBtn");

        // When the user clicks the button, open the modal
        btn.onclick = function() {
            modal.style.display = "block";
        }

        span.onclick = function() {
            modal.style.display = "none";
        }

        cancelBtn.onclick = function() {
            modal.style.display = "none";
        }

        window.onclick = function(event) {
            if (event.target == modal) {
                modal.style.display = "none";
            }
        }


        function updateReceivedBar() {
            // Retrieve and log the raw content
            const receivedAmountText = document.getElementById("receivedAmountDis").textContent;
            const projectAmountText = document.getElementById("projectAmountDis").textContent;

            console.log("Raw Received Amount Text:", receivedAmountText);
            console.log("Raw Project Amount Text:", projectAmountText);

            // Trim and parse the values
           // Remove non-numeric characters (like currency symbols or spaces)
            const receivedAmount = parseFloat(receivedAmountText.replace(/[^\d.-]/g, '').trim());
            const projectAmount = parseFloat(projectAmountText.replace(/[^\d.-]/g, '').trim());
            console.log("Parsed Received Amount:", receivedAmount);
            console.log("Parsed Project Amount:", projectAmount);

            const receivedBar = document.getElementById('receivedBar');

            if (!isNaN(receivedAmount) && !isNaN(projectAmount) && projectAmount > 0) {
                const percentage = (receivedAmount / projectAmount) * 100;
                receivedBar.style.width = percentage + '%';
                receivedBar.textContent = Math.round(percentage) + '%';
            } else {
                receivedBar.style.width = '0%';
                receivedBar.textContent = '0%';
            }
        }



        document.getElementById('editForm').onsubmit = function(event) {
            event.preventDefault();
            updateReceivedBar();
            modal.style.display = "none";
        };

        updateReceivedBar();
    });

</script>

<script> // edit form

        $(document).ready(function() {
            var modal = $('#editModal');
            var span = $('.close');
            var cancelBtn = $('#cancelBtn');

            // Set up CSRF token for AJAX requests
            $.ajaxSetup({
                beforeSend: function(xhr, settings) {
                    if (!this.crossDomain) {
                        xhr.setRequestHeader("X-CSRFToken", getCookie('csrftoken'));
                    }
                }
            });

            // Function to get CSRF token from cookies
            function getCookie(name) {
                var cookieValue = null;
                if (document.cookie && document.cookie !== '') {
                    var cookies = document.cookie.split(';');
                    for (var i = 0; i < cookies.length; i++) {
                        var cookie = cookies[i].trim();
                        if (cookie.substring(0, name.length + 1) === (name + '=')) {
                            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                            break;
                        }
                    }
                }
                return cookieValue;
            }

            // Close modal when user clicks on <span> (x) or cancel button
            span.on('click', function() {
                modal.hide();
            });

            cancelBtn.on('click', function() {
                modal.hide();
            });



            // Function to open modal and fetch data
            function openModal(code) {

                       // Function to convert date string to "datetime-local" format
            function convertToDatetimeLocal(dateStr) {
                const date = new Date(dateStr);
                const pad = (num) => (num < 10 ? '0' + num : num);

                const localDateTime = date.getFullYear() + '-' +
                                      pad(date.getMonth() + 1) + '-' +
                                      pad(date.getDate()) + 'T' +
                                      pad(date.getHours()) + ':' +
                                      pad(date.getMinutes());

                return localDateTime;
            }

                if (code) {
                    $.ajax({
                        url: '/get_model_data/' + code + '/',
                        method: 'GET',
                        success: function(data) {
                            $('#editForm').attr('data-edit-code', code); // Set the edit code
                            $('#projectType').val(data.projectType);
                            $('#projectStatus').val(data.projectStatus);
                            $('#projectName').val(data.projectName);
                            $('#customerCompany').val(data.customerCompany);
                            $('#shootStart').val(convertToDatetimeLocal(data.shootStart)); // Use correct variable name
                            $('#shootEnd').val(convertToDatetimeLocal(data.shootEnd)); // Also convert shootEnd
                            $('#projectAmount').val(data.projectAmount);
                            $('#projectLocation').val(data.projectLocation);
                            $('#outsourcing').prop('checked', data.outsourcing);

                            $('#useLocation').prop('checked', data.uselocation);

                            if (data.outsourcing) {
                                $('#outsourcingDetails').show(); // Show the outsourcing details if the checkbox is checked
                            } else {
                                $('#outsourcingDetails').hide(); // Hide the outsourcing details if not checked
                            }
                            $('#reference').val(data.reference);
                            $('#outsourcingFor').val(data.outfor).change();
                            $('#outsourcingAmount').val(data.outamt);
                            $('#outsourcingCustomer').val(data.outcus).change();
                            $('#outsourcingPaid').prop('checked', data.outpaid);

                            modal.show();
                        },
                        error: function() {
                            alert('Failed to fetch data. Please try again.');
                        }
                    });
                } else {
                    $('#editForm').removeAttr('data-edit-code'); // Clear the edit code for new projects
                    modal.show();
                }
            }

            // Attach click event to edit buttons
            $('.edit-button').on('click', function(event) {
                event.preventDefault(); // Prevent default link behavior
                var code = $(this).data('code'); // Get the project code from the button
                openModal(code);
            });

            // Handle form submission via AJAX
            $('#editForm').on('submit', function(event) {
                event.preventDefault();
                var code = $('#editForm').attr('data-edit-code'); // Get the edit code
                var url = '/edit_model/' + (code ? code + '/' : ''); // Ensure URL includes code if available


                $.ajax({
                    url: url,
                    method: 'POST',
                    data: $(this).serialize(),
                    success: function(response) {
                        if (response.success) {
                            alert('Form submitted successfully!');
                            modal.hide();
                            location.reload();  // Reload the page to reflect changes
                        } else {
                            alert('Failed to submit form: ' + response.error);
                        }
                    },
                    error: function() {
                        alert('An error occurred. Please try again.');
                    }
                });
            });
        });

    </script>

    <script>
        //type dynamic dropdown
        document.addEventListener('DOMContentLoaded', function() {
           var TypeDropdown = document.getElementById('projectType');

           // Fetch type when the page loads
           fetch('/get-unique-types/')
               .then(response => response.json())
               .then(data => {
                   data.forEach(function(type) {
                       var option = document.createElement('option');
                       option.value = type;
                       option.text = type;
                       TypeDropdown.appendChild(option);
                   });
               })
               .catch(error => console.error('Error fetching type:', error));
       });
   </script>

   <script>
       //client dynamic dropdown
       document.addEventListener('DOMContentLoaded', function() {
          var TypeDropdown = document.getElementById('outsourcingCustomer');

          // Fetch type when the page loads
          fetch('/get-unique-client/')
              .then(response => response.json())
              .then(data => {
                  data.forEach(function(client) {
                      var option = document.createElement('option');
                      option.value = client;
                      option.text = client;
                      TypeDropdown.appendChild(option);
                  });
              })
              .catch(error => console.error('Error fetching type:', error));
      });
   </script>

   <script>
    // page navigation from div
    function moveToExpensePage(exp_id) {
        window.location.href = "/expense/" + exp_id + "/";
    }
</script>

<script>
    // map latitude automation for edit modal
    document.getElementById('useLocation').addEventListener('change', function() {
        if (this.checked) {
            // Check if geolocation is available
            if (navigator.geolocation) {
                // Get current position
                navigator.geolocation.getCurrentPosition(function(position) {
                    const latitude = position.coords.latitude;
                    const longitude = position.coords.longitude;
                    // Fill in the coordinates in the Project Location field
                    document.getElementById('projectLocation').value = `${latitude}, ${longitude}`;
                }, function(error) {
                    alert('Error getting location: ' + error.message);
                });
            } else {
                alert("Geolocation is not supported by this browser.");
            }
        } else {
            // Clear the Project Location field if the toggle is turned off
            document.getElementById('projectLocation').value = '';
        }
    });
</script>

</body>
</html>