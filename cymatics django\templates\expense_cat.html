<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard inside</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #fafafa;
            margin: 0;
            display: flex;
        }
        .sidebar {
            width: 250px;
            background-color: #333;
            color: #fff;
            padding: 10px;
            position: fixed;
            height: 100%;
        }
        .sidebar h2 {
            color: #fff;
        }
        .sidebar a {
            display: block;
            color: #fff;
            padding: 8px;
            text-decoration: none;
        }
        .sidebar a:hover {
            background-color: #575757;
        }
        .container {
            margin-left: 270px;
            width: calc(100% - 270px);
            padding: 20px;
        }
      
       
        .category-container {
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: left;
            margin-bottom: 20px;
            font-size: medium;
            font-weight: 600;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table th, table td {
            text-align: left;
            padding: 12px;
            border-bottom: 1px solid #ddd;
        }
        table th {
            background-color: #f8f8f8;
            font-weight: bold;
        }
        table tr:hover {
            background-color: #f1f1f1;
        }
        .table-icon {
            display: flex;
            align-items: center;
        }
        .table-icon img {
            width: 30px;
            height: 30px;
            margin-right: 10px;
        }
        .amount {
            font-weight: bold;
            color: #333;
        }
        @media only screen and (max-width: 768px) {
            .container {
                width: 95%;
            }
        }
       
       

       
    .table-icon{
        cursor: pointer;
    }
    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }


.header h1 {
    font-size: 24px;
}

.search-add {
display: flex;
align-items: center;
justify-content: flex-end;  /* Align everything to the right */
width: 100%;
}

.search-add input {
padding: 10px;
border: 1px solid #ccc;
border-radius: 5px;
margin-right: 10px; /* Space between search bar and filter button */
width: 200px;       /* Adjust width of the search bar */
}
/* Filter Button */
.filter-btn {
    background-color: rgb(248, 248, 248);
    color: rgb(92, 90, 90);
    border: 1px solid #ccc;
    padding: 8px 16px;
    margin-left: 10px;  /* Adjust spacing between filter and new button */
    border-radius: 4px;
    cursor: pointer;
    position: relative;
    padding-left: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 130px;
    font-size: medium;
    font-style: normal;
}

.filter-btn .filter-text {
    flex-grow: 1;
    margin-left: 10px;
}

.filter-btn .dropdown-icon {
    margin-left: 10px; /* Adjust icon spacing */
    width: 15px;
    height: 15px;
    margin-top: 3px;
}
       
.dropdown-section .side-arrow {
    width: 20px;
    height: 12px;
    flex-shrink: 0;
    transition: transform 0.3s ease;
}


.filter-dropdown {
    background-color: white;
    border: 1px solid #ddd;
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    position: absolute;
    top: 80px; /* Position below the filter button */
    right: 20px;
    width: 280px;
    padding: 10px;
    display: none; /* Initially hidden */
    z-index: 10;
}



         
.dropdown-section {
   
    justify-content:space-between;
    align-items: center;
    cursor: pointer;
    position: relative;  
    padding: 8px 0;
}

        .filter-dropdown .dropdown-section:hover{
            background-color: #f1f1f1;
            display:block;
        }
        .dropdown-section label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    font-weight: bold;
    cursor: pointer;
    padding: 0 16px; 
    box-sizing: border-box; 
}
.dropdown-section label span {
    flex-grow: 1;
}

        .dropdown-section input[type="checkbox"] {
            width: 20px; 
            height: 20px; 
            margin-right: 10px; 

        }

      
        .submenu {
    display: none; 
    padding: 10px 16px; 
    color: #000000;
}

.submenu input[type="checkbox"] {
    width: 20px;
    height: 20px;
    margin-right: 10px;
}

.submenu.show {
    display:block;
}

        .clear-btn {
            padding: 5px;
            padding-right: 40px;
            padding-left: 40px;
            margin-top: 10px;
            background-color: #ffffff;
            color: rgb(0, 0, 0);
            border: 1px solid #888;
            border-radius: 4px;
            cursor: pointer;
        }

        .done-btn {
            padding: 5px;
            padding-right: 40px;
            padding-left: 40px;
            margin-top: 10px;
            background-color: #000000;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .clear-btn:hover, .done-btn:hover {
            background-color: #555;
        }

        .filter-btn img {
            width: 20px;
            height: 20px;
            vertical-align: middle;
            margin-right: 6px;
            margin-top: -2px;
        }
      
        .toggle-status img {
            width: 15px;
            height: 10px;
            vertical-align: middle;
           
            margin-right: 20px;
            margin-top: -2px;
        }

        .arrow {
            background-color: #f1f1f1;
            border: none;
            padding: 10px;
            cursor: pointer;
            }

            .pagination {
                display: flex;
                justify-content: center;
                align-items: center;
                margin: 20px;
                }

    </style>
</head>
<body>

    <div class="sidebar">
        <h2>Menu</h2>
        <a href="#dashboard">Dashboard</a>
        <a href="#project">Project</a>
        <a href="#income">Income</a>
        <a href="#expense">Expense</a>
        <a href="#calendar">Calendar</a>
        <a href="#clients">Clients</a>
        <a href="#status">Status</a>
        <a href="#pending-payments">Pending Payments</a>
        <a href="#map">Map</a>
        <a href="#assets">Assets</a>
        <a href="#budget">Budget</a>
        <a href="#entertainment">Entertainment</a>
        <a href="#others">Others</a>
        <a href="#cymatics">Cymatics</a>
        <a href="#gadgets">Gadgets</a>
        <a href="#loan-repayment">Loan Repayment</a>
    </div>

    <div class="container">
        <main class="main-content">
            <header class="header">
                <div class="search-add">

             <!-- search box -->
    <form method="get" action="{% url 't_exp_in' %}">
        {% csrf_token %}
        <input type="text" name="q" class="search-bar" placeholder="Search" value="{{ query }}"></div>

        
        <!-- filter box -->        
        <button id="filter-btn" onclick="toggleFilterDropdown(event)" class="filter-btn">
            <span class="filter-text">Filter</span>
            <img src="dropdown-arrow.png" alt="Filter Icon" class="dropdown-icon">
        </button> 


        <div class="filter-dropdown" id="filterDropdown">
            <div class="dropdown-section">
                <label class="toggle-status">
                    <span>Category Sum</span>
                    <img src="side-arrow.png" alt="Toggle Submenu" class="side-arrow">
                </label>
                <div class="submenu" id="category-dropdown">
                    <!--Dynamically updated-->
                </div>
            </div>

             <!-- hidden fields for filters-->
        <input type="hidden" name="category" id="category" value="{{ category }}">

            <div class="filter-actions">
                <button class="clear-btn" onclick="clearFilters()">Clear All</button>
                <button class="done-btn" onclick="toggleFilterDropdown()">Done</button>
            </div>

       

    </form>
</header>
<!-- Category Tables -->
{% for cat, exps in cate.items %}
<div class="category-container">
    <h1>{{ cat }}</h1>
    <table id="data-table">
        <thead>
            <tr>
                <th>Category</th>
                <th>Details</th>
                <th>Amount</th>
            </tr>
        </thead>
        <tbody>
            {% for exp in exps.items %}
            <tr onclick="moveToDetailPage('{{ exp.id }}')" style="cursor: pointer;">
                <td class="table-icon">
                    <img src="fuel-icon.png" alt="Fuel Icon">
                    {{ exp.category }}
                </td>
                <td>{{ exp.description }}</td>
                <td class="amount">₹{{ exp.amount }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    
    
</div>


<!-- Pagination for each category -->
<div class="pagination">
    <button id="prev" class="arrow" {% if not exps.has_previous %}disabled{% endif %} 
            onclick="window.location.href='?page={{ exps.previous_page_number }}&category={{ category }}&q={{ query }}'">←</button>
    <span id="page-info">
         {{ exps.page_number }}
    </span>
    <button id="next" class="arrow" {% if not exps.has_next %}disabled{% endif %}
            onclick="window.location.href='?page={{ exps.next_page_number }}&category={{ category }}&q={{ query }}'">→</button>
</div>

{% endfor %}

<!--
<script>
    $(document).ready(function() {
        // Handle pagination click for each category
        $(document).on('click', '.pagination button', function(e) {
            e.preventDefault();
    
            var page = $(this).data('page');
            var category = $(this).closest('.pagination').data('category');
            var query = "{{ query }}";  // Assuming query is already available in the template context
    
            if (page) {
                $.ajax({
                    url: window.location.pathname,
                    type: 'GET',
                    data: {
                        'page': page,
                        'category': category,
                        'q': query,
                    },
                    dataType: 'json',
                    success: function(data) {
                        // Update expense table and pagination info for the specific category
                        var expenseList = $('#expense-list-' + category);
                        expenseList.empty();  // Clear the current table
    
                        $.each(data.expenses[category].items, function(index, exp) {
                            expenseList.append(`
                                <tr onclick="moveToDetailPage('${exp.id}')" style="cursor: pointer;">
                                    <td class="table-icon">
                                        <img src="fuel-icon.png" alt="Fuel Icon">
                                        ${exp.category}
                                    </td>
                                    <td>${exp.description}</td>
                                    <td class="amount">₹${exp.amount}</td>
                                </tr>
                            `);
                        });
    
                        // Update pagination info
                        var paginationContainer = $('.pagination[data-category="' + category + '"]');
                        paginationContainer.find('.page-info').text(
                            `Page ${data.expenses[category].page_number} of ${data.expenses[category].num_pages}`
                        );
                        paginationContainer.find('.prev').data('page', data.expenses[category].previous_page_number)
                            .prop('disabled', !data.expenses[category].has_previous);
                        paginationContainer.find('.next').data('page', data.expenses[category].next_page_number)
                            .prop('disabled', !data.expenses[category].has_next);
                    },
                    error: function(xhr, status, error) {
                        console.error('Error loading data:', error);
                    }
                });
            }
        });
    });
    
</script>
-->

<!--
<script> // when there is no need of pagination , we need to hide it...
    $(document).ready(function() {
        $('#prev, #next').click(function (e) {
            e.preventDefault();
            var page = $(this).data('page');
            if (page) {
                loadPage(page);
            } else {
                console.warn('No page number available for', this.id, 'button');
            }
        });
    
        // Load initial page
        loadPage(1);
    
        // Hide pagination if both buttons are disabled
        function togglePaginationVisibility() {
            if ($('#prev').is(':disabled') && $('#next').is(':disabled')) {
                $('.pagination').hide();
            } else {
                $('.pagination').show();
            }
        }
    
        // Check the visibility when the page is loaded
        togglePaginationVisibility();
    });
</script>
-->

    <script> // dropdown

        document.querySelector('input[name="q"]').addEventListener('keypress', function(event) {
                   if (event.key === 'Enter') {
                       event.preventDefault(); 
                       this.form.submit(); 
                   }
               });
   
    
               function toggleFilterDropdown(event) {
                   event.preventDefault(); 
                   const filterDropdown = document.getElementById('filterDropdown');
                   filterDropdown.style.display = filterDropdown.style.display === 'block' ? 'none' : 'block';
               }
   
   document.addEventListener('click', function(event) {
       const filterDropdown = document.getElementById('filterDropdown');
       const filterBtn = document.getElementById('filter-btn');
       
      
       if (!filterDropdown.contains(event.target) && !filterBtn.contains(event.target)) {
                event.stopPropagation();
       }
   });
   
               // Ensure clicking inside the dropdown does not close it
   document.querySelectorAll('.dropdown-section').forEach(section => {
       section.addEventListener('click', function(event) {
           event.stopPropagation(); // Prevent the dropdown from closing when interacting with it
       });
   });
   
               // Ensure clicking inside the dropdown does not close it
               const dropdownSections = document.querySelectorAll('.dropdown-section');
               dropdownSections.forEach(section => {
                   section.addEventListener('click', function(event) {
                       event.stopPropagation(); 
                   });
               });
   
              
   // JavaScript to clear filters
   function clearFilters() {
    document.getElementById('category').value ='';

    document.forms[0].submit();
}
   
   document.querySelector('.done-btn').addEventListener('click', function() {
           filterDropdown.style.display = 'none'; 
       });
   
   // Ensure clear button closes the filter dropdown
   document.querySelector('.clear-btn').addEventListener('click', function(event) {
       event.preventDefault();
       clearFilters(); 
   });
   
              
               dropdownSections.forEach(section => {
                   section.addEventListener('click', function() {
                       const submenu = this.querySelector('.submenu');
                       submenu.style.display = submenu.style.display === 'block' ? 'none' : 'block';
                   });
               });
           
   // Functions to populate filters dynamically (no changes to these)

   
   </script>

   <script>
    // page navigation from div
    function moveToDetailPage(exp_id) {
        window.location.href = "/expense/" + exp_id + "/";
    }
</script>

<script>

    function setFilter(field, value) {
        const fieldElement = document.getElementById(field);
        if (fieldElement) {
            const currentValues = fieldElement.value ? fieldElement.value.split(',') : [];
            if (currentValues.includes(value)) {
                // Remove the value if it's already checked (uncheck scenario)
                fieldElement.value = currentValues.filter(item => item !== value).join(',');
            } else {
                // Add the value if it's checked
                currentValues.push(value);
                fieldElement.value = currentValues.join(',');
            }
        }
    }

     // Fetch unique companies from the server and populate the dropdown
function fetchUniqueCategory() {
    fetch('/get-unique-category/')
        .then(response => response.json())
        .then(data => {
            const dropdown = document.getElementById('category-dropdown');
            dropdown.innerHTML = '';  // Clear existing content

            const selectedCategories = document.getElementById('category').value.split(',');

            // Loop through the companies and create checkboxes
            data.forEach(category => {
                const label = document.createElement('label');
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.setAttribute('onchange', `setFilter('category', '${category}')`);

                // Check if the current company is selected and mark the checkbox as checked
                if (selectedCategories.includes(category)) {
                    checkbox.checked = true;
                }

                label.appendChild(checkbox);
                label.append(category);
                dropdown.appendChild(label);
                dropdown.appendChild(document.createElement('br'));
            });
        });
}

window.onload = function(){
    fetchUniqueCategory();
}

</script>

    
</body>
</html>