{% load static %}
{% static "images" as baseurl %}
<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Income Dashboard</title>
    <!--<link rel="stylesheet" href="{% static './css/income.css' %}">-->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.5.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" type="text/css" href="https://npmcdn.com/flatpickr/dist/themes/dark.css">
</head>
<style>

    .user-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background-color: #ddd;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        font-size: 18px;
        color: #0e0e0e;
        background-color: #e1ecb8;

    }

    .modal {
        display: none;
        position: fixed;
        z-index: 1;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0,0,0,0.4);
    }


.modal-content {
background-color: #ffffff;
margin: 5% auto; /* Adjusted for more space at the top */
padding: 20px;
border: 1px solid #888;
width: 400px;
border-radius: 8px;
box-sizing: border-box; /* Ensures padding and border are included in width and height */
max-height: calc(100vh - 10%); /* Prevents overflow by limiting height */
display: flex; /* Use flexbox for layout */
flex-direction: column; /* Stack children vertically */
}

.modal-content form {
flex: 1; /* Allow the form to grow and take available space */
display: flex;
flex-direction: column;
overflow: hidden; /* Prevent overflow of form elements */
}



.submit-btn,
.cancel-btn {
 flex: 1;
 padding: 10px;
 border: none;
 border-radius: 5px;
 cursor: pointer;
 margin: 0 5px;


}


.modal-content .close {
color: #aaa;
float: right;
font-size: 28px;
font-weight: bold;
}

.close:hover,
.close:focus {
color: black;
text-decoration: none;
cursor: pointer;
}

.modal-content h3 {
text-align: center;
font-size: 20px; /* Adjust as needed */
margin-bottom: 20px;
}


.modal-content form label {
margin-bottom: 10px;
}

.modal-content form input,
.modal-content form button {
padding: 10px;
margin-bottom: 10px;
border: 1px solid #ddd;
border-radius: 5px;
}


.modal .form-actions button[type="submit"] {
background-color: rgb(0, 0, 0);
color: white;
}

.form-actions .cancel-btn{
    background-color: #ffffff;
    color: black;
    border: 1px solid #888 !important;
}

.search-bar button[type="button"] {
background-color: #000000;
color:#ffffff;
}


#projectIncomeWrapper {
display: flex;
align-items: center;
justify-content: space-between;
margin-top: 10px;
}

#projectIncomeWrapper label {
margin: 0;
font-weight: medium;
}


#addForm button, #editForm button {
        padding: 10px;
        margin-top: 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        gap:0px;
    }

    #addForm input, #addForm select,
    #editForm input, #editForm select {
        padding: 8px;
        margin-top: 5px;
        border: 1px solid #ccc;
        border-radius: 4px;
    }
 #addForm button:hover,  #editForm button:hover {
        background-color: #555;
    }
    #editForm, #addForm {
        display: flex;
        flex-direction: column;
    }

    #editForm label, #addForm label {
        margin-top: 10px;
    }

    /* Outsourcing Section Styles */
    #outsourcingDetails {
        margin-top: 10px;
    }

    #outsourcingDetails label {
        display: block;
        margin-top: 10px;
        font-weight: medium;
    }

    #outsourcingDetails select,
    #outsourcingDetails input[type="number"] {
        width: 100%;
        padding: 8px;
        margin-top: 5px;
        border: 1px solid #ccc;
        border-radius: 4px;
    }

    #addOutsourcingDetails,#editOutsourcingDetails {
        margin-top: 10px;
    }

    #addOutsourcingDetails label,
    #editOutsourcingDetails label {
        display: block;
        margin-top: 10px;
        font-weight: medium;
    }

    #addOutsourcingDetails select,
    #addOutsourcingDetails input[type="number"],
    #editOutsourcingDetails select,
    #editOutsourcingDetails input[type="number"] {
        width: 100%;
        padding: 8px;
        margin-top: 5px;
        border: 1px solid #ccc;
        border-radius: 4px;
    }

    .switch {
        position: relative;
        display: inline-block;
        width: 34px;
        height: 20px;
    }

    .switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 20px;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 16px;
        width: 16px;
        left: 2px;
        bottom: 2px;
        background-color: white;
        border-radius: 50%;
        transition: .4s;
    }

    input:checked + .slider {
        background-color: #000000;
    }

    input:checked + .slider:before {
        transform: translateX(14px);
    }

.search-bar {
display: flex;
align-items: center;
}

.search-bar input {
padding: 15px 20px;  /* Adjust padding for a larger input box */
border: 1px solid #a1a1a1;
border-radius: 5px;
margin-right: 10px;
font-size: 18px;  /* Increase font size */
width: 300px;  /* Increase the width of the input */
}



.search-bar button:hover {
background-color: #717070;
}



/* Adjust the upper-bar to move up and extend left */
.upper-bar {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: 20px;
background-color: #1e1e1e;
border-bottom: 1px solid #ddd;
padding-bottom: 20px;
padding-top: 40px;
color: #cacaca;
gap: 20px;
position: relative;
top: -20px; /* Move it upwards */
left: -20px; /* Shift it to the left */
right:-20px;
width: calc(100% + 41px); /* Extend it back to cover full width */
}

.upper-bar .total-income {
display: flex;
align-items: center;      /* Center both spans vertically */
width: 100%;              /* Take full width of the upper bar */
gap: 120px;                /* Add a small gap between the spans */
}

.upper-bar .total-income span:first-child {
font-weight: bold;
color: #e9e7e7;
justify-content: left;
padding-left:20px ;
font-size: 18px;
font-family:Arial, Helvetica, sans-serif;
}

.upper-bar .total-income span:last-child {
font-size: 18px;          /* Reduce the font size slightly */
color: rgb(236, 236, 236);
text-align: left;       /* Center the text in the second span */
justify-content:center;
font-family:Arial, Helvetica, sans-serif;
}

.add-income {
margin-bottom: 20px;
display: flex;
justify-content: center;
margin-top: -20px;  /* Move the button slightly upwards */
}

.add-income-btn {
padding: 1px 250px;  /* Decrease height, increase width */
background-color: #e7e4e4;
border: none;
border-radius: 10px;
cursor: pointer;
font-size: 14px;  /* Decrease font size for "Add Income" */
display: flex;
flex-direction: column;
align-items: center;
justify-content: center;
line-height: 1.2; /* Adjust line height for better spacing */
position: relative;  /* Ensure correct positioning */
}

.income-btn {
padding: 1px 250px;
background-color: #000000;
border: none;
color:#fff;
border-radius: 10px;
cursor: pointer;
font-size: 12px;  /* Decrease font size for "Add Income" */
display: flex;
align-items: center;
justify-content: center;
line-height: 1.2; /* Adjust line height for better spacing */
position: relative;  /* Ensure correct positioning */
}
.income-btn:hover {
background-color: #d0d0d0;
}

/* Increase size of the "+" */
.add-income-btn::before {
content: "+";  /* Display the "+" */
font-size: 35px;  /* Increase the size of the "+" */
display: block;
margin-bottom: -8px;  /* Reduce gap between + and text */
}

.add-income-btn:hover {
background-color: #d0d0d0;
}


.footer {
    display: flex;
    justify-content: center;
    padding: 20px;
    background-color: white;
    margin-top: auto;
    border-top: 1px solid #ddd;
   }

   .footer .btn {
    padding: 8px 250px;
    background-color: #000000;
    color: white;
    border:None;
    width: 100px;
    border-radius: 10px;
    cursor: pointer;
    font-size: 26px;

   }

.modal-content h2 {
text-align: center;
margin-bottom: 20px;
font-size:19px;
}
.action-btn {
   background-color:transparent; /* Remove the red background */
   border: none; /* Remove the border */
   font-size: 28px;
   color: black;
   width: auto; /* Adjust width to fit the content */
   height: auto; /* Adjust height to fit the content */
   display: flex;
   align-items: center;
   justify-content: center;
   cursor: pointer;
   line-height: 0;
   margin-left: 10px;
}
  .income-btn .add{
    color:#fff;
    margin:1px;
    }
    body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 0;
        display: flex;
        height: 100vh;
        flex-direction: column;
    }
    .container {
        display: flex;
        width: 100%;
        flex-grow: 1;
    }
        .sidebar {
          background-color: #1e1e1e;
          color: white;
          width: 250px;
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: center;
          transition: width 0.3s;
          position: relative;
      }
      .sidebar.closed {
        width: 60px;
    }

    /* Icon visibility and border */
    .sidebar .toggle-icon {
        position: absolute;
        top: 25px !important; /* Aligned near the top */
        right: -8px; /* Adjusted to be right on the edge line */
        cursor: pointer;
        visibility: hidden;
        border: 3px solid rgba(78, 27, 231, 0.5); /* Light border */
        border-radius: 8px;
        padding: 1px;
        transition: visibility 0.3s ease-in-out, top 0.3s ease-in-out; /* Smooth transitions */
        z-index: 2;
    }
    #toggle-icon {
        width: 20px;
        height: 20px;
    }


    /* Adjust position for closed state to avoid overlap */
    .sidebar.closed .toggle-icon {
        top: 10px;
        right: -8px; /* Keep it on the edge even when closed */
    }

    /* Show icon when hovering near the sidebar or over the icon */
    .sidebar:hover .toggle-icon, .toggle-icon:hover {
        visibility: visible;
    }

    .sidebar .logo {
        padding: 20px;
        text-align: center;
    }

    .sidebar.closed .logo {
        display: none;
    }

    .sidebar nav ul {
        list-style: none;
        padding: 0;
        width: 100%;
        text-align: center;
    }

    .sidebar nav ul li {
        padding: 12px 20px;
        cursor: pointer;
        transition: background-color 0.3s, border-left 0.3s;
        display: flex;
        justify-content: flex-start;
        align-items: center;
    }

    .sidebar.closed nav ul li {
        justify-content: center;
    }

    .sidebar nav ul li a {
        display: flex;
        align-items: center;
        text-decoration: none;
        color: white;
        width: 100%;
        font-family: Arial, sans-serif;
    }

    .sidebar nav ul li a:hover {
        background-color: #555;
        border-left: 4px solid #ffcc00;
    }

    .menu-icon {
        margin-right: 10px;
        width: 24px;
        height: 24px;
    }

    .menu-text {
        transition: opacity 0.3s, visibility 0.3s;
        font-family: Arial, sans-serif;
    }

    .sidebar.closed .menu-text {
        display: none;
    }

    .sidebar.closed nav ul li:hover {
        background-color: inherit;
    }

    .profile-section {
        position: relative; /* Allows positioning of the dropdown */
        padding: 12px 20px; /* Match padding with other menu items */
        cursor: pointer; /* Change cursor on hover */
        transition: background-color 0.3s, border-left 0.3s; /* Smooth transition */
    }

    .profile-section:hover {
        background-color: #555; /* Background color on hover */
        border-left: 4px solid #ffcc00; /* Left border on hover */
    }

    .dropdown {
        position: absolute; /* Position relative to the profile section */
        bottom: 100%; /* Position above the profile section */
        left: 0; /* Align to the left */
        background-color: white; /* Background color of the dropdown */
        border: 1px solid #ccc; /* Border for the dropdown */
        border-radius: 4px; /* Rounded corners */
        z-index: 1000; /* Ensure it appears above other elements */
        width: 160px; /* Set width for the dropdown */
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); /* Shadow for a floating effect */
        display: none; /* Initially hidden */
    }

    .dropdown ul {
        list-style: none; /* Remove default list styles */
        padding: 0; /* Remove padding */
        margin: 0; /* Remove margin */
    }

    .dropdown li {
        padding: 10px; /* Padding for each item */
        color: black; /* Set text color to black */
        cursor: pointer; /* Change cursor on hover */
    }

    .dropdown li:hover {
        background-color: #f1f1f1; /* Background on hover */
    }

    .main-content {
      flex-grow: 1;
      background-color: #f1f1f1;
      padding: 20px;
      position: relative; /* Required for positioning the form */
    }


/* Adjust the upper-bar to move up and extend left */
.upper-bar {
 display: flex;
 justify-content: space-between;
 align-items: center;
 margin-bottom: 20px;
 background-color: #1e1e1e;
 border-bottom: 1px solid #ddd;
 padding-bottom: 20px;
 padding-top: 40px;
 color: #cacaca;
 gap: 20px;
 position: relative;
 top: -20px; /* Move it upwards */
 left: -20px; /* Shift it to the left */
 right:-20px;
 width: calc(100% + 41px); /* Extend it back to cover full width */
}

.upper-bar .total-income {
 display: flex;
 align-items: center;      /* Center both spans vertically */
 width: 100%;              /* Take full width of the upper bar */
 gap: 120px;                /* Add a small gap between the spans */
}

.upper-bar .total-income span:first-child {
 font-weight: bold;
 color: #e9e7e7;
 justify-content: left;
 padding-left:20px ;
 font-size: 18px;
 font-family:Arial, Helvetica, sans-serif;
}

.upper-bar .total-income span:last-child {
 font-size: 18px;          /* Reduce the font size slightly */
 color: rgb(236, 236, 236);
 text-align: left;       /* Center the text in the second span */
 justify-content:center;
 font-family:Arial, Helvetica, sans-serif;
}

    .chart-section {
        margin-bottom: 30px;
    }

    .add-income {
        margin-bottom: 20px;
        display: flex;
        justify-content: center;
        margin-top: -20px;  /* Move the button slightly upwards */
       }

       .add-income-btn {
        padding: 1px 250px;  /* Decrease height, increase width */
        background-color: #e7e4e4;
        border: none;
        border-radius: 10px;
        cursor: pointer;
        font-size: 14px;  /* Decrease font size for "Add Income" */
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        line-height: 1.2; /* Adjust line height for better spacing */
        position: relative;  /* Ensure correct positioning */
       }

       .income-btn {
        padding: 1px 250px;
        background-color: #000000;
        border: none;
        color:#fff;
        border-radius: 10px;
        cursor: pointer;
        font-size: 12px;  /* Decrease font size for "Add Income" */
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 1.2; /* Adjust line height for better spacing */
        position: relative;  /* Ensure correct positioning */
       }
       .income-btn:hover {
        background-color: #d0d0d0;
       }

       /* Increase size of the "+" */
       .add-income-btn::before {
        content: "+";  /* Display the "+" */
        font-size: 35px;  /* Increase the size of the "+" */
        display: block;
        margin-bottom: -8px;  /* Reduce gap between + and text */
       }

       .add-income-btn:hover {
        background-color: #d0d0d0;
       }


    .header {
        margin-bottom: 20px;
        display: flex;
        justify-content: flex-end;
    }

    .header form {
        display: flex;
        align-items: center;
    }

    .header form input[type="text"] {
        padding: 5px;
        border-radius: 5px;
        border: 1px solid #ddd;
        margin-right: 10px;
    }

    .header form button {
        padding: 5px 10px;
        background-color: #333;
        color: #fff;
        border: none;
        border-radius: 5px;
        cursor: pointer;
    }

    .income-table {
        flex-grow: 1;
        overflow-y: auto;
    }

    .income-table table {
        width: 100%;
        border-collapse: collapse;
    }

    .income-table table th,
    .income-table table td {
        padding: 10px;
        text-align: left;
        border-bottom: 1px solid #ddd;
    }

    .income-table table th {
        background-color: #f4f4f4;
    }

    .action-btn {
        background-color:transparent; /* Remove the red background */
        border: none; /* Remove the border */
        font-size: 28px;
        color: black;
        width: auto; /* Adjust width to fit the content */
        height: auto; /* Adjust height to fit the content */
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        line-height: 0;
        margin-left: 10px;
    }



    .modal {
        display: none;
        position: fixed;
        z-index: 1;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgb(0, 0, 0);
        background-color: rgba(0, 0, 0, 0.4);
        padding-top: 60px;
    }

    .modal-content {
        background-color: #fff;
        margin: 5% auto;
        padding: 20px;
        border: 1px solid #ddd;
        width: 80%;
        max-width: 500px;
        border-radius: 10px;
        position: relative;
    }

    .modal-content .close {
        position: absolute;
        top: 10px;
        right: 10px;
        color: #aaa;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
    }

    .modal-content .close:hover,
    .modal-content .close:focus {
        color: #000;
        text-decoration: none;
        cursor: pointer;
    }

    .modal-content form {
        display: flex;
        flex-direction: column;
    }

    .modal-content form label {
        margin-bottom: 10px;
    }

    .modal-content form input,
    .modal-content form button {
        padding: 10px;
        margin-bottom: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
    }

    .modal-content form .form-actions {
        display: flex;
        justify-content: space-between;
    }

    .modal-content form .form-actions button {
        padding: 10px 20px;
    }
    .form-actions button {
        margin-left: 10px;
      }
      form button[type="submit"],
      form button[type="button"] {
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        align-items : left;
      }

      form button[type="submit"] {
        background-color: black;
        color: white;
      }

      form button[type="button"] {
        background-color: #ffffff;
        color: rgb(0, 0, 0);
        border: 1px solid #888;
      }





</style>
<body>
    <div class="container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="toggle-icon">
                <img src="{% static './images/menuleft.png' %}" alt="icon" id="toggle-icon" width="16" height="16">
            </div>
            <div class="logo">
                <img src="{% static './images/logowhite.png' %}" alt="logo" width="50" height="50">
            </div>
            <nav>
                <ul>
                    <li class="menu-item"><a href="{% url 'dashboard' %}"><img src="{% static 'images/dashboard.png' %}" class="menu-icon"><span class="menu-text">Dashboard</span></a></li>
                    <li class="menu-item"><a href="{% url 'projects' %}"><img src="{% static 'images/project.png' %}" class="menu-icon"><span class="menu-text">Project</span></a></li>
                    <li class="menu-item active"><a href="{% url 'incomef_view' %}"><img src="{% static 'images/income.png' %}" class="menu-icon"><span class="menu-text">Income</span></a></li>
                    <li class="menu-item"><a href="{% url 'expense' %}"><img src="{% static 'images/expenses.png' %}" class="menu-icon"><span class="menu-text">Expense</span></a></li>
                    <li class="menu-item"><a href="{% url 'calendar' %}"><img src="{% static 'images/calendar.png' %}" class="menu-icon"><span class="menu-text">Calendar</span></a></li>
                    <li class="menu-item"><a href="{% url 'allproject' %}"><img src="{% static 'images/All projects.png' %}" class="menu-icon"><span class="menu-text">All Projects</span></a></li>
                    <li class="menu-item"><a href="{% url 'clientsbook' %}"><img src="{% static 'images/clientsbook.png' %}" class="menu-icon"><span class="menu-text">Clients Book</span></a></li>
                    <li class="menu-item"><a href="{% url 'clients' %}"><img src="{% static 'images/Clients.png' %}" class="menu-icon"><span class="menu-text">Clients</span></a></li>
                    <li class="menu-item"><a href="{% url 'status' %}"><img src="{% static 'images/status.png' %}" class="menu-icon"><span class="menu-text">Status</span></a></li>
                    <li class="menu-item"><a href="{% url 'pending_pay' %}"><img src="{% static 'images/pending.png' %}" class="menu-icon"><span class="menu-text">Pending Payments</span></a></li>
                    <li class="menu-item"><a href="{% url 'project_map' %}"><img src="{% static 'images/map.png' %}" class="menu-icon"><span class="menu-text">Map</span></a></li>
                    <li class="menu-item"><a href="{% url 'assets' %}"><img src="{% static 'images/Assets.png' %}" class="menu-icon"><span class="menu-text">Assets</span></a></li>
                    <li class="menu-item"><a href="{% url 'budget' %}"><img src="{% static 'images/budget.png' %}" class="menu-icon"><span class="menu-text">Budget</span></a></li>
                    <li class="menu-item"><a href="{% url 'entertainment' %}"><img src="{% static 'images/Entertainment.png' %}" class="menu-icon"><span class="menu-text">Entertainment</span></a></li>
                </ul>
            </nav>
            <div class="profile-section" id="profileMenu">
                <div class="user-icon" id="userIcon">
                  <!-- Default content in case JS is not available -->
                  U
              </div>

                <span class="menu-text" id="name">{{ user.username }}</span>
                <div class="dropdown" id="profileDropdown">
                    <ul>
                        <li><a href="{% url 'profile' %}">View Profile</a></li>
                        <li><a href="{% url 'logout_view' %}">Sign Out</a></li>
                    </ul>
                </div>
              </div>
        </aside>


        <!-- Main Content -->
        <main class="main-content">
            <!-- Upper Menu Bar -->
            <header class="upper-bar">
                <div class="total-income">
                    <span>Total Income</span>
                    <span>{{ total }}</span>
                </div>
            </header>

            <!-- Monthly Project Values Chart -->
             <!-- Add Income Button -->
             <section class="add-income">
                <button class="add-income-btn" onclick="openForm()"> Add Income</button>
            </section>


        <!-- Monthly Project Values Chart -->
        <section class="chart-section">
            <canvas id="projectChart" width="800" height="300"></canvas>
        </section>


            <!-- Search Box -->
            <header class="header">
                <form method="get" action="{% url 'incomef_view' %}">
                    {% csrf_token %}
                    <div class="search-bar">
                        <input type="text" name="q" placeholder="Search" value="{{ query }}">

                    <button type="button" class="income-btn" onclick="openForm()">
                        <i class="fa-solid fa-plus add"></i> <b>Add</b></button>
                    </div>
                </form>
            </header>

            <!-- Income Table -->
            <section class="income-table">
                <table>
                    <thead>
                        <tr>
                            <th>Amount</th>
                            <th>Date</th>
                            <th>Description</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in objs %}
                        <tr onclick="moveToDetailPage('{{ item.id }}')" style="cursor: pointer;">
                            <td>{{ item.amount }}</td>
                            <td>{{ item.date }}</td>
                            <td>{{ item.description }}</td>
                            <td><button class="action-btn" onclick="openEditForm()" data-id="{{ item.id }}">
                                <i class="bi bi-three-dots"></i>
                            </button></td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </section>

            <!-- Footer with Add Button -->
            <footer class="footer">
                <button class="btn" onclick="openForm()"> <i class="fa-solid fa-plus add"></i></button>
            </footer>
        </main>
    </div>

    <!-- Add Item Form -->
<div id="addModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeForm()">&times;</span>
        <h2>Add Item</h2>
        <form id="addForm" method="post" action="{% url 'incomef_view' %}">
            {% csrf_token %}
            <label for="date">Date</label>
            <input type="date" id="date" name="date" required>

            <label for="description">Description</label>
            <input type="text" id="description" name="description" required>

            <label for="amount">Amount</label>
            <input type="number" id="amount" name="amount" required>

            <label for="note">Note</label>
            <input type="text" id="note" name="note">

            <!-- Wrap the label and switch in a div -->
            <div id="projectIncomeWrapper">
                <label for="addOutsourcing">Project Income</label>
                <label class="switch">
                    <input type="checkbox" id="addOutsourcing" name="outsourcing" onchange="toggleAddOutsourcingDetails()">
                    <span class="slider"></span>
                </label>
            </div>

            <div id="addOutsourcingDetails" style="display: none; margin-top: 10px; align-items: center;">
                <label for="projectCode" style="font-size:small;">Project Code</label>
                <select id="projectCode" name="projectCode">
                    <option value="">Select Project Code</option>
                    <!-- Options will be dynamically populated -->
                </select>
            </div>
            <script>
                function toggleAddOutsourcingDetails() {
                    const outsourcingDetails = document.getElementById("addOutsourcingDetails");
                    const toggle = document.getElementById("addOutsourcing");
                    outsourcingDetails.style.display = toggle.checked ? "block" : "none";
                }
            </script>

            <div class="form-actions">
                <button id="addBtn" class="submit-btn"type="submit">Submit</button>
                <button type="button" class="cancel-btn"onclick="closeForm()">Cancel</button>
            </div>
        </form>
    </div>
</div>


    <!-- Edit Item Form -->
<div id="editModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeForm()">&times;</span>
        <h2>Edit</h2>
        <form id="editForm" method="post">
            {% csrf_token %}
            <label for="date">Date</label>
            <input type="date" id="edit-date" name="date" required>

            <label for="description">Description</label>
            <input type="text" id="edit-description" name="description" required>

            <label for="amount">Amount</label>
            <input type="number" id="edit-amount" name="amount" required>

            <label for="note">Note</label>
            <input type="text" id="editnote" name="editnote">

            <!-- Wrap the label and switch in a div -->
            <div id="projectIncomeWrapper">
                <label for="editOutsourcing">Project Income</label>
                <label class="switch">
                    <input type="checkbox" id="editOutsourcing" name="outsourcing" onchange="toggleEditOutsourcingDetails()">
                    <span class="slider"></span>
                </label>
            </div>

            <div id="editOutsourcingDetails" style="display: none; margin-top: 10px; align-items: center;">
                <label for="editprojectCode" style="font-size:small;">Project Code</label>
                <select id="editProjectCode" name="projectCode">
                    <option value="">Select Project Code</option>
                    <!-- Options will be dynamically populated -->
                </select>
            </div>
            <script>
                function toggleEditOutsourcingDetails() {
                    const outsourcingDetails = document.getElementById("editOutsourcingDetails");
                    const toggle = document.getElementById("editOutsourcing");
                    outsourcingDetails.style.display = toggle.checked ? "block" : "none";
                }

                // Ensure that the correct display status is set based on the checkbox's state when the page loads.
                document.editEventListener('DOMContentLoaded', function() {
                    toggleEditOutsourcingDetails();  // Check the state of the checkbox and adjust the display accordingly
                });
            </script>


            <div class="form-actions">
                <button id="addBtn" class="submit-btn" type="submit">Submit</button>
                <button type="button" class="cancel-btn" onclick="closeForm()">Cancel</button>
            </div>
        </form>
    </div>
</div>


    <script>

        function openForm() {
            document.getElementById("addModal").style.display = "block";
        }

        function closeForm() {
            document.getElementById("addModal").style.display = "none";
        }

        function openEditForm(date, description, amount, projectIncome) {
            document.getElementById("edit-date").value = date;
            document.getElementById("edit-description").value = description;
            document.getElementById("edit-amount").value = amount;
            document.getElementById("edit-project-income").checked = projectIncome;
            document.getElementById("editForm").style.display = "block";
        }



        function closeEditForm() {
            document.getElementById("editForm").style.display = "none";
        }
    </script>

    <script>// add form

        document.getElementById('addBtn').addEventListener('submit', function(event) { ////////////// form submit button id
            event.preventDefault();

            var form = this;
            var formData = new FormData(form);

            fetch(form.action, {
                method: form.method,
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (response.headers.get('content-type')?.includes('application/json')) {
                    return response.json();
                } else {
                    return response.text().then(text => { throw new Error(text) });
                }
            })
            .then(data => {
                if (data.success) {
                    form.reset();
                    closeAddModal();
                    location.reload();
                } else {
                    var errorMessage = document.getElementById("error-message");
                    errorMessage.textContent = data.error;
                    errorMessage.style.display = "block";
                }
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById("error-message").textContent = 'An error occurred: ' + error.message;
                document.getElementById("error-message").style.display = "block";
            });
        });


    </script>



    <script>// edit form

        $(document).ready(function() {
            var modal = $('#editModal');
            var span = $('.close');
            var cancelBtn = $('#cancelBtnEdit');

            $.ajaxSetup({
                beforeSend: function(xhr, settings) {
                    if (!this.crossDomain) {
                        xhr.setRequestHeader("X-CSRFToken", getCookie('csrftoken'));
                    }
                }
            });

            function getCookie(name) {
                var cookieValue = null;
                if (document.cookie && document.cookie !== '') {
                    var cookies = document.cookie.split(';');
                    for (var i = 0; i < cookies.length; i++) {
                        var cookie = cookies[i].trim();
                        if (cookie.substring(0, name.length + 1) === (name + '=')) {
                            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                            break;
                        }
                    }
                }
                return cookieValue;
            }

            // Close the modal
            span.on('click', function() {
                modal.hide();
            });

            cancelBtn.on('click', function() {
                modal.hide();
            });

            function convertToDate(dateStr) {
                const date = new Date(dateStr);
                const pad = (num) => (num < 10 ? '0' + num : num);
                const localDate = date.getFullYear() + '-' +
                                   pad(date.getMonth() + 1) + '-' +
                                   pad(date.getDate());
                return localDate;
            }

            function openModal(inc_id) {
                if (inc_id) {
                    $.ajax({
                        url: '/get_income_data/' + inc_id + '/',
                        method: 'GET',
                        success: function(data) {
                            $('#editForm').attr('data-edit-id', inc_id); // Set the edit code
                            $('#edit-date').val(convertToDate(data.date));
                            $('#edit-description').val(data.desc);
                            $('#edit-amount').val(data.amount);
                            $('#editnote').val(data.note);
                            $('#edit-project-income').prop('checked', data.project_income);  // Set checkbox checked state
                                if (data.project_code) {
                                    $('#editProjectCode').val(data.project_code.id);  // Set the selected value
                                } else {
                                    $('#editProjectCode').val('');  // Set to empty if no project is associated
                                }

                            modal.show();
                        },
                        error: function() {
                            alert('Failed to fetch data. Please try again.');
                        }
                    });
                } else {
                    $('#editForm').removeAttr('data-edit-id'); // Clear the edit code for new projects
                    modal.show();
                }
            }

            // Attach click event to edit buttons
            $('.action-btn').on('click', function(event) {
                event.preventDefault(); // Prevent default link behavior
                event.stopPropagation();
                var id = $(this).data('id'); // Get the project code from the button
                openModal(id);
            });

            $('#editForm').on('submit', function(event) {
                event.preventDefault();
                var id = $('#editForm').attr('data-edit-id'); // Get the edit code
                var url = '/edit_income/' + (id ? id + '/' : ''); // Ensure URL includes code if available

                $.ajax({
                    url: url,
                    method: 'POST',
                    data: $(this).serialize(),
                    success: function(response) {
                        if (response.success) {
                            alert('Form submitted successfully!');
                            modal.hide();
                            location.reload();  // Reload the page to reflect changes
                        } else {
                            alert('Failed to submit form: ' + response.error);
                        }
                    },
                    error: function() {
                        alert('An error occurred. Please try again.');
                    }
                });
            });
        });

    </script>

    <script>
        document.addEventListener('DOMContentLoaded', (event) => {
            const ctx = document.getElementById('projectChart').getContext('2d');
            const months = JSON.parse('{{ months|escapejs }}');
            const originalValues = JSON.parse('{{ original_values|escapejs }}');
            const receivedValues = JSON.parse('{{ received_values|escapejs }}');

            const projectChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: months,
                    datasets: [
                        {
                            label: 'Project Value',
                            data: originalValues,
                            backgroundColor: 'rgba(0,0,0)',
                            borderColor: 'rgba(0,0,0,0)',
                            borderWidth: 3,
                            borderRadius: 5,
                            barThickness: 21,
                        },
                        {
                            label: 'Received Value',
                            data: receivedValues,
                            backgroundColor: '#B24BF3',
                            borderColor: 'rgba(0,0,0,0)',
                            borderWidth: 3,
                            borderRadius: 5,
                            barThickness: 21,
                        }
                    ]
                },
                options: {
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                            align: 'start',
                            labels: {
                                usePointStyle: true,
                                pointStyle: 'rectRounded',
                                color: 'black',
                                boxWidth: 20,
                                boxHeight: 20,
                            }
                        },
                        tooltip: {
                            callbacks: {
                                title: function(tooltipItems) {
                                    // Show the month
                                    return months[tooltipItems[0].dataIndex];
                                },
                                label: function(tooltipItem) {
                                    // Show the value for the specific dataset
                                    if (tooltipItem.datasetIndex === 0) {
                                        return `Project Value: ${originalValues[tooltipItem.dataIndex]}`;
                                    } else if (tooltipItem.datasetIndex === 1) {
                                        return `Received Value: ${receivedValues[tooltipItem.dataIndex]}`;
                                    }
                                }
                            },
                            displayColors: true,
                        }
                    },
                    scales: {
                        x: {
                            stacked: false,
                            ticks: {
                                autoSkip: false,
                                display: false
                            },
                            barPercentage: 0.8,
                            categoryPercentage: 0.5
                        },
                        y: {
                            stacked: false,
                            beginAtZero: true
                        }
                    }
                }
            });
        });
    </script>


    <script>
    // project code dynamic dropdown for add
    document.addEventListener('DOMContentLoaded', function() {
        var projectCodeDropdown = document.getElementById('projectCode');

        // Fetch project codes when the page loads
        fetch('/get_project_codes/')
            .then(response => response.json())
            .then(data => {
                data.forEach(function(item) {
                    var option = document.createElement('option');
                    option.value = item.id;
                    option.text = item.name;
                    projectCodeDropdown.appendChild(option);
                });
            })
            .catch(error => console.error('Error fetching project codes:', error));
    });

    //////////////////////////////

    // project code dynamic dropdown for edit
    document.addEventListener('DOMContentLoaded', function() {
        var projectCodeDropdown = document.getElementById('editProjectCode');

        // Fetch project codes when the page loads
        fetch('/get_project_codes/')
            .then(response => response.json())
            .then(data => {
                data.forEach(function(item) {
                    var option = document.createElement('option');
                    option.value = item.id;
                    option.text = item.name;
                    projectCodeDropdown.appendChild(option);
                });
            })
            .catch(error => console.error('Error fetching project codes:', error));
    });
</script>
<script>
    // JavaScript to handle dropdown visibility
    const profileMenu = document.getElementById('profileMenu');
    const profileDropdown = document.getElementById('profileDropdown');

    profileMenu.addEventListener('click', function () {
        // Toggle dropdown visibility
        if (profileDropdown.style.display === 'none' || profileDropdown.style.display === '') {
            profileDropdown.style.display = 'block';
        } else {
            profileDropdown.style.display = 'none';
        }
    });

    // Close dropdown if clicked outside
    window.addEventListener('click', function (event) {
        if (!profileMenu.contains(event.target)) {
            profileDropdown.style.display = 'none';
        }
    });
</script>
<script>
  // user icon
  const username = document.getElementById('name').textContent;
  document.querySelector('#userIcon').innerText = username.charAt(0);
</script>

<script>
    // page navigation from div
    function moveToDetailPage(inc_id) {
        window.location.href = "/income/" + inc_id + "/";
    }
</script>
<script>
    const sidebar = document.querySelector('.sidebar');
    const toggleIcon = document.getElementById('toggle-icon');

    toggleIcon.addEventListener('click', function() {
      if (sidebar.classList.contains('closed')) {
        sidebar.classList.remove('closed');
        toggleIcon.src = "{% static 'images/menuleft.png' %}";  // Change to the "left-chevron" when open
      } else {
        sidebar.classList.add('closed');
        toggleIcon.src = "{% static 'images/menuright.png' %}";  // Change to the "chevron" when closed
      }
    });
  </script>
  <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script>
        config={
            enableTime:false,

        }
        flatpickr("input[type=date]",config);
    </script>
</body>

</html>
