{% load static %}
{% static "images" as baseurl %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Location</title>
    <link rel="stylesheet" type="text/css" href="https://npmcdn.com/flatpickr/dist/themes/dark.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAqIalCM0rqsXeHiyRP4ImBbVgqwMSv8D8&callback=initMap" async defer></script>
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAqIalCM0rqsXeHiyRP4ImBbVgqwMSv8D8&libraries=places"></script>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>



    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            height: 100vh;
            flex-direction: column;
        }
        .container {
            display: flex;
            width: 100%;
            flex-grow: 1;
        }
            .sidebar {
              background-color: #1e1e1e;
              color: white;
              width: 250px;
              display: flex;
              flex-direction: column;
              justify-content: flex-start;
              align-items: center;
              transition: width 0.3s;
              position: relative;
          }

          .sidebar.closed {
              width: 60px;
          }

          /* Icon visibility and border */
          .sidebar .toggle-icon {
              position: absolute;
              top: 25px !important; /* Aligned near the top */
              right: -8px; /* Adjusted to be right on the edge line */
              cursor: pointer;
              visibility: hidden;
              border: 3px solid rgba(78, 27, 231, 0.5); /* Light border */
              border-radius: 8px;
              padding: 1px;
              transition: visibility 0.3s ease-in-out, top 0.3s ease-in-out; /* Smooth transitions */
              z-index: 2;
          }
          #toggle-icon {
              width: 20px;
              height: 20px;
          }


          /* Adjust position for closed state to avoid overlap */
          .sidebar.closed .toggle-icon {
              top: 10px;
              right: -8px; /* Keep it on the edge even when closed */
          }

          /* Show icon when hovering near the sidebar or over the icon */
          .sidebar:hover .toggle-icon, .toggle-icon:hover {
              visibility: visible;
          }

          .sidebar .logo {
              padding: 20px;
              text-align: center;
          }

          .sidebar.closed .logo {
              display: none;
          }

          .sidebar nav ul {
              list-style: none;
              padding: 0;
              width: 100%;
              text-align: center;
          }

          .sidebar nav ul li {
              padding: 12px 20px;
              cursor: pointer;
              transition: background-color 0.3s, border-left 0.3s;
              display: flex;
              justify-content: flex-start;
              align-items: center;
          }

          .sidebar.closed nav ul li {
              justify-content: center;
          }

          .sidebar nav ul li a {
              display: flex;
              align-items: center;
              text-decoration: none;
              color: white;
              width: 100%;
              font-family: Arial, sans-serif;
          }

          .sidebar nav ul li a:hover {
              background-color: #555;
              border-left: 4px solid #ffcc00;
          }

          .menu-icon {
              margin-right: 10px;
              width: 24px;
              height: 24px;
          }

          .menu-text {
              transition: opacity 0.3s, visibility 0.3s;
              font-family: Arial, sans-serif;
          }

          .sidebar.closed .menu-text {
              display: none;
          }

          .sidebar.closed nav ul li:hover {
              background-color: inherit;
          }


          .main-content {
            flex-grow: 1;
            background-color: #f1f1f1;
            padding: 20px;
            position: relative; /*  for positioning the form */
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        /* Container to allow scrolling of the map */
        .map-container {
            flex: 1;
            position: relative;
            overflow: auto; /* Scrollbars only for the map */
            height: 600px; /* Adjust height to make scrolling visible */
            width: 100%; /* Full width */
            border: 1px solid #ccc; /* Optional border for visibility */
        }

        #map {
            width: 1200px; /* Large enough to require scrolling */
            height: 1200px; /* Large enough to require scrolling */
        }
        .search-bar {
            position: relative;
            display: flex;
            align-items: center;
        }
        .search-bar input {
            padding: 5px 10px;
            font-size: 16px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .add-button .add{
            margin:2px;
        }
        .add-button {
            padding: 10px 20px;
            background-color: #000000;
            color: white;
            border: none;
            font-weight:medium;
            border-radius: 5px;
            cursor: pointer;
            margin-left: 20px;
            display:flex;
        }
        .add-button:hover {
            background-color: #a1ada2;
        }
        .form-container {
            display: none; /* Initially hidden */
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: absolute; /* Positioning the form */
            top: 20px; /* Adjust top position */
            right: 20px; /* Adjust right position */
            width: 390px; /* Increase width here for the form */
            z-index: 1000; /* Ensure it is above other elements */
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input[type="text"],
        input[type="number"],
        input[type="datetime-local"],
        select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        .form-actions {
    display: flex;
    justify-content: flex-end; /* Keep the cancel button aligned to the right */
    margin-top: 20px;
}

.submit-container {
    margin-right: auto; /* This will push the cancel button to the right */
}

.form-actions button {
    margin-left: 10px;
}

form button[type="submit"],
form button[type="button"] {
    padding: 10px 20px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

form button[type="submit"] {
    background-color: black;
    color: white;
}

form button[type="button"] {
    background-color: white;
    border: 1px solid rgb(140,140,140);
    color: rgb(0, 0, 0);
}


          .toggle-container {
        display: flex;
        justify-content: space-between; /* Align label and toggle button */
        align-items: center;
        margin: 10px 0;
    }

    .toggle-right {
        display: flex;
        justify-content: flex-end; /* Aligns toggle to the right */
        flex: 1;
    }

    .toggle {
        position: relative;
        display: inline-block;
        width: 34px;
        height: 20px;
    }

    .toggle input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    input:checked + .sliderr {
        background-color: #000000;
    }

    input:checked + .sliderr:before {
        transform: translateX(14px);
    }

    .sliderr {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: 0.4s;
        border-radius: 20px;
    }

    .sliderr:before {
        position: absolute;
        content: "";
        height: 16px;
        width: 16px;
        left: 2px;
        bottom: 2px;
        background-color: white;
        transition: 0.4s;
        border-radius: 50%;
    }

    .close-btn {
        position: absolute;
        top: 10px;
        right: 10px;
        background-color: transparent;
        border: none;
        font-size: 24px;
        cursor: pointer;
    }

    .close-btn:hover {
        color: grey;
    }

    .form-container h3 {
        text-align: center;
        margin-bottom: 20px;
        font-size: 19px;
    }

    #addOutsourcingAmount,#reference{
    width: 100% !important; /* Ensure this width is applied */
    max-width: 100% !important; /* Limit the width */
    box-sizing: border-box; /* Ensure padding is included in the width */
}

.btn-check {
    display: none; /* Hide the checkbox */
}

.btn-primary {
    background-color: #ffffff !important; /* Ensure white background */
    color: black !important; /* Ensure black text */
    border: 2px solid #ccc;
    border-radius: 10px;
    padding: 10px 15px;
    cursor: pointer;
    margin: 3px; /* Add margin to create gaps */
    transition: background-color 0.3s, color 0.3s, border-color 0.3s, box-shadow 0.3s;
    font-weight: bold;
    font-size: 14px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.btn-primary.active {
    background-color: #000000; /* Even darker blue */
    border-color: #000000; /* Keep the border color */
    color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

#statusButtons {
    display: flex; /* Align buttons in a row */
    justify-content: flex-start; /* Align to the start */
}

/* user icon */

        .user-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background-color: #ddd;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            font-size: 18px;
            color: #0e0e0e;
            background-color: #e1ecb8;

        }
           .profile-section {
            position: relative; /* Allows positioning of the dropdown */
            padding: 12px 20px; /* Match padding with other menu items */
            cursor: pointer; /* Change cursor on hover */
            transition: background-color 0.3s, border-left 0.3s; /* Smooth transition */
        }

        .profile-section:hover {
            background-color: #555; /* Background color on hover */
            border-left: 4px solid #ffcc00; /* Left border on hover */
        }

        .dropdown {
            position: absolute; /* Position relative to the profile section */
            bottom: 100%; /* Position above the profile section */
            left: 0; /* Align to the left */
            background-color: white; /* Background color of the dropdown */
            border: 1px solid #ccc; /* Border for the dropdown */
            border-radius: 4px; /* Rounded corners */
            z-index: 1000; /* Ensure it appears above other elements */
            width: 160px; /* Set width for the dropdown */
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); /* Shadow for a floating effect */
            display: none; /* Initially hidden */
        }

        .dropdown ul {
            list-style: none; /* Remove default list styles */
            padding: 0; /* Remove padding */
            margin: 0; /* Remove margin */
        }

        .dropdown li {
            padding: 10px; /* Padding for each item */
            color: black; /* Set text color to black */
            cursor: pointer; /* Change cursor on hover */
        }

        .dropdown li:hover {
            background-color: #f1f1f1; /* Background on hover */
        }
    </style>
</head>
<body onload="initMap()">
    <div class="container">
        <aside class="sidebar">
            <div class="toggle-icon">
                <img src="{% static 'images/menuleft.png' %}" alt="icon" id="toggle-icon" width="16" height="16">
            </div>
            <div class="logo">
                <img src="{% static 'images/logowhite.png' %}" alt="logo" width="50" height="50">
            </div>
            <nav>
                <ul>
                    <li class="menu-item"><a href="{% url 'dashboard' %}"><img src="{% static 'images/dashboard.png' %}" class="menu-icon"><span class="menu-text">Dashboard</span></a></li>
                    <li class="menu-item"><a href="{% url 'projects' %}"><img src="{% static 'images/project.png' %}" class="menu-icon"><span class="menu-text">Project</span></a></li>
                    <li class="menu-item"><a href="{% url 'incomef_view' %}"><img src="{% static 'images/income.png' %}" class="menu-icon"><span class="menu-text">Income</span></a></li>
                    <li class="menu-item"><a href="{% url 'expense' %}"><img src="{% static 'images/expenses.png' %}" class="menu-icon"><span class="menu-text">Expense</span></a></li>
                    <li class="menu-item"><a href="{% url 'calendar' %}"><img src="{% static 'images/calendar.png' %}" class="menu-icon"><span class="menu-text">Calendar</span></a></li>
                    <li class="menu-item"><a href="{% url 'allproject' %}"><img src="{% static 'images/All projects.png' %}" class="menu-icon"><span class="menu-text">All Projects</span></a></li>
                    <li class="menu-item"><a href="{% url 'clientsbook' %}"><img src="{% static 'images/clientsbook.png' %}" class="menu-icon"><span class="menu-text">Clients Book</span></a></li>
                    <li class="menu-item"><a href="{% url 'clients' %}"><img src="{% static 'images/Clients.png' %}" class="menu-icon"><span class="menu-text">Clients</span></a></li>
                    <li class="menu-item"><a href="{% url 'status' %}"><img src="{% static 'images/status.png' %}" class="menu-icon"><span class="menu-text">Status</span></a></li>
                    <li class="menu-item active"><a href="{% url 'pending_pay' %}"><img src="{% static 'images/pending.png' %}" class="menu-icon"><span class="menu-text">Pending Payments</span></a></li>
                    <li class="menu-item"><a href="{% url 'project_map' %}"><img src="{% static 'images/map.png' %}" class="menu-icon"><span class="menu-text">Map</span></a></li>
                    <li class="menu-item"><a href="{% url 'assets' %}"><img src="{% static 'images/Assets.png' %}" class="menu-icon"><span class="menu-text">Assets</span></a></li>
                    <li class="menu-item"><a href="{% url 'budget' %}"><img src="{% static 'images/budget.png' %}" class="menu-icon"><span class="menu-text">Budget</span></a></li>
                    <li class="menu-item"><a href="{% url 'entertainment' %}"><img src="{% static 'images/Entertainment.png' %}" class="menu-icon"><span class="menu-text">Entertainment</span></a></li>
                </ul>
            </nav>
            <div class="profile-section" id="profileMenu">
                <img src="{% static 'images/profile.png' %}" alt="Profile" class="profile-icon">
                <span class="menu-text">Riktha L</span>
                <div class="dropdown" id="profileDropdown">
                    <ul>
                        <li><a href="{% url 'profile' %}">View Profile</a></li>
                        <li><a href="{% url 'logout_view' %}">Sign Out</a></li>
                    </ul>
                </div>
            </div>
        </aside>

        <main class="main-content">
            <div class="header">
                <h1>Location</h1>
                <div class="search-bar">
                    <input type="text" placeholder="Search">
                    <button class="add-button" id="addButton">
                        <i class="fa-solid fa-plus add"></i><b>Add</b>
                    </button>
                </div>
            </div>
             <!-- Map Container with Scrollbars -->
            <div class="map-container">
                    <div id="map" style="height: 500px; width: 100%;"></div> <!-- Map container -->
            </div>

   <!-- Add form -->
   <div class="form-container" id="projectForm" style="display:none;">
    <button class="close-btn"onclick="closeForm()" >&times;</button> <!-- Close Button -->
 <!-- add modal -->
 <h3>Add Project</h3>
    <form id = "addForm" action ="{% url 'projects' %}" method = "post">
        {% csrf_token %}
        <div class="form-group">
            <label for="eventType">Type</label>
            <select id="eventType" name="addProjectType" >
                <option value="">---</option>
                <!-- dynamic display -->
            </select>
        </div>
        <label for="status">Status</label>
        <div id="statusButtons">
            <input type="checkbox" class="btn-check" id="completedBtn" autocomplete="off" onclick="toggleStatus(this)">
            <label class="btn btn-primary" for="completedBtn">Completed</label>

            <input type="checkbox" class="btn-check" id="ongoingBtn" autocomplete="off" onclick="toggleStatus(this)">
            <label class="btn btn-primary" for="ongoingBtn">Ongoing</label>

            <input type="checkbox" class="btn-check" id="pendingBtn" autocomplete="off" onclick="toggleStatus(this)">
            <label class="btn btn-primary" for="pendingBtn">Pending</label>

            <input type="checkbox" class="btn-check" id="cancelledBtn" autocomplete="off" onclick="toggleStatus(this)">
            <label class="btn btn-primary" for="cancelledBtn">Cancelled</label>
        </div>

<!-- Hidden input to store the selected status -->
    <input type="hidden" id="addProjectStatus" name="addProjectStatus" value="" >

        <div class="form-group">
            <label for="projectName">Project Name</label>
            <input type="text" id="projectName" name="addProjectName" >
        </div>

        <div class="form-group">
            <label for="customerCompany">Customer Company</label>
            <input type="text" id="customerCompany" name="addCustomerCompany" >
        </div>

        <div class="form-group">
            <label for="shootStart">Shoot Start</label>
            <input type="datetime-local" id="shootStart" name="addShootStart" >
        </div>
        <div class="form-group">
            <label for="shootEnd">Shoot End</label>
            <input type="datetime-local" id="shootEnd" name="addShootEnd" >
        </div>

        <div class="form-group">
            <label for="projectAmount">Project Amount</label>
            <input type="number" id="projectAmount" name="addProjectAmount" >
        </div>


        <div class="form-group">
            <label for="projectLocation">Project Location</label>
            <input type="text" id="projectLocation" name="addProjectLocation" >
        </div>

        <!-- Hidden fields to store latitude and longitude -->
        <input type="hidden" id="latitude" name="latitude">
        <input type="hidden" id="longitude" name="longitude">

            <!--sandhiya-->
            <div class="form-group">
                <label for="locationLink">Location Link</label>
                <input type="text" id="locationLink" name="locationLink" placeholder="Enter Google Maps link">
                <button type="button" onclick="getAddressFromLink()" style="margin-top:8px">Get Address</button>
            </div>
            <div class="form-group">
                <label for="address">Extracted Address</label>
                <input type="text" id="address" name="address" readonly>
            </div>


        <div class="form-group toggle-container">
            <label for="outsourcing">Outsourcing</label>
            <div class="toggle-right">
                <label class="toggle">
                    <input type="checkbox" id="outsourcing" name="addoutsourcing" onclick="toggleAddOutsourcingDetails()">
                    <span class="sliderr"></span>
                </label>
            </div>
        </div>

        <div id="addOutsourcingDetails" style="display: none; margin-top: 10px;align-items: center;">
            <label for="addOutsourcingFor">Outsourcing For</label>
            <select id="addOutsourcingFor" name="addoutsourcingFor">
                <option value="">---</option>

                <option >Photo</option>
                <option >Video</option>
                <option >Editor</option>
                <option >Drone</option>
                <option >Pilot</option>
            </select>

            <label for="addOutsourcingAmount" style="margin-top: 10px;white-space:nowrap;">Outsourcing Amount</label>
            <input type="number" id="addOutsourcingAmount" name="addoutsourcingAmount">

            <label for="addOutsourcingCustomer" style="margin-top: 10px;">Outsourcing Customer Name</label>
            <select id="addOutsourcingCustomer" name="addoutsourcingCustomer">
                <option value="">---</option>
            </select>

            <label for="addOutsourcingPaid" style="display: flex; align-items: center; margin-top: 10px;white-space:nowrap;">
                Outsourcing Paid
                <label class="toggle" style="margin-left: 230px;">
                    <input type="checkbox" id="addOutsourcingPaid" name="addoutsourcingPaid">
                    <span class="sliderr"></span>
                </label>
            </label>
        </div>

        <!--sandhiya-->
        <div class="form-group">
            <label for="reference">Reference</label>
            <input type="text" id="reference" name="addReference">
        </div>

        <div class="form-actions">
            <div class="submit-container">
                <button class="submit" type="submit" id="addProjectBtn" >Submit</button>
            </div>
            <button class="button" type="button" class="cancel" id="cancelButton">Cancel</button>
        </div>

    </form>
</div>
</main>
</div>

    <script>
        // user icon
        const username = document.getElementById('name').textContent;
        document.querySelector('#userIcon').innerText = username.charAt(0);
    </script>

    <script>

        document.getElementById('addButton').onclick = function() {
            document.getElementById('projectForm').style.display = 'block';
        };
        function closeForm() {
            document.getElementById("projectForm").style.display = "none";
        }

    </script>

    <script>



        document.getElementById('addProjectBtn').addEventListener('submit', function(event) {
            event.preventDefault();

            var form = this;
            var formData = new FormData(form);

            fetch(form.action, {
                method: form.method,
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (response.headers.get('content-type')?.includes('application/json')) {
                    return response.json();
                } else {
                    return response.text().then(text => { throw new Error(text) });
                }
            })
            .then(data => {
                if (data.success) {
                    closeForm();  // Close form on successful submission
                    location.reload();  // Optionally reload the page
                } else {
                    var errorMessage = document.getElementById("error-message");
                    errorMessage.textContent = data.error;
                    errorMessage.style.display = "block";
                }
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById("error-message").textContent = 'An error occurred: ' + error.message;
                document.getElementById("error-message").style.display = "block";
            });
        });


        // Close button logic (&times;)
        document.querySelector('.close-btn').addEventListener('click', function() {
            closeForm();  // Close the form when the close button is clicked
        });




        document.getElementById('cancelButton').onclick = function() {
            const form = document.getElementById('addForm');
            document.getElementById('projectForm').style.display = 'none';
            form.reset(); // Reset form fields
        };

        function selectStatus(status) {
            // Remove selected class from all buttons
            const buttons = document.querySelectorAll('.status-button');
            buttons.forEach(button => {
                button.classList.remove('selected');
                button.style.backgroundColor = "#cbcbcb";
            });
            // Add selected class to the clicked button
            const selectedButton = document.querySelector(.status-button.${status});
            selectedButton.classList.add('selected');
            selectedButton.style.backgroundColor = "#4CAF50";
        }


    </script>


    <script>
        document.addEventListener('DOMContentLoaded', function() {
            var dropdownIds = ['addOutsourcingCustomer'];  // Add the IDs of all the dropdowns here

            // Fetch type when the page loads
            fetch('/get-unique-client/')
                .then(response => response.json())
                .then(data => {
                    dropdownIds.forEach(function(dropdownId) {
                        var dropdown = document.getElementById(dropdownId);
                        dropdown.innerHTML = '';  // Clear existing options

                        // Populate each dropdown with the fetched types
                        data.forEach(function(client) {
                            var option = document.createElement('option');
                            option.value = client;
                            option.text = client;
                            dropdown.appendChild(option);
                        });
                    });
                })
                .catch(error => console.error('Error fetching type:', error));
        });

    </script>


    <script>
        $.getScript("https://maps.googleapis.com/maps/api/js?key=AIzaSyAqIalCM0rqsXeHiyRP4ImBbVgqwMSv8D8&libraries=places")
        .done(function(script, textStatus) {
            google.maps.event.addDomListener(window, "load", initAutoComplete);
        });

        let autocomplete;

        function initAutoComplete() {
            autocomplete = new google.maps.places.Autocomplete(
                document.getElementById('projectLocation'),
                {
                    // No 'types' filter for broader searches (places, establishments, addresses, etc.)
                    componentRestrictions: {'country': 'in'} // Restrict to India (or change country if needed)
                }
            );

            autocomplete.addListener('place_changed', onPlaceChanged);
        }

        function onPlaceChanged() {
            var place = autocomplete.getPlace();

            if (!place.geometry) {
                document.getElementById('projectLocation').placeholder = "*Begin typing address or place name";
                return;
            }

            // Retrieve latitude and longitude
            var latitude = place.geometry.location.lat();
            var longitude = place.geometry.location.lng();

            // Populate hidden fields with latitude and longitude
            $('#latitude').val(latitude);
            $('#longitude').val(longitude);

            // Optionally, retrieve more address components as before
            var num = '', route = '', town = '', county = '', country = '', postalCode = '';
            for (var i = 0; i < place.address_components.length; i++) {
                for (var j = 0; j < place.address_components[i].types.length; j++) {
                    if (place.address_components[i].types[j] === "street_number") {
                        num = place.address_components[i].long_name;
                    }
                    if (place.address_components[i].types[j] === "route") {
                        route = place.address_components[i].long_name;
                    }
                    if (place.address_components[i].types[j] === "locality") {
                        town = place.address_components[i].long_name;
                    }
                    if (place.address_components[i].types[j] === "administrative_area_level_2") {
                        county = place.address_components[i].long_name;
                    }
                    if (place.address_components[i].types[j] === "country") {
                        country = place.address_components[i].long_name;
                    }
                    if (place.address_components[i].types[j] === "postal_code") {
                        postalCode = place.address_components[i].long_name;
                    }
                }
            }

            console.log(`Latitude: ${latitude}, Longitude: ${longitude}`);
            console.log(`Address: ${num} ${route}, Town: ${town}, Country: ${country}`);
        }


    </script>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/5.3.0/js/bootstrap.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

<script>
          config={
              enableTime:true,

          }
          flatpickr("input[type=datetime-local]",config);
</script>

<!-- Location link script-->
<script>
    async function resolveShortUrl(shortUrl) {
        try {
            const response = await fetch(`/resolve-url/?url=${encodeURIComponent(shortUrl)}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            if (data.resolved_url) {
                return data.resolved_url;
            } else {
                console.error('Error resolving URL:', data.error);
                return null;
            }
        } catch (error) {
            console.error('Error resolving short URL:', error);
            return null;
        }
    }


          function extractPlaceNameAndCoordinates(link) {
              // Updated regex to correctly extract place name and latitude/longitude
              const regex = /\/maps\/place\/([^\/]+)\/@(-?\d+\.\d+),(-?\d+\.\d+)/;
              const match = link.match(regex);
              if (match) {
                  const placeName = decodeURIComponent(match[1]).replace(/\+/g, ' '); // Decode and replace + with spaces
                  const lat = parseFloat(match[2]);
                  const lng = parseFloat(match[3]);
                  return { placeName, coordinates: { lat, lng } };
              }
              return null;
          }

          async function getAddressFromLink() {
              const locationLink = document.getElementById("locationLink").value;
              const addressField = document.getElementById("address");

              try {
                  // Resolve the URL if it is a short URL
                  const resolvedUrl = locationLink.includes('goo.gl') ? await resolveShortUrl(locationLink) : locationLink;

                  if (resolvedUrl) {
                      // Extract the place name and coordinates from the resolved link
                      const placeInfo = extractPlaceNameAndCoordinates(resolvedUrl);
                      console.log("ex co: ",placeInfo);
                      if (placeInfo) {
                          // Call initMap with the coordinates
                          initMap(placeInfo.coordinates);
                          addressField.value = 'Fetching address...'; // Optional: Indicate address fetching
                          const address = await geocodeLatLng(placeInfo.coordinates);
                          addressField.value = address; // Update the address field with the fetched address
                      } else {
                          addressField.value = 'Invalid link or no coordinates found';
                      }
                  } else {
                      addressField.value = 'Failed to resolve URL';
                  }
              } catch (error) {
                  console.error('Error fetching address:', error);
                  addressField.value = 'Error fetching address: ' + error.message;
              }
          }
              // Function to geocode latitude and longitude to an address
       function geocodeLatLng(latlng) {
          return new Promise((resolve, reject) => {
              const geocoder = new google.maps.Geocoder();
              const latlngObj = {
                  lat: parseFloat(latlng.lat),
                  lng: parseFloat(latlng.lng)
              };

              geocoder.geocode({ location: latlngObj }, (results, status) => {
                  if (status === 'OK') {
                      if (results[0]) {
                          resolve(results[0].formatted_address); // Resolve with formatted address
                      } else {
                          reject('No results found');
                      }
                  } else {
                      reject('Geocoder failed due to: ' + status);
                  }
              });
          });
      }
</script>

<script>
          // JavaScript to handle dropdown visibility
          const profileMenu = document.getElementById('profileMenu');
          const profileDropdown = document.getElementById('profileDropdown');

          profileMenu.addEventListener('click', function () {
              // Toggle dropdown visibility
              if (profileDropdown.style.display === 'none' || profileDropdown.style.display === '') {
                  profileDropdown.style.display = 'block';
              } else {
                  profileDropdown.style.display = 'none';
              }
          });

          // Close dropdown if clicked outside
          window.addEventListener('click', function (event) {
              if (!profileMenu.contains(event.target)) {
                  profileDropdown.style.display = 'none';
              }
          });
</script>

<script>
    const sidebar = document.querySelector('.sidebar');
    const toggleIcon = document.getElementById('toggle-icon');

    toggleIcon.addEventListener('click', function() {
      if (sidebar.classList.contains('closed')) {
        sidebar.classList.remove('closed');
        toggleIcon.src = "{% static 'images/menuleft.png' %}";  // Change to the "left-chevron" when open
      } else {
        sidebar.classList.add('closed');
        toggleIcon.src = "{% static 'images/menuright.png' %}";  // Change to the "chevron" when closed
      }
    });
</script>



<script>
        document.addEventListener('DOMContentLoaded', function() {
            var dropdownIds = ['eventType'];  // Add the IDs of all the dropdowns here

            // Fetch type when the page loads
            fetch('/get-unique-types/')
                .then(response => response.json())
                .then(data => {
                    dropdownIds.forEach(function(dropdownId) {
                        var dropdown = document.getElementById(dropdownId);
                        dropdown.innerHTML = '';  // Clear existing options

                        // Populate each dropdown with the fetched types
                        data.forEach(function(type) {
                            var option = document.createElement('option');
                            option.value = type;
                            option.text = type;
                            dropdown.appendChild(option);
                        });
                    });
                })
                .catch(error => console.error('Error fetching type:', error));
        });
</script>

<script>
        // Toggle outsourcing details
        function toggleAddOutsourcingDetails() {
        const outsourcingDetails = document.getElementById("addOutsourcingDetails");
        const outsourcingToggle = document.getElementById("outsourcing");

        // Show/Hide outsourcing details based on the toggle status
        outsourcingDetails.style.display = outsourcingToggle.checked ? "block" : "none";
}

</script>

<script>
        document.addEventListener('DOMContentLoaded', function() {
            var dropdownIds = ['addOutsourcingCustomer'];  // Add the IDs of all the dropdowns here

            // Fetch type when the page loads
            fetch('/get-unique-client/')
                .then(response => response.json())
                .then(data => {
                    dropdownIds.forEach(function(dropdownId) {
                        var dropdown = document.getElementById(dropdownId);
                        dropdown.innerHTML = '';  // Clear existing options

                        // Populate each dropdown with the fetched types
                        data.forEach(function(client) {
                            var option = document.createElement('option');
                            option.value = client;
                            option.text = client;
                            dropdown.appendChild(option);
                        });
                    });
                })
                .catch(error => console.error('Error fetching type:', error));
        });

</script>


<script>
    function toggleStatus(checkbox) {
        var buttons = document.querySelectorAll('#statusButtons .btn-check');

        if (checkbox.checked) {
            buttons.forEach(function(btn) {
                if (btn !== checkbox) {
                    btn.checked = false;
                }
            });
        }

        buttons.forEach(function(btn) {
            var label = document.querySelector('label[for="' + btn.id + '"]');
            if (btn.checked) {
                label.classList.add('active');
            } else {
                label.classList.remove('active');
            }
        });
    }

    function closeEditModal() {
        document.getElementById('editModal').style.display = 'none';
    }
  </script>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
  <script src="https://maxcdn.bootstrapcdn.com/bootstrap/5.3.0/js/bootstrap.min.js"></script>


<script>
    let currentInfoWindow = null; // Variable to keep track of the currently open info window

    // Initialize the map centered around South India
    function initMap() {
        const southIndiaCenter = { lat: 12.9716, lng: 77.5946 }; // Center on Bangalore

        // Initialize the map
        const map = new google.maps.Map(document.getElementById('map'), {
            zoom: 6, // Zoom level to show South India
            center: southIndiaCenter, // Initial center at South India
            mapTypeId: 'roadmap', // Can also be 'satellite', 'hybrid', 'terrain'
            scrollwheel: true, // Enable zooming with mouse scroll wheel
            gestureHandling: 'auto' // Enable gesture handling (for touchpads and mobile devices)
        });

        // Fetch project data from the backend
        fetch('/map/', { // Update with the correct URL
            headers: {
                'X-Requested-With': 'XMLHttpRequest' // Set the AJAX request header
            }
        })
        .then(response => response.json())
        .then(data => {
            const projects = data.projects; // Get projects from the response

            // Add markers to the map
            projects.forEach(project => {
                const marker = new google.maps.Marker({
                    position: { lat: project.location[0], lng: project.location[1] },
                    map: map,
                    title: project.name
                });

                // Generate the Google Static Map URL based on project location
                const staticMapUrl = `https://maps.googleapis.com/maps/api/staticmap?center=${project.location[0]},${project.location[1]}&zoom=14&size=400x300&maptype=roadmap&markers=color:red%7C${project.location[0]},${project.location[1]}&key=AIzaSyAqIalCM0rqsXeHiyRP4ImBbVgqwMSv8D8`;

                const infoWindowContent = `
                <div style="display: flex; flex-direction: column; width: 200px; font-family: Arial, sans-serif; text-align: left;">
                    <div style="flex: 2; display: flex; justify-content: center;">
                        <a href="https://www.google.com/maps?q=${project.location[0]},${project.location[1]}" target="_blank">
                            <img src="${staticMapUrl}" alt="Location View" style="width: 100%; height: auto; border-radius: 4px 4px 0 0; object-fit: cover; cursor: pointer;">
                        </a>
                    </div>
                    <div style="flex: 1; background-color: #f8f9fa; padding: 8px; border-radius: 0 0 4px 4px;">
                        <h3 style="font-size: 16px; margin: 0 0 4px;">${project.name}</h3>
                        <p style="font-size: 12px; margin: 0; color: grey;">${project.location[0]}, ${project.location[1]}</p>
                        <p style="font-size: 12px; margin: 0; font-weight: 400;">${project.client}</p>
                        <div style="text-align: right; margin-top: 8px;">
                            <button onclick="moveToDetailPage('${project.code}')" style="padding: 4px 8px; font-size: 12px; background-color: #000000; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                View Project
                            </button>
                        </div>
                    </div>
                </div>
            `;

                // Add info windows to markers
                const infoWindow = new google.maps.InfoWindow({
                    content: infoWindowContent
                });

                marker.addListener('click', function() {
                    if (currentInfoWindow) {
                        currentInfoWindow.close(); // Close the previously open info window
                    }
                    infoWindow.open(map, marker);
                    currentInfoWindow = infoWindow; // Set the current info window
                });
            });

            // Set initial view to display all locations
            const bounds = new google.maps.LatLngBounds();
            projects.forEach(project => {
                bounds.extend({ lat: project.location[0], lng: project.location[1] });
            });
            map.fitBounds(bounds); // Adjust map to fit all markers
        })
        .catch(error => {
            console.error('Error fetching project data:', error);
        });
    }

            // Redirect to project detail page
            function moveToDetailPage(code) {
                window.location.href = `/project/${code}/`;
            }
</script>




</body>
</html>