{% load static %}
{% static "images" as baseurl %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Location</title>
    <link rel="stylesheet" type="text/css" href="https://npmcdn.com/flatpickr/dist/themes/dark.css">
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAqIalCM0rqsXeHiyRP4ImBbVgqwMSv8D8&callback=initMap" defer></script>
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAqIalCM0rqsXeHiyRP4ImBbVgqwMSv8D8&libraries=places"></script>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <link rel="stylesheet" href="styles.css">

    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            height: 100vh;
            flex-direction: column;
        }
        .container {
            display: flex;
            width: 100%;
            flex-grow: 1;
        }
            .sidebar {
              background-color: #1e1e1e;
              color: white;
              width: 250px;
              display: flex;
              flex-direction: column;
              justify-content: flex-start;
              align-items: center;
              transition: width 0.3s;
              position: relative;
          }

          .sidebar.closed {
              width: 60px;
          }

          /* Icon visibility and border */
          .sidebar .toggle-icon {
              position: absolute;
              top: 25px !important; /* Aligned near the top */
              right: -8px; /* Adjusted to be right on the edge line */
              cursor: pointer;
              visibility: hidden;
              border: 3px solid rgba(78, 27, 231, 0.5); /* Light border */
              border-radius: 8px;
              padding: 1px;
              transition: visibility 0.3s ease-in-out, top 0.3s ease-in-out; /* Smooth transitions */
              z-index: 2;
          }
          #toggle-icon {
              width: 20px;
              height: 20px;
          }


          /* Adjust position for closed state to avoid overlap */
          .sidebar.closed .toggle-icon {
              top: 10px;
              right: -8px; /* Keep it on the edge even when closed */
          }

          /* Show icon when hovering near the sidebar or over the icon */
          .sidebar:hover .toggle-icon, .toggle-icon:hover {
              visibility: visible;
          }

          .sidebar .logo {
              padding: 20px;
              text-align: center;
          }

          .sidebar.closed .logo {
              display: none;
          }

          .sidebar nav ul {
              list-style: none;
              padding: 0;
              width: 100%;
              text-align: center;
          }

          .sidebar nav ul li {
              padding: 12px 20px;
              cursor: pointer;
              transition: background-color 0.3s, border-left 0.3s;
              display: flex;
              justify-content: flex-start;
              align-items: center;
          }

          .sidebar.closed nav ul li {
              justify-content: center;
          }

          .sidebar nav ul li a {
              display: flex;
              align-items: center;
              text-decoration: none;
              color: white;
              width: 100%;
              font-family: Arial, sans-serif;
          }

          .sidebar nav ul li a:hover {
              background-color: #555;
              border-left: 4px solid #ffcc00;
          }

          .menu-icon {
              margin-right: 10px;
              width: 24px;
              height: 24px;
          }

          .menu-text {
              transition: opacity 0.3s, visibility 0.3s;
              font-family: Arial, sans-serif;
          }

          .sidebar.closed .menu-text {
              display: none;
          }

          .sidebar.closed nav ul li:hover {
              background-color: inherit;
          }


        .main-content {
            flex-grow: 1;
            background-color: #f1f1f1;
            padding: 20px;
            position: relative; /* Required for positioning the form */
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        /* Container to allow scrolling of the map */
        .map-container {
            flex: 1;
            position: relative;
            overflow: auto; /* Scrollbars only for the map */
            height: 600px; /* Adjust height to make scrolling visible */
            width: 100%; /* Full width */
            border: 1px solid #ccc; /* Optional border for visibility */
        }

        #map {
            width: 1200px; /* Large enough to require scrolling */
            height: 1200px; /* Large enough to require scrolling */
        }
        .search-bar {
            position: relative;
            display: flex;
            align-items: center;
        }
        .search-bar input {
            padding: 5px 10px;
            font-size: 16px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .add-button {
            padding: 10px 20px;
            background-color: #000000;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-left: 20px;
        }
        .add-button:hover {
            background-color: #a1ada2;
        }
        .form-container {
            display: none; /* Initially hidden */
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: absolute; /* Positioning the form */
            top: 20px; /* Adjust top position */
            right: 20px; /* Adjust right position */
            width: 350px; /* Increase width here for the form */
            z-index: 1000; /* Ensure it is above other elements */
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input[type="text"],
        input[type="number"],
        input[type="datetime-local"],
        select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .status-button {
            margin-right: 10px;
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
            color: black;
            font-weight: bold;
            font-size: 14px;
            margin-top: 20px;
        }
        .status-button.completed {
            background-color: #cbcbcb; /* Default color */
        }
        .status-button.pending {
            background-color: #cbcbcb; /* Default color */
        }
        .status-button.ongoing {
            background-color: #cbcbcb; /* Default color */
        }
        .status-button.cancelled {
            background-color: #cbcbcb; /* Default color */
        }
        .status-button.selected {
            background-color: #d21998; /* Change to green when selected */
            color: white; /* Change text color to white */
        }
        .form-actions {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
          }

          .form-actions button {
            margin-left: 10px;
          }
          form button[type="submit"],
          form button[type="button"] {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
          }

          form button[type="submit"] {
            background-color: black;
            color: white;
          }

          form button[type="button"] {
            background-color: #ccc;
          }

        .toggle-container {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }
        .toggle {
            position: relative;
            display: inline-block;
            width: 34px;
            height: 20px;

        }
        .toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        input:checked + .sliderr {
            background-color: #000000;
        }
        input:checked + .sliderr:before {
            transform: translateX(14px);
        }
        .sliderr {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 20px;
        }
        .sliderr:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        .close-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: transparent;
            border: none;
            font-size: 24px;
            cursor: pointer;
        }

        .close-btn:hover {
            color: grey;
        }
        .modal-content h3 {
            text-align: center;
            margin-bottom: 20px;
            font-size:19px;
           }
           .profile-section {
            position: relative; /* Allows positioning of the dropdown */
            padding: 12px 20px; /* Match padding with other menu items */
            cursor: pointer; /* Change cursor on hover */
            transition: background-color 0.3s, border-left 0.3s; /* Smooth transition */
        }

        .profile-section:hover {
            background-color: #555; /* Background color on hover */
            border-left: 4px solid #ffcc00; /* Left border on hover */
        }

        .dropdown {
            position: absolute; /* Position relative to the profile section */
            bottom: 100%; /* Position above the profile section */
            left: 0; /* Align to the left */
            background-color: white; /* Background color of the dropdown */
            border: 1px solid #ccc; /* Border for the dropdown */
            border-radius: 4px; /* Rounded corners */
            z-index: 1000; /* Ensure it appears above other elements */
            width: 160px; /* Set width for the dropdown */
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); /* Shadow for a floating effect */
            display: none; /* Initially hidden */
        }

        .dropdown ul {
            list-style: none; /* Remove default list styles */
            padding: 0; /* Remove padding */
            margin: 0; /* Remove margin */
        }

        .dropdown li {
            padding: 10px; /* Padding for each item */
            color: black; /* Set text color to black */
            cursor: pointer; /* Change cursor on hover */
        }

        .dropdown li:hover {
            background-color: #f1f1f1; /* Background on hover */
        }
    </style>
</head>
<body>
    <div class="container">
        <aside class="sidebar">
            <div class="toggle-icon">
                <img src="{% static 'images/menuleft.png' %}" alt="icon" id="toggle-icon" width="16" height="16">
            </div>
            <div class="logo">
                <img src="{% static 'images/logowhite.png' %}" alt="logo" width="50" height="50">
            </div>
            <nav>
                <ul>
                    <li class="menu-item"><a href="{% url 'dashboard' %}"><img src="{% static 'images/dashboard.png' %}" class="menu-icon"><span class="menu-text">Dashboard</span></a></li>
                    <li class="menu-item"><a href="{% url 'projects' %}"><img src="{% static 'images/project.png' %}" class="menu-icon"><span class="menu-text">Project</span></a></li>
                    <li class="menu-item"><a href="{% url 'incomef_view' %}"><img src="{% static 'images/income.png' %}" class="menu-icon"><span class="menu-text">Income</span></a></li>
                    <li class="menu-item"><a href="{% url 'expense' %}"><img src="{% static 'images/expenses.png' %}" class="menu-icon"><span class="menu-text">Expense</span></a></li>
                    <li class="menu-item"><a href="{% url 'calendar' %}"><img src="{% static 'images/calendar.png' %}" class="menu-icon"><span class="menu-text">Calendar</span></a></li>
                    <li class="menu-item"><a href="{% url 'allproject' %}"><img src="{% static 'images/All projects.png' %}" class="menu-icon"><span class="menu-text">All Projects</span></a></li>
                    <li class="menu-item"><a href="{% url 'clientsbook' %}"><img src="{% static 'images/clientsbook.png' %}" class="menu-icon"><span class="menu-text">Clients Book</span></a></li>
                    <li class="menu-item"><a href="{% url 'clients' %}"><img src="{% static 'images/Clients.png' %}" class="menu-icon"><span class="menu-text">Clients</span></a></li>
                    <li class="menu-item"><a href="{% url 'status' %}"><img src="{% static 'images/status.png' %}" class="menu-icon"><span class="menu-text">Status</span></a></li>
                    <li class="menu-item"><a href="{% url 'pending_pay' %}"><img src="{% static 'images/pending.png' %}" class="menu-icon"><span class="menu-text">Pending Payments</span></a></li>
                    <li class="menu-item active"><a href="{% url 'project_map' %}"><img src="{% static 'images/map.png' %}" class="menu-icon"><span class="menu-text">Map</span></a></li>
                    <li class="menu-item"><a href="{% url 'assets' %}"><img src="{% static 'images/Assets.png' %}" class="menu-icon"><span class="menu-text">Assets</span></a></li>
                    <li class="menu-item"><a href="{% url 'budget' %}"><img src="{% static 'images/budget.png' %}" class="menu-icon"><span class="menu-text">Budget</span></a></li>
                    <li class="menu-item"><a href="{% url 'entertainment' %}"><img src="{% static 'images/Entertainment.png' %}" class="menu-icon"><span class="menu-text">Entertainment</span></a></li>
                </ul>
            </nav>
            <div class="profile-section" id="profileMenu">
                <img src="{% static 'images/profile.png' %}" alt="Profile" class="profile-icon">
                <span class="menu-text">Riktha L</span>
                <div class="dropdown" id="profileDropdown">
                    <ul>
                        <li><a href="{% url 'profile' %}">View Profile</a></li>
                        <li><a href="{% url 'logout_view' %}">Sign Out</a></li>
                    </ul>
                </div>
            </div>
        </aside>

        <main class="main-content">
            <div class="header">
                <h1>Location</h1>
                <div class="search-bar">
                    <input type="text" placeholder="Search...">
                    <button class="add-button" id="addButton">Add</button>
                </div>
            </div>
             <!-- Map Container with Scrollbars -->
            <div class="map-container">
                <div id="map"></div>
            </div>

   <!-- Add form -->
            <div class="form-container" id="projectForm">
                <button class="close-btn">&times;</button> <!-- Close Button -->
             <!-- add modal -->
             <h3>Add Project</h3>
                <form id = "addForm" action ="{% url 'projects' %}" method = "post">
                    {% csrf_token %}
                    <div class="form-group">
                        <label for="eventType">Type</label>
                        <select id="eventType" name="addProjectType" required>
                            <option value="">---</option>
                            <!-- dynamic display -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="addProjectStatus">Status:</label>
                           <select id="addProjectStatus" name="addProjectStatus" required>
                               <option value="">---</option>

                               <option value="Completed">Completed</option>
                               <option value="Pending">Pending</option>
                               <option value="Ongoing">Ongoing</option>
                               <option value="Cancelled">Cancelled</option>
                           </select>
                    </div>

                    <div class="form-group">
                        <label for="projectName">Project Name</label>
                        <input type="text" id="projectName" name="addProjectName" required>
                    </div>

                    <div class="form-group">
                        <label for="customerCompany">Customer Company</label>
                        <input type="text" id="customerCompany" name="addCustomerCompany" required>
                    </div>

                    <div class="form-group">
                        <label for="shootStart">Shoot Start</label>
                        <input type="datetime-local" id="shootStart" name="addShootStart" required>
                    </div>
                    <div class="form-group">
                        <label for="shootEnd">Shoot End</label>
                        <input type="datetime-local" id="shootEnd" name="addShootEnd" required>
                    </div>

                    <div class="form-group">
                        <label for="projectAmount">Project Amount</label>
                        <input type="number" id="projectAmount" name="addProjectAmount" required>
                    </div>


                    <div class="form-group">
                        <label for="projectLocation">Project Location</label>
                        <input type="text" id="projectLocation" name="addProjectLocation" required>
                    </div>
                    <!-- Hidden fields to store latitude and longitude -->
                    <input type="hidden" id="latitude" name="latitude">
                    <input type="hidden" id="longitude" name="longitude">

                    <!--sandhiya-->
                    <div class="form-group">
                        <label for="locationLink">Location Link</label>
                        <input type="text" id="locationLink" name="locationLink" placeholder="Enter Google Maps link">
                        <button type="button" onclick="getAddressFromLink()">Get Address</button>
                    </div>
                    <div class="form-group">
                        <label for="address">Extracted Address</label>
                        <input type="text" id="address" name="address" readonly>
                    </div>

                    <div class="form-group toggle-container">
                        <label for="outsourcing">Outsourcing</label>
                        <label class="toggle">
                            <input type="checkbox" id="outsourcing" name="addoutsourcing" >
                            <span class="sliderr"></span>
                        </label>
                    </div>

                    <div id="addOutsourcingDetails" style="display: none; margin-top: 10px;align-items: center;">
                        <label for="addOutsourcingFor">Outsourcing For</label>
                        <select id="addOutsourcingFor" name="addoutsourcingFor">
                            <option value="">---</option>

                            <option >Photo</option>
                            <option >Video</option>
                            <option >Editor</option>
                            <option >Drone</option>
                            <option >Pilot</option>
                        </select>

                        <label for="addOutsourcingAmount" style="margin-top: 10px;align-items: center;">Outsourcing Amount</label>
                        <input type="number" id="addOutsourcingAmount" name="addoutsourcingAmount">

                        <label for="addOutsourcingCustomer" style="margin-top: 10px;align-items: center;">Outsourcing Customer Name</label>
                        <select id="addOutsourcingCustomer" name="addOutsourcingCustomer">
                            <option value="">---</option>
                            <!-- dynamic clients display-->
                        </select>

                        <label for="addOutsourcingPaid" style="display: flex; align-items: center; margin-top: 10px;">
                            Outsourcing Paid
                            <label class="switch" style="margin-left: 200px;">
                                <input type="checkbox" id="addOutsourcingPaid" name="addoutsourcingPaid">
                                <span class="slider"></span>
                            </label>
                        </label>
                    </div>

                    <!--sandhiya-->

                </form>
            </div>
        </main>
    </div>
    <script>
        $.getScript("https://maps.googleapis.com/maps/api/js?key=AIzaSyAqIalCM0rqsXeHiyRP4ImBbVgqwMSv8D8&libraries=places")
        .done(function(script, textStatus) {
            google.maps.event.addDomListener(window, "load", initAutoComplete);
        });

        let autocomplete;

        function initAutoComplete() {
            autocomplete = new google.maps.places.Autocomplete(
                document.getElementById('projectLocation'),
                {
                    // No 'types' filter for broader searches (places, establishments, addresses, etc.)
                    componentRestrictions: {'country': 'in'} // Restrict to India (or change country if needed)
                }
            );

            autocomplete.addListener('place_changed', onPlaceChanged);
        }

        function onPlaceChanged() {
            var place = autocomplete.getPlace();

            if (!place.geometry) {
                document.getElementById('projectLocation').placeholder = "*Begin typing address or place name";
                return;
            }

            // Retrieve latitude and longitude
            var latitude = place.geometry.location.lat();
            var longitude = place.geometry.location.lng();

            // Populate hidden fields with latitude and longitude
            $('#latitude').val(latitude);
            $('#longitude').val(longitude);

            // Optionally, retrieve more address components as before
            var num = '', route = '', town = '', county = '', country = '', postalCode = '';
            for (var i = 0; i < place.address_components.length; i++) {
                for (var j = 0; j < place.address_components[i].types.length; j++) {
                    if (place.address_components[i].types[j] === "street_number") {
                        num = place.address_components[i].long_name;
                    }
                    if (place.address_components[i].types[j] === "route") {
                        route = place.address_components[i].long_name;
                    }
                    if (place.address_components[i].types[j] === "locality") {
                        town = place.address_components[i].long_name;
                    }
                    if (place.address_components[i].types[j] === "administrative_area_level_2") {
                        county = place.address_components[i].long_name;
                    }
                    if (place.address_components[i].types[j] === "country") {
                        country = place.address_components[i].long_name;
                    }
                    if (place.address_components[i].types[j] === "postal_code") {
                        postalCode = place.address_components[i].long_name;
                    }
                }
            }

            console.log(`Latitude: ${latitude}, Longitude: ${longitude}`);
            console.log(`Address: ${num} ${route}, Town: ${town}, Country: ${country}`);
        }


    </script>

    <script>
        let currentInfoWindow = null; // Variable to keep track of the currently open info window

        // Initialize the map centered around South India
        function initMap() {
            const southIndiaCenter = { lat: 12.9716, lng: 77.5946 }; // Center on Bangalore

            // Initialize the map
            const map = new google.maps.Map(document.getElementById('map'), {
                zoom: 6, // Zoom level to show South India
                center: southIndiaCenter, // Initial center at South India
                mapTypeId: 'roadmap', // Can also be 'satellite', 'hybrid', 'terrain'
                scrollwheel: true, // Enable zooming with mouse scroll wheel
                gestureHandling: 'auto' // Enable gesture handling (for touchpads and mobile devices)
            });

            // Sample project data (replace with dynamic data later)
            const projects = [
                { name: "Project A", location: { lat: 28.6139, lng: 77.2090 }, client: "Client A", coordinates: "28.6139, 77.2090" }, // New Delhi
                { name: "Project B", location: { lat: 12.9716, lng: 77.5946 }, client: "Client B", coordinates: "12.9716, 77.5946" }, // Bangalore
                { name: "Project C", location: { lat: 6.9271, lng: 79.8612 }, client: "Client C", coordinates: "6.9271, 79.8612" } // Colombo, Sri Lanka
            ];

            // Add markers to the map
            projects.forEach(project => {
                const marker = new google.maps.Marker({
                    position: project.location,
                    map: map,
                    title: project.name
                });

                // Generate the Google Static Map URL based on project location
                const staticMapUrl = `https://maps.googleapis.com/maps/api/staticmap?center=${project.location.lat},${project.location.lng}&zoom=14&size=400x300&maptype=roadmap&markers=color:red%7C${project.location.lat},${project.location.lng}&key=AIzaSyAqIalCM0rqsXeHiyRP4ImBbVgqwMSv8D8`;

                const infoWindowContent = `
                <div style="display: flex; flex-direction: column; width: 200px; font-family: Arial, sans-serif; text-align: left;">

                    <!-- Static Image (2/3 of the container), now clickable -->
                    <div style="flex: 2; display: flex; justify-content: center;">
                        <a href="https://www.google.com/maps?q=${project.location.lat},${project.location.lng}" target="_blank">
                            <img src="${staticMapUrl}" alt="Location View" style="width: 100%; height: auto; border-radius: 4px 4px 0 0; object-fit: cover; cursor: pointer;">
                        </a>
                    </div>

                    <!-- Project Details (1/3 of the container) -->
                    <div style="flex: 1; background-color: #f8f9fa; padding: 8px; border-radius: 0 0 4px 4px;">
                        <h3 style="font-size: 16px; margin: 0 0 4px;">${project.name}</h3>
                        <p style="font-size: 12px; margin: 0;">Client: ${project.client}</p>
                        <p style="font-size: 12px; margin: 0;">Coordinates: ${project.coordinates}</p>

                        <!-- Button to Navigate to Project Page -->
                        <div style="text-align: right; margin-top: 8px;">
                            <button onclick="window.location.href='${project.projectPage}'"
                                style="padding: 4px 8px; font-size: 12px; background-color: #000000; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                View Project
                            </button>
                        </div>
                    </div>

                </div>
            `;



                // Add info windows to markers
                const infoWindow = new google.maps.InfoWindow({
                    content: infoWindowContent
                });

                marker.addListener('click', function() {
                    if (currentInfoWindow) {
                        currentInfoWindow.close(); // Close the previously open info window
                    }
                    infoWindow.open(map, marker);
                    currentInfoWindow = infoWindow; // Set the current info window
                });
            });

            // Set initial view to display a location image
            const bounds = new google.maps.LatLngBounds();
            projects.forEach(project => {
                bounds.extend(project.location);
            });
            map.fitBounds(bounds); // Adjust map to fit all markers
        }

        document.getElementById('addButton').onclick = function() {
            document.getElementById('projectForm').style.display = 'block'; // Show the form on add button click
        };

        document.getElementById('projectForm').onsubmit = function(e) {
            e.preventDefault();
            alert('Form submitted successfully!');
            // Here you can add form submission logic (e.g., AJAX request)
        };

        document.getElementById('cancelButton').onclick = function() {
            var form = document.getElementById('projectForm');
            // Hide the form
            form.style.display = 'none';
            // Reset the form fields
            form.reset();
        };

        function selectStatus(status) {
            // Remove selected class from all buttons
            const buttons = document.querySelectorAll('.status-button');
            buttons.forEach(button => {
                button.classList.remove('selected'); // Remove selected class
                button.style.backgroundColor = "#cbcbcb"; // Reset to default color
            });
            // Add selected class to the clicked button
            const selectedButton = document.querySelector(`.status-button.${status}`);
            selectedButton.classList.add('selected');
            selectedButton.style.backgroundColor = "#4CAF50"; // Change color to green when selected
        }

        document.getElementById('useLocation').addEventListener('change', function() {
            if (this.checked) {
                // Check if geolocation is available
                if (navigator.geolocation) {
                    // Get current position
                    navigator.geolocation.getCurrentPosition(function(position) {
                        const latitude = position.coords.latitude;
                        const longitude = position.coords.longitude;
                        // Fill in the coordinates in the Project Location field
                        document.getElementById('projectLocation').value = `${latitude}, ${longitude}`;
                    }, function(error) {
                        alert('Error getting location: ' + error.message);
                    });
                } else {
                    alert("Geolocation is not supported by this browser.");
                }
            } else {
                // Clear the Project Location field if the toggle is turned off
                document.getElementById('projectLocation').value = '';
            }
        });

        document.querySelector('.close-btn').addEventListener('click', function() {
            // Hide the form when the close button is clicked
            document.getElementById('projectForm').style.display = 'none';
        });
    </script>
    <script>
        const sidebar = document.querySelector('.sidebar');
        const toggleIcon = document.getElementById('toggle-icon');

        toggleIcon.addEventListener('click', function() {
          if (sidebar.classList.contains('closed')) {
            sidebar.classList.remove('closed');
            toggleIcon.src = "{% static 'images/menuleft.png' %}";  // Change to the "left-chevron" when open
          } else {
            sidebar.classList.add('closed');
            toggleIcon.src = "{% static 'images/menuright.png' %}";  // Change to the "chevron" when closed
          }
        });
      </script>

        <script>// add form

            document.getElementById('addProjectBtn').addEventListener('submit', function(event) {
                event.preventDefault();

                var form = this;
                var formData = new FormData(form);

                fetch(form.action, {
                    method: form.method,
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    if (response.headers.get('content-type')?.includes('application/json')) {
                        return response.json();
                    } else {
                        return response.text().then(text => { throw new Error(text) });
                    }
                })
                .then(data => {
                    if (data.success) {
                        form.reset();
                        closeAddModal();
                        location.reload();
                    } else {
                        var errorMessage = document.getElementById("error-message");
                        errorMessage.textContent = data.error;
                        errorMessage.style.display = "block";
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById("error-message").textContent = 'An error occurred: ' + error.message;
                    document.getElementById("error-message").style.display = "block";
                });
            });


        </script>


        <script>
            document.addEventListener('DOMContentLoaded', function() {
                var dropdownIds = ['addProjectType'];  // Add the IDs of all the dropdowns here

                // Fetch type when the page loads
                fetch('/get-unique-types/')
                    .then(response => response.json())
                    .then(data => {
                        dropdownIds.forEach(function(dropdownId) {
                            var dropdown = document.getElementById(dropdownId);
                            dropdown.innerHTML = '';  // Clear existing options

                            // Populate each dropdown with the fetched types
                            data.forEach(function(type) {
                                var option = document.createElement('option');
                                option.value = type;
                                option.text = type;
                                dropdown.appendChild(option);
                            });
                        });
                    })
                    .catch(error => console.error('Error fetching type:', error));
            });
            </script>


<script> // outsourcing toogles  edit form
    function toggleOutsourcingDetails() {
        const outsourcingDetails = document.getElementById("outsourcingDetails");
        const toggle = document.getElementById("outsourcing");
        outsourcingDetails.style.display = toggle.checked ? "block" : "none";
    }
</script>

<script>
// add form
    function toggleAddOutsourcingDetails() {
        const outsourcingDetails = document.getElementById("addOutsourcingDetails");
        const toggle = document.getElementById("addOutsourcing");
        outsourcingDetails.style.display = toggle.checked ? "block" : "none";
    }
</script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        var dropdownIds = ['addOutsourcingCustomer'];  // Add the IDs of all the dropdowns here

        // Fetch type when the page loads
        fetch('/get-unique-client/')
            .then(response => response.json())
            .then(data => {
                dropdownIds.forEach(function(dropdownId) {
                    var dropdown = document.getElementById(dropdownId);
                    dropdown.innerHTML = '';  // Clear existing options

                    // Populate each dropdown with the fetched types
                    data.forEach(function(client) {
                        var option = document.createElement('option');
                        option.value = client;
                        option.text = client;
                        dropdown.appendChild(option);
                    });
                });
            })
            .catch(error => console.error('Error fetching type:', error));
    });

</script>

<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script>
        config={
            enableTime:true,

        }
        flatpickr("input[type=datetime-local]",config);
    </script>
    <script>
        document.getElementById('profileMenu').addEventListener('click', function(event) {
            event.stopPropagation(); // Prevents the click from bubbling up
            const dropdown = document.getElementById('profileDropdown');
            dropdown.style.display = dropdown.style.display === 'none' || dropdown.style.display === '' ? 'block' : 'none';
        });

        // Hide dropdown when clicking outside
        document.addEventListener('click', function() {
            const dropdown = document.getElementById('profileDropdown');
            dropdown.style.display = 'none';
        });
    </script>

    <div class="form-group">
        <label for="locationLink">Location Link</label>
        <input type="text" id="locationLink" name="locationLink" placeholder="Enter Google Maps link">
        <button type="button" onclick="getAddressFromLink()">Get Address</button>
    </div>
    <div class="form-group">
        <label for="address">Extracted Address</label>
        <input type="text" id="address" name="address" readonly>
    </div>

    <!-- Google Maps JavaScript API -->
    <div id="map" style="height: 400px; width: 100%;"></div>

    <script>
        async function getAddressFromLink() {
            const locationLink = document.getElementById("locationLink").value;
            const addressField = document.getElementById("address");

            // Extract latitude and longitude from the link
            const coordinates = extractCoordinates(locationLink);

            if (coordinates) {
                try {
                    const address = await fetchAddressFromApi(coordinates);
                    addressField.value = address;
                    showOnMap(coordinates); // Show the location on the map
                } catch (error) {
                    console.error('Error fetching address:', error);
                    addressField.value = 'Error fetching address';
                }
            } else {
                addressField.value = 'Invalid link';
            }
        }

        function extractCoordinates(link) {
            // Regex to extract latitude and longitude from the Google Maps link
            const regex = /@(-?\d+\.\d+),(-?\d+\.\d+)/;
            const match = link.match(regex);
            if (match) {
                return { lat: parseFloat(match[1]), lng: parseFloat(match[2]) };
            }
            return null;
        }

        async function fetchAddressFromApi(coordinates) {
            const apiKey = 'YOUR_API_KEY';  // Replace with your actual API key
            const { lat, lng } = coordinates;

            // Call the Geocoding API using the latitude and longitude
            const response = await fetch(`https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${apiKey}`);
            const data = await response.json();

            if (data.status === 'OK' && data.results.length > 0) {
                return data.results[0].formatted_address;  // Return the formatted address
            } else {
                throw new Error('Address not found');
            }
        }

        function showOnMap(coordinates) {
            const apiKey = 'YOUR_API_KEY';  // Replace with your actual Google Maps JavaScript API key

            // Load the Google Maps JavaScript API
            const script = document.createElement('script');
            script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&callback=initMap`;
            script.async = true;
            document.head.appendChild(script);

            window.initMap = function () {
                const map = new google.maps.Map(document.getElementById('map'), {
                    zoom: 12,
                    center: coordinates
                });
                new google.maps.Marker({
                    map: map,
                    position: coordinates
                });
            };
        }
    </script>


</body>
</html>