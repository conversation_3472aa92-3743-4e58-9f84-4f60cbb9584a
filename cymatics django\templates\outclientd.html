{% load static %}
{% static "images" as baseurl %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Client Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            height: 100vh;
            flex-direction: column;
        }

        .container {
            display: flex;
            width: 100%;
            flex-grow: 1;
        }

        .sidebar {
            background-color: #1e1e1e;
            color: white;
            width: 250px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
        }

        .sidebar .logo {
            padding: 20px;
            text-align: center;
        }

        .menu-title {
            padding: 10px 0;
            text-align: center;
        }

        .sidebar nav ul {
            list-style: none;
            padding: 0;
            width: 100%;
        }

        .sidebar nav ul li {
            padding: 12px 20px;
            cursor: pointer;
            transition: background-color 0.3s, color 0.3s, border-left 0.3s;
            text-align: left;
            display: flex;
            align-items: center;
        }

        .sidebar nav ul li:hover {
            background-color: #333;
            color: #fff;
            border-left: 4px solid #fff;
        }

        .menu-icon {
            margin-right: 10px;
            width: 24px;
            height: 24px;
        }

        .main-content {
            flex-grow: 1;
            background-color: #f1f1f1;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .client-info {
            background-color: #ffffff;
            padding: 20px;
            margin-bottom: 20px;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .client-info h1 {
            margin: 0;
            font-size: 24px;
        }

        .client-info p {
            margin: 5px 0;
            font-size: 18px;
        }

        .client-info .contact-section {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }

        .client-info .contact-details {
            flex-grow: 1;
            margin-right: 10px;
        }

        .client-info .client-actions {
            display: flex;
            gap: 15px;
        }

        .client-actions button {
            padding: 10px 20px;
            border: 1px solid #ddd;
            border-radius: 25px;
            cursor: pointer;
            display: flex;
            align-items: center;
            background-color: #f0f0f0;
            color: #333;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            transition: background-color 0.3s ease, transform 0.2s ease;
        }

        .client-actions button:hover {
            background-color: #e0e0e0;
            transform: translateY(-2px);
        }

        .client-actions button img {
            margin-right: 10px;
            width: 16px;
            height: 16px;
        }

        .client-balance {
            font-size: 48px;
            font-weight: bold;
            text-align: center;
            width: 100%;
            margin: 20px 0;
        }

        .projects-section {
            background-color: #1e1e1e;
            color: white;
            padding: 20px;
        }

        .projects-section h2 {
            margin-top: 0;
        }

        .project-card {
            background-color: #333;
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-top: 10px;
            display: flex;
            align-items: center;
            transition: background-color 0.3s ease, transform 0.2s ease;
            cursor: pointer;
        }

        .project-card:hover {
            background-color: #444;
            transform: translateY(-2px);
        }

        .project-card img {
            margin-right: 10px;
            width: 50px;
            height: 50px;
            border-radius: 5px;
        }

        .project-card .arrow-icon {
            margin-left: auto;
            width: 10px;
            height: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <aside class="sidebar">
            <div class="logo">
                <img src="{% static './images/logowhite.png' %}" alt="logo" width="50" height="50">
            </div>
            <nav>
                <ul>
                    <li><a href="{% url 'dashboard' %}"><img src="{% static './images/dashboard.png' %}" alt="dashboard icon" class="menu-icon">Dashboard</a></li>
                    <li><a href="{% url 'projects' %}"><img src="{% static './images/Project.png' %}" class="menu-icon">Project</a></li>
                    <li><a href="{% url 'incomef_view' %}"><img src="{% static './images/Income.png' %}" alt="Income Icon" class="menu-icon">Income</a></li>
                    <li><a href="{% url 'expense' %}"><img src="{% static './images/expenses.png' %}" alt="Expenses Icon" class="menu-icon">Expense</a></li>
                    <li><a href="{% url 'calendar_view' %}"><img src="{% static './images/calendar.png' %}" alt="Calendar Icon" class="menu-icon">Calendar</a></li>
                    <li><a href="{% url 'allproject' %}"><img src="{% static './images/All projects.png' %}" alt="All Projects Icon" class="menu-icon">All Projects</a></li>
                    <li><a href="{% url 'clientsbook' %}"><img src="{% static './images/Client books.png' %}" alt="Clients Book Icon" class="menu-icon">Clients Book</a></li>
                    <li><a href="{% url 'clients' %}"><img src="{% static './images/Clients.png' %}" alt="Clients Icon" class="menu-icon">Clients</a></li>
                    <li><a href="{% url 'status' %}"><img src="{% static './images/Status.png' %}" alt="Status Icon" class="menu-icon">Status</a></li>
                    <li><a href="{% url 'pending_pay' %}"><img src="{% static './images/pending.png' %}" alt="Pending Payments Icon" class="menu-icon">Pending Payments</a></li>
                    <li><a href="{% url 'project_map' %}"><img src="{% static './images/maps-and-flags.png' %}" alt="Map Icon" class="menu-icon">Map</a></li>
                    <li><a href="{% url 'assets' %}"><img src="{% static './images/Assets.png' %}" alt="Assets Icon" class="menu-icon">Assets</a></li>
                    <li><a href="{% url 'budget' %}"><img src="{% static './images/budget.png' %}" alt="Budget Icon" class="menu-icon">Budget</a></li>
                    <li><a href="{% url 'entertainment' %}"><img src="{% static './images/Entertainment.png' %}" src= alt="Entertainment Icon" class="menu-icon">Entertainment</a></li>
                </ul>
            </nav>
        </aside>
        <main class="main-content">
            <div class="client-info">
                <div class="contact-section">
                    <div class="contact-details">
                        <h1>{{client.company}}</h1>
                        <p><strong>{{client.name}}</strong></p>
                        <p>{{client.number}}</p>
                        <p>{{client.email}}</p>
                    </div>
                    <div class="client-actions">
                        <button><img src="C:/Users/<USER>/Downloads/call.png" alt="Call Icon">Call</button>
                        <button><img src="C:/Users/<USER>/Downloads/sms.png" alt="SMS Icon">SMS</button>
                        <button><img src="C:/Users/<USER>/Downloads/email.png" alt="Email Icon">Email</button>
                    </div>
                </div>

                <!-- total project value-->
                <div class="client-balance">
                    {{total}}

                </div>

<!-- project counts -->
                {{count}}

            </div>

            <!-- project list -->
            <div class="projects-section">
                <h2>Projects</h2>
                {% if projects %}

                {% for obj in projects %}
                <div class="project-card" onclick="moveToProjectPage('{{ obj.code }}')">
                    {% if obj.image %}
                        <img src="{{obj.image.url}}" alt="Project Icon">
                    {% else %}
                        <img src="{% static 'images/Project.png' %}" alt="Project Icon">
                    {% endif %}
                    <div>
                        <p>{{obj.name}}</p>
                        <p>{{obj.pending_amt}}</p>
                        <p>₹{{obj.amount}}</p>
                    </div>
                    <img src="C:\Users\<USER>\Downloads\right-arrow (2).png" alt="Right Arrow" class="arrow-icon">
                </div>
                {% endfor %}

                {% else %}
        <p>No projects found for this client.</p>
                {% endif %}



            </div>
        </main>
    </div>

    <script>
        // page navigation from div
    function moveToProjectPage(code) {
        window.location.href = "/project/" + code + "/";
    }
    </script>


</body>
</html>