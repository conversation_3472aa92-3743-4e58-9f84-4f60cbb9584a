{% load static %}
{% static "images" as baseurl %}
<!doctype html>
<html>
    <head>
        <meta charset="UTF-8">
	    <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link rel="stylesheet" type="text/css" href="https://npmcdn.com/flatpickr/dist/themes/dark.css">
        <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

	    <title>cymatics</title>
        <link rel="stylesheet" href="{% static './css/pending_pay.css' %}">
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

        </head>
        <style>
            .modal {
                display: none;
                position: fixed;
                z-index: 1;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                overflow: auto;
                background-color: rgba(0,0,0,0.4);
            }

            .modal-content {
                background-color: #ffffff;
                margin: 10% auto;
                padding: 20px;
                border: 1px solid #888;
                width: 420px;
                border-radius: 10px;
            }

            .close {
                color: #aaa;
                float: right;
                font-size: 28px;
                font-weight: bold;
            }

            .close:hover,
            .close:focus {
                color: black;
                text-decoration: none;
                cursor: pointer;
            }

            #editForm, #addForm {
                display: flex;
                flex-direction: column;
            }

            #editForm label, #addForm label {
                margin-top: 10px;
                font-size:16px;
                font-weight: 510;


            }

            #editForm input, #editForm select,
            #addForm input, #addForm select {
                padding: 8px;
                margin-top: 5px;
                border: 1px solid #ccc;
                border-radius: 4px;
            }

            #editForm button, #addForm button {
                padding: 10px;
                margin-top: 20px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
            }

.form-actions .submit {
 background-color: rgb(0, 0, 0);
 color: white;
}

 .form-actions .button {
 background-color: #ccc;
}


            #editForm button:hover, #addForm button:hover {
                background-color: #555;
            }

            .form-actions {
                display: flex;
                justify-content: flex-end;
                margin-top: 20px;
            }

            .form-actions button {
                margin-left: 10px;
            }

            /* Outsourcing Section Styles */
            #outsourcingDetails {
                margin-top: 10px;
            }

            #outsourcingDetails label {
                display: block;
                margin-top: 10px;
                font-weight: medium;
            }

            #outsourcingDetails select,
            #outsourcingDetails input[type="number"] {
                width: 100%;
                padding: 8px;
                margin-top: 5px;
                border: 1px solid #ccc;
                border-radius: 4px;
            }

            #addOutsourcingDetails {
                margin-top: 10px;
            }

            #addOutsourcingDetails label {
                display: block;
                margin-top: 10px;
                font-weight: medium;
            }

            #addOutsourcingDetails select,
            #addOutsourcingDetails input[type="number"] {
                width: 100%;
                padding: 8px;
                margin-top: 5px;
                border: 1px solid #ccc;
                border-radius: 4px;
            }

            .switch {
                position: relative;
                display: inline-block;
                width: 34px;
                height: 20px;
            }

            .switch input {
                opacity: 0;
                width: 0;
                height: 0;
            }

            .slider {
                position: absolute;
                cursor: pointer;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: #ccc;
                transition: .4s;
                border-radius: 20px;
            }

            .slider:before {
                position: absolute;
                content: "";
                height: 16px;
                width: 16px;
                left: 2px;
                bottom: 2px;
                background-color: white;
                border-radius: 50%;
                transition: .4s;
            }

            input:checked + .slider {
                background-color: #000000;
            }

            input:checked + .slider:before {
                transform: translateX(14px);
            }

            .modal-content h2 {
            text-align: center;
            margin-bottom: 20px;
            font-size:19px;
           }

        #outsourcingAmount {
    width: 100% !important; /* Ensure this width is applied */
    max-width: 100% !important; /* Limit the width */
    box-sizing: border-box; /* Ensure padding is included in the width */
}
.search-bar{
display: flex;
align-items: center;
}
.search-bar input {
padding:15px 20px;
border: 1px solid #a1a1a1;
border-radius: 5px;
height:2px;
font-size: 18px;  /* Increase font size */
width: 210px;  /* Increase the width of the input */

}
.completed-list {
    background-color: #fff;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    margin-bottom:20px;
}
.dropdown-content {
    display: none;
    position: absolute;
    right: 0;
    background-color: #f9f9f9;
    min-width: 140px;
    box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.2);
    z-index: 1;
    border-radius: 8px;

}
.dropdown-content button {
    color: black;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    background: none;
    border: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    font-weight: 400;
    font-size:15px;
}

.btn-check {
    display: none; /* Hide the checkbox */
}

.btn-primary {
    background-color: #ffffff !important; /* Ensure white background */
    color: black !important; /* Ensure black text */
    border: 2px solid #ccc;
    border-radius: 10px;
    padding: 10px 15px;
    cursor: pointer;
    margin: 2px; /* Add margin to create gaps */
    transition: background-color 0.3s, color 0.3s, border-color 0.3s, box-shadow 0.3s;
    font-weight: bold;
    font-size: 12px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.btn-primary.active {
    background-color: #000000; /* Even darker blue */
    border-color: #000000; /* Keep the border color */
    color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

#statusButtons {
    display: flex; /* Align buttons in a row */
    justify-content: flex-start; /* Align to the start */
}
.user-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: #ddd;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    font-size: 18px;
    color: #0e0e0e;
    background-color: #e1ecb8;

}
.profile-section {
    position: relative;
    padding: 12px 20px;
    cursor: pointer;
    transition: background-color 0.3s, border-left 0.3s;
   }
   .profile-section:hover {
    background-color: #555;
    border-left: 4px solid #ffcc00;
   }
   .dropdown {
    position: absolute;
    bottom: 100%;
    left: 0;
    background-color: white;
    border: 1px solid #ccc;
    border-radius: 4px;
    z-index: 1000;
    width: 160px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: none;
   }
   .dropdown li {
    padding: 10px;
    color: black;
    cursor: pointer;
   }
   .dropdown li:hover {
    background-color: #f1f1f1;
   }

        </style>
    <body>

        <div class="container">
            <aside class="sidebar">
                <div class="toggle-icon">
                    <img src="{% static 'images/menuleft.png' %}" alt="icon" id="toggle-icon" width="16" height="16">
                </div>
                <div class="logo">
                    <img src="{% static './images/logowhite.png' %}" alt="logo" width="50" height="50">
                </div>
                <nav>
                    <ul>
                        <li class="menu-item"><a href="{% url 'dashboard' %}"><img src="{% static 'images/dashboard.png' %}" class="menu-icon"><span class="menu-text">Dashboard</span></a></li>
                        <li class="menu-item"><a href="{% url 'projects' %}"><img src="{% static 'images/project.png' %}" class="menu-icon"><span class="menu-text">Project</span></a></li>
                        <li class="menu-item"><a href="{% url 'incomef_view' %}"><img src="{% static 'images/income.png' %}" class="menu-icon"><span class="menu-text">Income</span></a></li>
                        <li class="menu-item"><a href="{% url 'expense' %}"><img src="{% static 'images/expenses.png' %}" class="menu-icon"><span class="menu-text">Expense</span></a></li>
                        <li class="menu-item"><a href="{% url 'calendar' %}"><img src="{% static 'images/calendar.png' %}" class="menu-icon"><span class="menu-text">Calendar</span></a></li>
                        <li class="menu-item"><a href="{% url 'allproject' %}"><img src="{% static 'images/All projects.png' %}" class="menu-icon"><span class="menu-text">All Projects</span></a></li>
                        <li class="menu-item"><a href="{% url 'clientsbook' %}"><img src="{% static 'images/clientsbook.png' %}" class="menu-icon"><span class="menu-text">Clients Book</span></a></li>
                        <li class="menu-item"><a href="{% url 'clients' %}"><img src="{% static 'images/Clients.png' %}" class="menu-icon"><span class="menu-text">Clients</span></a></li>
                        <li class="menu-item"><a href="{% url 'status' %}"><img src="{% static 'images/status.png' %}" class="menu-icon"><span class="menu-text">Status</span></a></li>
                        <li class="menu-item active"><a href="{% url 'pending_pay' %}"><img src="{% static 'images/pending.png' %}" class="menu-icon"><span class="menu-text">Pending Payments</span></a></li>
                        <li class="menu-item"><a href="{% url 'project_map' %}"><img src="{% static 'images/map.png' %}" class="menu-icon"><span class="menu-text">Map</span></a></li>
                        <li class="menu-item"><a href="{% url 'assets' %}"><img src="{% static 'images/Assets.png' %}" class="menu-icon"><span class="menu-text">Assets</span></a></li>
                        <li class="menu-item"><a href="{% url 'budget' %}"><img src="{% static 'images/budget.png' %}" class="menu-icon"><span class="menu-text">Budget</span></a></li>
                        <li class="menu-item"><a href="{% url 'entertainment' %}"><img src="{% static 'images/Entertainment.png' %}" class="menu-icon"><span class="menu-text">Entertainment</span></a></li>
                    </ul>
                </nav>
                <div class="profile-section" id="profileMenu">
                    <div class="user-icon" id="userIcon">
                      <!-- Default content in case JS is not available -->
                      U
                  </div>

                    <span class="menu-text" id="name">{{ user.username }}</span>
                    <div class="dropdown" id="profileDropdown">
                        <ul>
                            <li><a href="{% url 'profile' %}">View Profile</a></li>
                            <li><a href="{% url 'logout_view' %}">Sign Out</a></li>
                        </ul>
                    </div>
                  </div>
            </aside>
        <div class="main-content">
            <div class="header">
                <h1>Pending Payments</h1>

    <!-- search box -->
    <form method="post" action="{% url 'pending_pay' %}">
        {% csrf_token %}
        <div class="search-bar">
        <input type="text" name="q" placeholder="Search" value="{{ query }}">
        </div>
    </form>
</div>


        <div class="completed-list">
            {% for item in objs%}
            <div class="payment-item" onclick="moveToProjectPage('{{ item.code }}')" style="cursor: pointer;">
                <div class="payment-item-details">

                    <div>
                        <div class="payment-item-title">{{item.name}}</div>
                        <div class="payment-item-subtitle">{{item.company}}</div>
                        <div class="payment-item-subtitle">{{item.pending_amt}}</div>

                    </div>
                </div>
                <button class="call-button">
                    <img src="{% static 'images/p-call.png' %}" alt="Call Icon" style="width: 16px; height: 16px;" style="margin-right: 5px;">
                    Call
                </button>

                <div class="dropdown">
                    <button class="dropdown-button">⋮</button>
                    <div class="dropdown-content">
                        <button class="edit-btn" data-code="{{ item.code }}">Edit</button>
                        <button class="delete-btn" data-id = "{{ item.id }}">Delete</button>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>


   <!-- Modal for Edit Form -->
<div id="editModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeEditModal()">&times;</span>
        <h2>Edit</h2>
        <form id="editForm" method="post">
            <label for="projectType">Type</label>
            <select id="projectType" name="projectType" required>
                <option value="">---</option>
                <!-- dynamic type dispaly -->
            </select>

            <label for="status">Status</label>
            <div id="statusButtons">
                <input type="checkbox" class="btn-check" id="addcompletedBtn" autocomplete="off" name="addstatus" value="COMPLETED" onclick="toggleStatus(this)">
                <label class="btn btn-primary" for="addcompletedBtn">Completed</label>

                <input type="checkbox" class="btn-check" id="addongoingBtn" autocomplete="off"   name="addstatus" value="ONGOING" onclick="toggleStatus(this)">
                <label class="btn btn-primary" for="addongoingBtn">Ongoing</label>

                <input type="checkbox" class="btn-check" id="addpendingBtn" autocomplete="off"  name="addstatus" value="PENDING" onclick="toggleStatus(this)">
                <label class="btn btn-primary" for="addpendingBtn">Pending</label>

                <input type="checkbox" class="btn-check" id="addcancelledBtn" autocomplete="off" name="addstatus" value="CANCELLED" onclick="toggleStatus(this)">
                <label class="btn btn-primary" for="addcancelledBtn">Cancelled</label>
            </div>

              <input type="hidden" id="statusHiddenInput" name="status" value="">
            <label for="projectName">Project Name</label>
            <input type="text" id="projectName" name="projectName" required>

            <label for="customerCompany">Customer Company</label>
            <input type="text" id="customerCompany" name="customerCompany" required>

            <label for="shootStart">Shoot Start</label>
            <input type="datetime-local" id="shootStart" name="shootStart" required>

            <label for="shootEnd">Shoot End</label>
            <input type="datetime-local" id="shootEnd" name="shootEnd" required>

            <label for="projectAmount">Project Amount</label>
            <input type="number" id="projectAmount" name="projectAmount" required>

            <label for="projectLocation">Project Location</label>
            <input type="text" id="projectLocation" name="projectLocation" required>

            <label for="outsourcing" style="display: flex; align-items: center;">
                Outsourcing
                <label class="switch" style="margin-left: 295px;">
                    <input type="checkbox" id="outsourcing" name="outsourcing" onchange="toggleOutsourcingDetails()">
                    <span class="slider"></span>
                </label>
            </label>

            <div id="outsourcingDetails" style="display: none; margin-top: 10px;">
                <label for="outsourcingFor">Outsourcing For</label>
                <select id="outsourcingFor" name="outsourcingFor">
                    <option >---</option>

                    <option >Photo</option>
                    <option >Video</option>
                    <option >Editor</option>
                    <option >Drone</option>
                    <option >Pilot</option>
                </select>

                <label for="outsourcingAmount" style="margin-top: 10px;white-space:nowrap;">Outsourcing Amount</label>
                <input type="number" id="outsourcingAmount" name="outsourcingAmount">

                <label for="outsourcingCustomer" style="margin-top: 10px;">Outsourcing Customer Name</label>
                <select id="outsourcingCustomer" name="outsourcingCustomer">
                    <option value="">---</option>
                    <!-- dynamic clients display-->
                </select>

                <label for="outsourcingPaid" style="display: flex; align-items: center; margin-top: 10px;white-space:nowrap;">
                    Outsourcing Paid
                    <label class="switch" style="margin-left: 255px;">
                        <input type="checkbox" id="outsourcingPaid" name="outsourcingPaid">
                        <span class="slider"></span>
                    </label>
                </label>
            </div>
            <label for="reference">Reference</label>
            <input type="text" id="reference" name="reference">
            <div class="form-actions">
                <button class="submit" type="submit">Submit</button>
                <button class="button" type="button" id="cancelBtn" onclick="closeEditModal()">Cancel</button>
            </div>
        </form>
    </div>
</div>


<script>
    // Get the modal
var modal = document.getElementById("editModal");

// Get the button that opens the modal
var editButtons = document.querySelectorAll(".dropdown-content button:first-child");

// Get the <span> element that closes the modal
var span = document.getElementsByClassName("close")[0];

// When the user clicks on the edit button, open the modal
editButtons.forEach(button => {
    button.onclick = function() {
        modal.style.display = "block";
    };
});

// When the user clicks on <span> (x), close the modal
span.onclick = function() {
    modal.style.display = "none";
}

// When the user clicks anywhere outside of the modal, close it
window.onclick = function(event) {
    if (event.target == modal) {
        modal.style.display = "none";
    }
}

// Function to submit the edit form
function submitEditForm() {
    // Add logic to handle form submission
    alert("Form Submitted!");
    modal.style.display = "none";
}

// Function to cancel the edit
function cancelEditForm() {
    modal.style.display = "none";
}

// Function to handle delete
function deletePaymentItem(event) {
    var paymentItem = event.target.closest('.payment-item');
    paymentItem.remove();
}

// Attach delete event to all delete buttons
var deleteButtons = document.querySelectorAll(".dropdown-content button:last-child");
deleteButtons.forEach(button => {
    button.onclick = deletePaymentItem;
});

</script>


<script> // edit form
    $(document).ready(function() {
        var modal = $('#editModal');
        var span = $('.close');
        var cancelBtn = $('#cancelBtn');


        $.ajaxSetup({
            beforeSend: function(xhr, settings) {
                if (!this.crossDomain) {
                    xhr.setRequestHeader("X-CSRFToken", getCookie('csrftoken'));
                }
            }
        });

        function getCookie(name) {
            var cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                var cookies = document.cookie.split(';');
                for (var i = 0; i < cookies.length; i++) {
                    var cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }



        // When the user clicks on <span> (x) or cancel button, close the modal
        span.on('click', function() {
            modal.hide();
        });

        cancelBtn.on('click', function() {
            modal.hide();
        });



        // Function to open modal and fetch data
        function openModal(code) {

            function convertToDateTimeLocalDMY(dateStr) {
                console.log("edit form function");
                const date = new Date(dateStr);
                const pad = (num) => (num < 10 ? '0' + num : num);

                // Ensure correct format for 'dd-mm-yyyyTHH:MM'
                const localDateTime = pad(date.getDate()) + '-' +  // Day first
                                      pad(date.getMonth() + 1) + '-' +  // Month second
                                      date.getFullYear() + ' ' +  // Year third
                                      pad(date.getHours()) + ':' +  // Hours
                                      pad(date.getMinutes());  // Minutes

                return localDateTime;
            }




            function setStatusForEditForm(status) {
                console.log("Setting status for edit form:", status);
                var buttons = document.querySelectorAll('#statusButtons .btn-check');

                buttons.forEach(function(btn) {
                    if (btn.value === status) {
                        btn.checked = true;  // Check the correct checkbox
                        var label = document.querySelector('label[for="' + btn.id + '"]');
                        label.classList.add('active');  // Highlight this button
                        console.log("Checking button for status:", btn.value);
                    } else {
                        btn.checked = false;  // Uncheck other buttons
                        var label = document.querySelector('label[for="' + btn.id + '"]');
                        label.classList.remove('active');  // Remove highlight from others
                        console.log("Unchecking button:", btn.value);
                    }
                });
            }


            if (code) {
                $.ajax({
                    url: '/get_model_data/' + code + '/',
                    method: 'GET',
                    success: function(data) {


                        $('#editForm').attr('data-edit-code', code); // Set the edit code
                        $('#projectType').val(data.projectType).change();
                        $('#projectName').val(data.projectName);
                        $('#customerCompany').val(data.customerCompany);
                        $('#shootStart').val(convertToDateTimeLocalDMY(data.shootStart));
                        $('#shootEnd').val(convertToDateTimeLocalDMY(data.shootEnd));
                        $('#projectAmount').val(data.projectAmount);
                        $('#projectLocation').val(data.projectLocation);
                        $('#address').val(data.address);
                        $('#outsourcing').prop('checked', data.outsourcing);

                        if (data.outsourcing) {
                            $('#outsourcingDetails').show(); // Show the outsourcing details if the checkbox is checked
                        } else {
                            $('#outsourcingDetails').hide(); // Hide the outsourcing details if not checked
                        }

                        $('#reference').val(data.reference);
                        $('#outsourcingFor').val(data.outfor).change();
                        $('#outsourcingAmount').val(data.outamt);
                        $('#outsourcingCustomer').val(data.outcus).change();
                        $('#outsourcingPaid').prop('checked', data.outpaid);

                        console.log("just before the function...")
                        setStatusForEditForm(data.projectStatus);

                        modal.show();
                    },
                    error: function() {
                        alert('Failed to fetch data. Please try again.');
                    }
                });
            } else {
                $('#editForm').removeAttr('data-edit-code'); // Clear the edit code for new projects
                modal.show();
            }
        }
        // Attach click event to edit buttons
        $('.edit-btn').on('click', function(event) {
            event.stopPropagation();
            event.preventDefault(); // Prevent default link behavior
            var code = $(this).data('code'); // Get the project code from the button
            openModal(code);
        });

        $('#editForm').on('submit', function(event) {
            event.preventDefault();

            // Convert the date from dd-mm-yyyy to yyyy-mm-dd
            var dateField = $('#shootStart');  // Select the date field
            var dateValue = dateField.val();
            var convertedDate = convertToBackendDateTime(dateValue);
            dateField.val(convertedDate);  // Set the new converted date value back to the field


            // Convert the date from dd-mm-yyyy to yyyy-mm-dd
            var dateField = $('#shootEnd');  // Select the date field
            var dateValue = dateField.val();
            var convertedDate = convertToBackendDateTime(dateValue);
            dateField.val(convertedDate);  // Set the new converted date value back to the field

            var code = $('#editForm').attr('data-edit-code'); // Get the edit code
            var url = '/edit_model/' + (code ? code + '/' : ''); // Ensure URL includes code if available

            var formData = $(this).serializeArray(); // Serialize the form data
            var statusValue; // Variable to hold the selected status

    // Find the checked checkbox for the status
    $('#statusButtons .btn-check:checked').each(function() {
        statusValue = $(this).val(); // Get the value of the checked checkbox
    });

    if (statusValue) {
        // Add the selected status to the form data
        formData.push({ name: 'projectStatus', value: statusValue });
    }


            $.ajax({
                url: url,
                method: 'POST',
                data: formData,


                success: function(response) {
                    if (response.success) {
                        alert('Form submitted successfully!');
                        modal.hide();
                        location.reload();  // Reload the page to reflect changes
                    } else {
                        alert('Failed to submit form: ' + response.error);
                    }
                },
                error: function() {
                    alert('An error occurred. Please try again.');
                }
            });
        });
    });

</script>

<script>
    // page navigation from div
    function moveToDetailPage(code) {
        window.location.href = "/project/" + code + "/";
    }
</script>

<script> // deletion

    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.delete-btn').forEach(button => {
            button.addEventListener('click', function(event) {
                event.preventDefault();
                event.stopPropagation();
                const projectId = this.getAttribute('data-id');
                if (confirm('Are you sure you want to delete this project?')) {
                    fetch(`/delete_project/${projectId}/`, {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': getCookie('csrftoken'),
                        },
                    }).then(response => {
                        if (response.ok) {
                            window.location.reload();  // Refresh the page to reflect changes
                        } else {
                            alert('Failed to delete the project.');
                        }
                    }).catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred while deleting the project.');
                    });
                }
            });
        });

        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    });
</script>

<script>
    function toggleOutsourcingDetails() {
        const outsourcingDetails = document.getElementById("outsourcingDetails");
        const toggle = document.getElementById("outsourcing");
        outsourcingDetails.style.display = toggle.checked ? "block" : "none";
    }
</script>

<script>
     //type dynamic dropdown
     document.addEventListener('DOMContentLoaded', function() {
        var TypeDropdown = document.getElementById('projectType');

        // Fetch type when the page loads
        fetch('/get-unique-types/')
            .then(response => response.json())
            .then(data => {
                data.forEach(function(type) {
                    var option = document.createElement('option');
                    option.value = type;
                    option.text = type;
                    TypeDropdown.appendChild(option);
                });
            })
            .catch(error => console.error('Error fetching type:', error));
    });
</script>

<script>
    //client dynamic dropdown
    document.addEventListener('DOMContentLoaded', function() {
       var TypeDropdown = document.getElementById('outsourcingCustomer');

       // Fetch type when the page loads
       fetch('/get-unique-client/')
           .then(response => response.json())
           .then(data => {
               data.forEach(function(client) {
                   var option = document.createElement('option');
                   option.value = client;
                   option.text = client;
                   TypeDropdown.appendChild(option);
               });
           })
           .catch(error => console.error('Error fetching type:', error));
   });
</script>

<script>
    // page navigation from div
    function moveToProjectPage(code) {
        window.location.href = "/project/" + code + "/";
    }
</script>
<script>
    const sidebar = document.querySelector('.sidebar');
    const toggleIcon = document.getElementById('toggle-icon');

    toggleIcon.addEventListener('click', function() {
      if (sidebar.classList.contains('closed')) {
        sidebar.classList.remove('closed');
        toggleIcon.src = "{% static 'images/menuleft.png' %}";  // Change to the "left-chevron" when open
      } else {
        sidebar.classList.add('closed');
        toggleIcon.src = "{% static 'images/menuright.png' %}";  // Change to the "chevron" when closed
      }
    });
</script>
<script>
    config = {
        enableTime: true,  // Enable time picker
        dateFormat: "d-m-Y H:i",  // Format for datetime-local (ISO format without seconds)
    };
    flatpickr("input[type=datetime-local]", config);
</script>

<!-- Places Autocomplete -->

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script>
    $.getScript("https://maps.googleapis.com/maps/api/js?key=AIzaSyAqIalCM0rqsXeHiyRP4ImBbVgqwMSv8D8&libraries=places")
    .done(function(script, textStatus) {
        google.maps.event.addDomListener(window, "load", initAutoComplete);
    });

    let autocomplete;

    function initAutoComplete() {
        autocomplete = new google.maps.places.Autocomplete(
            document.getElementById('projectLocation'),
            {
                // No 'types' filter for broader searches (places, establishments, addresses, etc.)
                componentRestrictions: {'country': 'in'} // Restrict to India (or change country if needed)
            }
        );

        autocomplete.addListener('place_changed', onPlaceChanged);
    }

    function onPlaceChanged() {
        var place = autocomplete.getPlace();

        if (!place.geometry) {
            document.getElementById('projectLocation').placeholder = "*Begin typing address or place name";
            return;
        }

        // Retrieve latitude and longitude
        var latitude = place.geometry.location.lat();
        var longitude = place.geometry.location.lng();

        // Populate hidden fields with latitude and longitude
        $('#latitude').val(latitude);
        $('#longitude').val(longitude);

        // Optionally, retrieve more address components as before
        var num = '', route = '', town = '', county = '', country = '', postalCode = '';
        for (var i = 0; i < place.address_components.length; i++) {
            for (var j = 0; j < place.address_components[i].types.length; j++) {
                if (place.address_components[i].types[j] === "street_number") {
                    num = place.address_components[i].long_name;
                }
                if (place.address_components[i].types[j] === "route") {
                    route = place.address_components[i].long_name;
                }
                if (place.address_components[i].types[j] === "locality") {
                    town = place.address_components[i].long_name;
                }
                if (place.address_components[i].types[j] === "administrative_area_level_2") {
                    county = place.address_components[i].long_name;
                }
                if (place.address_components[i].types[j] === "country") {
                    country = place.address_components[i].long_name;
                }
                if (place.address_components[i].types[j] === "postal_code") {
                    postalCode = place.address_components[i].long_name;
                }
            }
        }

        console.log(`Latitude: ${latitude}, Longitude: ${longitude}`);
        console.log(`Address: ${num} ${route}, Town: ${town}, Country: ${country}`);
    }
</script>
<!--Location Link-->
<script>
    async function resolveShortUrl(shortUrl) {
        try {
            // Call the Django view to resolve the short URL
            const response = await fetch(`/resolve-url?url=${encodeURIComponent(shortUrl)}`);
            const data = await response.json();

            if (data.resolved_url) {
                return data.resolved_url; // Return the resolved URL
            } else {
                console.error('Error resolving URL:', data.error);
                return null;
            }
        } catch (error) {
            console.error('Error resolving short URL:', error);
            return null;
        }
    }

    function extractPlaceNameAndCoordinates(link) {
        // Updated regex to correctly extract place name and latitude/longitude
        const regex = /\/maps\/place\/([^\/]+)\/@(-?\d+\.\d+),(-?\d+\.\d+)/;
        const match = link.match(regex);
        if (match) {
            const placeName = decodeURIComponent(match[1]).replace(/\+/g, ' '); // Decode and replace + with spaces
            const lat = parseFloat(match[2]);
            const lng = parseFloat(match[3]);
            return { placeName, coordinates: { lat, lng } };
        }
        return null;
    }

    async function getAddressFromLink() {
        const locationLink = document.getElementById("locationLink").value;
        const addressField = document.getElementById("address");

        try {
            // Resolve the URL if it is a short URL
            const resolvedUrl = locationLink.includes('goo.gl') ? await resolveShortUrl(locationLink) : locationLink;

            if (resolvedUrl) {
                // Extract the place name and coordinates from the resolved link
                const placeInfo = extractPlaceNameAndCoordinates(resolvedUrl);
                console.log("ex co: ",placeInfo);
                if (placeInfo) {
                    // Call initMap with the coordinates
                    //initMap(placeInfo.coordinates);
                    addressField.value = 'Fetching address...'; // Optional: Indicate address fetching
                    const address = await geocodeLatLng(placeInfo.coordinates);
                    addressField.value = address; // Update the address field with the fetched address
                } else {
                    addressField.value = 'Invalid link or no coordinates found';
                }
            } else {
                addressField.value = 'Failed to resolve URL';
            }
        } catch (error) {
            console.error('Error fetching address:', error);
            addressField.value = 'Error fetching address: ' + error.message;
        }
    }

    function geocodeLatLng(latlng) {
        return new Promise((resolve, reject) => {
            const geocoder = new google.maps.Geocoder();
            const { lat, lng } = latlng;

            const latlngObj = {
                lat: parseFloat(latlng.lat),
                lng: parseFloat(latlng.lng)
            };
            geocoder.geocode({ location: latlngObj }, (results, status) => {
                if (status === 'OK') {
                    if (results[0]) {
                        resolve(results[0].formatted_address); // Resolve with formatted address
                    } else {
                        reject('No results found');
                    }
                } else {
                    reject('Geocoder failed due to: ' + status);
                }
            });
        });
    }

</script>
<script>
    function toggleStatus(checkbox) {
        var buttons = document.querySelectorAll('#statusButtons .btn-check');

        if (checkbox.checked) {
            buttons.forEach(function(btn) {
                if (btn !== checkbox) {
                    btn.checked = false;
                }
            });
        }

        buttons.forEach(function(btn) {
            var label = document.querySelector('label[for="' + btn.id + '"]');
            if (btn.checked) {
                label.classList.add('active');
            } else {
                label.classList.remove('active');
            }
        });
    }

    function closeEditModal() {
        document.getElementById('editModal').style.display = 'none';
    }
  </script>
  <script>
    function convertToBackendDateTime(dateTimeStr) {
        console.log("Input dateTimeStr:", dateTimeStr); // Log the input value

        // Split the datetime string into date and time parts using 'T' as the delimiter
        const [datePart, timePart] = dateTimeStr.split(' ');  // Correctly splitting on 'T'

        console.log("After splitting - Date part:", datePart, "Time part:", timePart); // Log the split parts

        const parts = datePart.split('-');  // Split the date part (dd-mm-yyyy)
        console.log("Split date parts - Day:", parts[0], "Month:", parts[1], "Year:", parts[2]); // Log the split date parts

        // Rearrange to yyyy-mm-dd and concatenate with the time part
        const backendDateTime = `${parts[2]}-${parts[1]}-${parts[0]} ${timePart}`;

        console.log("Converted backend datetime format:", backendDateTime); // Log the final converted value

        return backendDateTime;
    }
</script>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
  <script src="https://maxcdn.bootstrapcdn.com/bootstrap/5.3.0/js/bootstrap.min.js"></script>
  <script>
    // JavaScript to handle dropdown visibility
    const profileMenu = document.getElementById('profileMenu');
    const profileDropdown = document.getElementById('profileDropdown');

    profileMenu.addEventListener('click', function () {
        // Toggle dropdown visibility
        if (profileDropdown.style.display === 'none' || profileDropdown.style.display === '') {
            profileDropdown.style.display = 'block';
        } else {
            profileDropdown.style.display = 'none';
        }
    });

    // Close dropdown if clicked outside
    window.addEventListener('click', function (event) {
        if (!profileMenu.contains(event.target)) {
            profileDropdown.style.display = 'none';
        }
    });
</script>
<script>
    // user icon
    const username = document.getElementById('name').textContent;
    document.querySelector('#userIcon').innerText = username.charAt(0);
  </script>
</body>
</html>