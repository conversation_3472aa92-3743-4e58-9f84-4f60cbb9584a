{% load static %}
{% static "images" as baseurl %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sidebar Menu</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            height: 100vh;
            flex-direction: column;
            background-color: #fafafa;
        }
        .container {
            display: flex;
            width: 100%;
            flex-grow: 1;
        }
        .sidebar {
            background-color: #1e1e1e;
            color: white;
            width: 250px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            transition: width 0.3s;
            position: relative;
        }
        .sidebar.closed {
            width: 60px;
        }
        .sidebar .toggle-icon {
            position: absolute;
            top: 25px !important;
            right: -8px;
            cursor: pointer;
            visibility: hidden;
            border: 3px solid rgba(78, 27, 231, 0.5);
            border-radius: 8px;
            padding: 1px;
            transition: visibility 0.3s ease-in-out, top 0.3s ease-in-out;
            z-index: 2;
        }
        #toggle-icon {
            width: 20px;
            height: 20px;
        }
        .sidebar.closed .toggle-icon {
            top: 10px;
            right: -8px;
        }
        .sidebar:hover .toggle-icon,
        .toggle-icon:hover {
            visibility: visible;
        }
        .sidebar .logo {
            padding: 20px;
            text-align: center;
        }
        .sidebar.closed .logo {
            display: none;
        }
        .sidebar nav ul {
            list-style: none;
            padding: 0;
            width: 100%;
            text-align: center;
        }
        .sidebar nav ul li {
            padding: 12px 20px;
            cursor: pointer;
            transition: background-color 0.3s, border-left 0.3s;
            display: flex;
            justify-content: flex-start;
            align-items: center;
        }
        .sidebar.closed nav ul li {
            justify-content: center;
        }
        .sidebar nav ul li a {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: white;
            width: 100%;
            font-family: Arial, sans-serif;
        }
        .sidebar nav ul li a:hover {
            background-color: #555;
            border-left: 4px solid #ffcc00;
        }
        .menu-icon {
            margin-right: 10px;
            width: 24px;
            height: 24px;
        }
        .menu-text {
            transition: opacity 0.3s, visibility 0.3s;
            font-family: Arial, sans-serif;
        }
        .sidebar.closed .menu-text {
            display: none;
        }
        .sidebar.closed nav ul li:hover {
            background-color: inherit;
        }
        .main-content {
            flex-grow: 1;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .profile-box {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            height:90px;
            width:750px;
            border-radius: 8px;
            text-align: center;
            align-items:center;
        }
        .profile-section {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            cursor: pointer;
            position: relative;
        }
        .profile-section:hover {
            background-color: #555;
            border-left: 4px solid #ffcc00;
        }
        .dropdown {
            position: absolute;
            bottom: 100%;
            left: 0;
            background-color: white;
            border: 1px solid #ccc;
            border-radius: 4px;
            z-index: 1000;
            width: 160px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: none;
        }
        .dropdown ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .dropdown li {
            padding: 10px;
            color: black;
            cursor: pointer;
        }
        .dropdown li:hover {
            background-color: #f1f1f1;
        }

        /* user icon */

        .user-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background-color: #ddd;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            font-size: 18px;
            color: #0e0e0e;
            background-color: #e1ecb8;

        }
    </style>
</head>
<body>
    <div class="container">
        <aside class="sidebar">
            <div class="toggle-icon">
                <img src="{% static 'images/menuleft.png' %}" alt="icon" id="toggle-icon" width="16" height="16">
            </div>
            <div class="logo">
                <img src="{% static 'images/logowhite.png' %}" alt="logo" width="50" height="50">
            </div>
            <nav>
                <ul>
                        <li><a href="{% url 'dashboard' %}"><img src="{% static './images/dashboard.png' %}" alt="dashboard icon" class="menu-icon">Dashboard</a></li>
                        <li><a href="{% url 'projects' %}"><img src="{% static './images/Project.png' %}" class="menu-icon">Project</a></li>
                        <li><a href="{% url 'incomef_view' %}"><img src="{% static './images/Income.png' %}" alt="Income Icon" class="menu-icon">Income</a></li>
                        <li><a href="{% url 'expense' %}"><img src="{% static './images/expenses.png' %}" alt="Expenses Icon" class="menu-icon">Expense</a></li>
                        <li><a href="{% url 'calendar' %}"><img src="{% static './images/calendar.png' %}" alt="Calendar Icon" class="menu-icon">Calendar</a></li>
                        <li><a href="{% url 'allproject' %}"><img src="{% static './images/All projects.png' %}" alt="All Projects Icon" class="menu-icon">All Projects</a></li>
                        <li><a href="{% url 'clientsbook' %}"><img src="{% static './images/Client books.png' %}" alt="Clients Book Icon" class="menu-icon">Clients Book</a></li>
                        <li><a href="{% url 'clients' %}"><img src="{% static './images/Clients.png' %}" alt="Clients Icon" class="menu-icon">Clients</a></li>
                        <li><a href="{% url 'status' %}"><img src="{% static './images/Status.png' %}" alt="Status Icon" class="menu-icon">Status</a></li>
                        <li><a href="{% url 'pending_pay' %}"><img src="{% static './images/pending.png' %}" alt="Pending Payments Icon" class="menu-icon">Pending Payments</a></li>
                        <li><a href="{% url 'project_map' %}"><img src="{% static './images/maps-and-flags.png' %}" alt="Map Icon" class="menu-icon">Map</a></li>
                        <li><a href="{% url 'assets' %}"><img src="{% static './images/Assets.png' %}" alt="Assets Icon" class="menu-icon">Assets</a></li>
                        <li><a href="{% url 'budget' %}"><img src="{% static './images/budget.png' %}" alt="Budget Icon" class="menu-icon">Budget</a></li>
                        <li><a href="{% url 'entertainment' %}"><img src="{% static './images/Entertainment.png' %}" src= alt="Entertainment Icon" class="menu-icon">Entertainment</a></li>
                </ul>
            </nav>
            <div class="profile-section" id="profileMenu">
                <div class="user-icon" id="userIcon">
                    <!-- Default content in case JS is not available -->
                    U
                </div>

                <!--<img src="profile.png" alt="Profile" class="profile-icon">-->
                <span class="menu-text">{{user.username }}</span>
                <div class="dropdown" id="profileDropdown">
                    <ul>
                        {% if user.is_authenticated %}
                        <li><a href="{% url 'profile' %}">View Profile</a></li>
                        <li><a href="{% url 'logout_view' %}">Sign Out</a></li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </aside>
        <div class="main-content">
            <div class="profile-box">
                {% if user.is_authenticated %}

                <h1 id="name">{{user.username}}</h1>
                <p>{{user.email}}</p>
                {% endif %}

            </div>
        </div>
    </div>
    <script>
        document.getElementById('profileMenu').addEventListener('click', function(event) {
            event.stopPropagation();
            const dropdown = document.getElementById('profileDropdown');
            dropdown.style.display = dropdown.style.display === 'none' || dropdown.style.display === '' ? 'block' : 'none';
        });

        document.addEventListener('click', function() {
            const dropdown = document.getElementById('profileDropdown');
            dropdown.style.display = 'none';
        });

        const sidebar = document.querySelector('.sidebar');
        const toggleIcon = document.getElementById('toggle-icon');

        toggleIcon.addEventListener('click', function() {
            sidebar.classList.toggle('closed');
            toggleIcon.src = sidebar.classList.contains('closed') ? 'menuright.png' : 'menuleft.png';
        });
    </script>

    <script>
        const side = document.querySelector('.sidebar');
        const toggleI = document.getElementById('toggle-icon');

        toggleI.addEventListener('click', function() {
          if (side.classList.contains('closed')) {
            side.classList.remove('closed');
            toggleI.src = "{% static 'images/menuleft.png' %}";  // Change to the "left-chevron" when open
          } else {
            side.classList.add('closed');
            toggleI.src = "{% static 'images/menuright.png' %}";  // Change to the "chevron" when closed
          }
        });
      </script>

    <script>
        // user icon
        const username = document.getElementById('name').textContent;
        document.querySelector('#userIcon').innerText = username.charAt(0);
    </script>

</body>
</html>