#!/usr/bin/env python
"""
Test database connection for Cymatics Pro
"""

import os
import sys
import django

# Setup Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cymaticspro.settings')
django.setup()

from django.db import connection
from cymaticsapp.models import Client

def test_connection():
    """Test database connection"""
    try:
        # Test basic connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            print(f"✓ Database connection successful: {result}")
        
        # Test model access
        client_count = Client.objects.count()
        print(f"✓ Current clients in database: {client_count}")
        
        print("✅ Database is ready for population!")
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

if __name__ == '__main__':
    test_connection()
