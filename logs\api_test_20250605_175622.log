2025-06-05 17:56:22,813 - INFO - 🚀 Starting Cymatics API Test Suite...
2025-06-05 17:56:22,813 - INFO - 🏥 Testing Health Check...
2025-06-05 17:56:22,820 - INFO - ✅ PASS GET /health - 200 (0.007s)
2025-06-05 17:56:22,821 - INFO - ℹ️ Testing API Info...
2025-06-05 17:56:22,825 - INFO - ✅ PASS GET /api - 200 (0.004s)
2025-06-05 17:56:22,825 - INFO - 🔐 Testing Authentication...
2025-06-05 17:56:22,914 - INFO - ❌ FAIL POST /api/auth/send-otp - 500 (0.088s)
2025-06-05 17:56:22,914 - ERROR - Error: {"success":false,"error":{"code":"DATABASE_ERROR","message":"Database operation failed","details":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database."},"timestamp":"2025-06-05T12:26:22.913Z"}
2025-06-05 17:56:22,915 - ERROR - Failed to send OTP, skipping auth tests
2025-06-05 17:56:22,915 - WARNING - ⚠️ Running tests without authentication token - some tests may fail
2025-06-05 17:56:22,915 - INFO - 👥 Testing Client Management...
2025-06-05 17:56:22,920 - INFO - ❌ FAIL GET /api/clients - 401 (0.004s)
2025-06-05 17:56:22,920 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:22.919Z"}
2025-06-05 17:56:22,925 - INFO - ❌ FAIL GET /api/clients/stats - 401 (0.004s)
2025-06-05 17:56:22,926 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:22.924Z"}
2025-06-05 17:56:22,930 - INFO - ❌ FAIL GET /api/clients/dropdown - 401 (0.003s)
2025-06-05 17:56:22,930 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:22.929Z"}
2025-06-05 17:56:22,937 - INFO - ❌ FAIL POST /api/clients - 401 (0.006s)
2025-06-05 17:56:22,937 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:22.935Z"}
2025-06-05 17:56:22,943 - INFO - ❌ FAIL GET /api/clients/99999 - 401 (0.005s)
2025-06-05 17:56:22,944 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:22.942Z"}
2025-06-05 17:56:22,944 - INFO - 🏢 Testing Outclient Management...
2025-06-05 17:56:22,987 - INFO - ❌ FAIL GET /api/outclients - 401 (0.042s)
2025-06-05 17:56:22,987 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:22.985Z"}
2025-06-05 17:56:22,990 - INFO - ❌ FAIL GET /api/outclients/stats - 401 (0.003s)
2025-06-05 17:56:22,991 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:22.990Z"}
2025-06-05 17:56:22,996 - INFO - ❌ FAIL GET /api/outclients/dropdown - 401 (0.004s)
2025-06-05 17:56:22,996 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:22.995Z"}
2025-06-05 17:56:22,999 - INFO - ❌ FAIL POST /api/outclients - 401 (0.002s)
2025-06-05 17:56:22,999 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:22.998Z"}
2025-06-05 17:56:23,000 - INFO - 📋 Testing Project Management...
2025-06-05 17:56:23,003 - INFO - ❌ FAIL GET /api/projects - 401 (0.003s)
2025-06-05 17:56:23,003 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.002Z"}
2025-06-05 17:56:23,007 - INFO - ❌ FAIL GET /api/projects/stats - 401 (0.003s)
2025-06-05 17:56:23,007 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.006Z"}
2025-06-05 17:56:23,014 - INFO - ❌ FAIL GET /api/projects/codes - 401 (0.006s)
2025-06-05 17:56:23,014 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.013Z"}
2025-06-05 17:56:23,018 - INFO - ❌ FAIL POST /api/projects - 401 (0.003s)
2025-06-05 17:56:23,018 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.017Z"}
2025-06-05 17:56:23,019 - INFO - 💰 Testing Financial Management...
2025-06-05 17:56:23,022 - INFO - ❌ FAIL GET /api/financial/income - 401 (0.003s)
2025-06-05 17:56:23,022 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.021Z"}
2025-06-05 17:56:23,026 - INFO - ❌ FAIL POST /api/financial/income - 401 (0.003s)
2025-06-05 17:56:23,026 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.025Z"}
2025-06-05 17:56:23,030 - INFO - ❌ FAIL GET /api/financial/expenses - 401 (0.003s)
2025-06-05 17:56:23,031 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.029Z"}
2025-06-05 17:56:23,034 - INFO - ❌ FAIL GET /api/financial/expenses/categories - 401 (0.003s)
2025-06-05 17:56:23,034 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.033Z"}
2025-06-05 17:56:23,037 - INFO - ❌ FAIL GET /api/financial/expenses/totals - 401 (0.003s)
2025-06-05 17:56:23,038 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.036Z"}
2025-06-05 17:56:23,043 - INFO - ❌ FAIL POST /api/financial/expenses - 401 (0.006s)
2025-06-05 17:56:23,044 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.042Z"}
2025-06-05 17:56:23,048 - INFO - ❌ FAIL GET /api/financial/summary - 401 (0.003s)
2025-06-05 17:56:23,048 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.047Z"}
2025-06-05 17:56:23,050 - INFO - ❌ FAIL GET /api/financial/budget - 401 (0.002s)
2025-06-05 17:56:23,051 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.050Z"}
2025-06-05 17:56:23,051 - INFO - 🏭 Testing Asset Management...
2025-06-05 17:56:23,055 - INFO - ❌ FAIL GET /api/assets - 401 (0.003s)
2025-06-05 17:56:23,055 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.054Z"}
2025-06-05 17:56:23,060 - INFO - ❌ FAIL GET /api/assets/stats - 401 (0.004s)
2025-06-05 17:56:23,060 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.058Z"}
2025-06-05 17:56:23,064 - INFO - ❌ FAIL GET /api/assets/types - 401 (0.004s)
2025-06-05 17:56:23,065 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.063Z"}
2025-06-05 17:56:23,069 - INFO - ❌ FAIL POST /api/assets - 401 (0.003s)
2025-06-05 17:56:23,069 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.068Z"}
2025-06-05 17:56:23,070 - INFO - 🎬 Testing Entertainment Management...
2025-06-05 17:56:23,072 - INFO - ❌ FAIL GET /api/entertainment - 401 (0.003s)
2025-06-05 17:56:23,073 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.072Z"}
2025-06-05 17:56:23,077 - INFO - ❌ FAIL GET /api/entertainment/stats - 401 (0.003s)
2025-06-05 17:56:23,078 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.076Z"}
2025-06-05 17:56:23,081 - INFO - ❌ FAIL GET /api/entertainment/types - 401 (0.003s)
2025-06-05 17:56:23,081 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.080Z"}
2025-06-05 17:56:23,085 - INFO - ❌ FAIL GET /api/entertainment/languages - 401 (0.003s)
2025-06-05 17:56:23,085 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.084Z"}
2025-06-05 17:56:23,089 - INFO - ❌ FAIL POST /api/entertainment - 401 (0.003s)
2025-06-05 17:56:23,089 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.088Z"}
2025-06-05 17:56:23,090 - INFO - 📅 Testing Calendar Management...
2025-06-05 17:56:23,093 - INFO - ❌ FAIL GET /api/calendar/events - 401 (0.003s)
2025-06-05 17:56:23,094 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.092Z"}
2025-06-05 17:56:23,099 - INFO - ❌ FAIL GET /api/calendar/events/upcoming - 401 (0.005s)
2025-06-05 17:56:23,100 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.097Z"}
2025-06-05 17:56:23,102 - INFO - ❌ FAIL GET /api/calendar/events/today - 401 (0.002s)
2025-06-05 17:56:23,103 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.102Z"}
2025-06-05 17:56:23,106 - INFO - ❌ FAIL GET /api/calendar/events/week - 401 (0.003s)
2025-06-05 17:56:23,106 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.105Z"}
2025-06-05 17:56:23,110 - INFO - ❌ FAIL GET /api/calendar/events/month - 401 (0.003s)
2025-06-05 17:56:23,111 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.109Z"}
2025-06-05 17:56:23,114 - INFO - ❌ FAIL GET /api/calendar/events/stats - 401 (0.003s)
2025-06-05 17:56:23,115 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.113Z"}
2025-06-05 17:56:23,118 - INFO - ❌ FAIL POST /api/calendar/events - 401 (0.003s)
2025-06-05 17:56:23,118 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.117Z"}
2025-06-05 17:56:23,121 - INFO - ❌ FAIL GET /api/calendar/events/range?startDate=2025-06-05&endDate=2025-06-12 - 401 (0.002s)
2025-06-05 17:56:23,122 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.120Z"}
2025-06-05 17:56:23,122 - INFO - 🗺️ Testing Maps Integration...
2025-06-05 17:56:23,126 - INFO - ❌ FAIL POST /api/maps/geocode - 401 (0.004s)
2025-06-05 17:56:23,126 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.125Z"}
2025-06-05 17:56:23,130 - INFO - ❌ FAIL POST /api/maps/reverse-geocode - 401 (0.003s)
2025-06-05 17:56:23,130 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.130Z"}
2025-06-05 17:56:23,133 - INFO - ❌ FAIL POST /api/maps/detailed-geocode - 401 (0.003s)
2025-06-05 17:56:23,134 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.133Z"}
2025-06-05 17:56:23,137 - INFO - ❌ FAIL POST /api/maps/nearby-places - 401 (0.003s)
2025-06-05 17:56:23,137 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.137Z"}
2025-06-05 17:56:23,140 - INFO - ❌ FAIL POST /api/maps/distance - 401 (0.002s)
2025-06-05 17:56:23,141 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.140Z"}
2025-06-05 17:56:23,145 - INFO - ❌ FAIL GET /api/maps/static-map?lat=40.7128&lng=-74.0060&zoom=12 - 401 (0.004s)
2025-06-05 17:56:23,145 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.144Z"}
2025-06-05 17:56:23,148 - INFO - ❌ FAIL POST /api/maps/directions - 401 (0.003s)
2025-06-05 17:56:23,149 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.148Z"}
2025-06-05 17:56:23,151 - INFO - ❌ FAIL POST /api/maps/validate-coordinates - 401 (0.002s)
2025-06-05 17:56:23,152 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.151Z"}
2025-06-05 17:56:23,152 - INFO - 📊 Testing Dashboard...
2025-06-05 17:56:23,155 - INFO - ❌ FAIL GET /api/dashboard/stats - 401 (0.003s)
2025-06-05 17:56:23,156 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.155Z"}
2025-06-05 17:56:23,160 - INFO - ❌ FAIL GET /api/dashboard/financial-summary?startDate=2025-05-06&endDate=2025-06-05 - 401 (0.004s)
2025-06-05 17:56:23,160 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.159Z"}
2025-06-05 17:56:23,161 - INFO - 🧹 Cleaning up test data...
2025-06-05 17:56:23,161 - INFO - 🏁 Test suite completed in 0.35 seconds
2025-06-05 17:56:23,166 - INFO - Summary report saved to: logs/test_summary_20250605_175623.txt
