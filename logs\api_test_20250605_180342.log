2025-06-05 18:03:42,755 - INFO - 🚀 Starting Cymatics API Test Suite...
2025-06-05 18:03:42,755 - INFO - 🏥 Testing Health Check...
2025-06-05 18:03:42,781 - INFO - ✅ PASS GET /health - 200 (0.025s)
2025-06-05 18:03:42,883 - INFO - ℹ️ Testing API Info...
2025-06-05 18:03:42,895 - INFO - ✅ PASS GET /api - 200 (0.011s)
2025-06-05 18:03:42,997 - INFO - 🔐 Testing Authentication...
2025-06-05 18:03:43,359 - INFO - ❌ FAIL POST /api/auth/send-otp - 500 (0.362s)
2025-06-05 18:03:43,360 - ERROR - Error: {"success":false,"error":{"code":"DATABASE_ERROR","message":"Database operation failed","details":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database."},"timestamp":"2025-06-05T12:33:43.358Z"}
2025-06-05 18:03:43,461 - ERROR - Failed to send OTP, skipping auth tests
2025-06-05 18:03:43,462 - WARNING - ⚠️ Running tests without authentication token - some tests may fail
2025-06-05 18:03:43,462 - INFO - 👥 Testing Client Management...
2025-06-05 18:03:43,468 - INFO - ❌ FAIL GET /api/clients - 401 (0.006s)
2025-06-05 18:03:43,469 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:43.466Z"}
2025-06-05 18:03:43,572 - INFO - ❌ FAIL GET /api/clients/stats - 401 (0.002s)
2025-06-05 18:03:43,572 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:43.570Z"}
2025-06-05 18:03:43,675 - INFO - ❌ FAIL GET /api/clients/dropdown - 401 (0.002s)
2025-06-05 18:03:43,677 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:43.674Z"}
2025-06-05 18:03:43,783 - INFO - ❌ FAIL POST /api/clients - 401 (0.006s)
2025-06-05 18:03:43,783 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:43.780Z"}
2025-06-05 18:03:43,889 - INFO - ❌ FAIL GET /api/clients/99999 - 401 (0.004s)
2025-06-05 18:03:43,889 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:43.887Z"}
2025-06-05 18:03:43,990 - INFO - 🏢 Testing Outclient Management...
2025-06-05 18:03:43,996 - INFO - ❌ FAIL GET /api/outclients - 401 (0.005s)
2025-06-05 18:03:43,997 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:43.993Z"}
2025-06-05 18:03:44,101 - INFO - ❌ FAIL GET /api/outclients/stats - 401 (0.004s)
2025-06-05 18:03:44,102 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:44.100Z"}
2025-06-05 18:03:44,207 - INFO - ❌ FAIL GET /api/outclients/dropdown - 401 (0.004s)
2025-06-05 18:03:44,207 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:44.205Z"}
2025-06-05 18:03:44,312 - INFO - ❌ FAIL POST /api/outclients - 401 (0.004s)
2025-06-05 18:03:44,313 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:44.310Z"}
2025-06-05 18:03:44,413 - INFO - 📋 Testing Project Management...
2025-06-05 18:03:44,420 - INFO - ❌ FAIL GET /api/projects - 401 (0.006s)
2025-06-05 18:03:44,420 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:44.418Z"}
2025-06-05 18:03:44,524 - INFO - ❌ FAIL GET /api/projects/stats - 401 (0.003s)
2025-06-05 18:03:44,524 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:44.522Z"}
2025-06-05 18:03:44,628 - INFO - ❌ FAIL GET /api/projects/codes - 401 (0.003s)
2025-06-05 18:03:44,628 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:44.627Z"}
2025-06-05 18:03:44,732 - INFO - ❌ FAIL POST /api/projects - 401 (0.003s)
2025-06-05 18:03:44,733 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:44.731Z"}
2025-06-05 18:03:44,834 - INFO - 💰 Testing Financial Management...
2025-06-05 18:03:44,840 - INFO - ❌ FAIL GET /api/financial/income - 401 (0.006s)
2025-06-05 18:03:44,840 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:44.838Z"}
2025-06-05 18:03:44,949 - INFO - ❌ FAIL POST /api/financial/income - 401 (0.008s)
2025-06-05 18:03:44,949 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:44.947Z"}
2025-06-05 18:03:45,053 - INFO - ❌ FAIL GET /api/financial/expenses - 401 (0.003s)
2025-06-05 18:03:45,053 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:45.052Z"}
2025-06-05 18:03:45,158 - INFO - ❌ FAIL GET /api/financial/expenses/categories - 401 (0.003s)
2025-06-05 18:03:45,158 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:45.156Z"}
2025-06-05 18:03:45,262 - INFO - ❌ FAIL GET /api/financial/expenses/totals - 401 (0.003s)
2025-06-05 18:03:45,263 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:45.260Z"}
2025-06-05 18:03:45,365 - INFO - ❌ FAIL POST /api/financial/expenses - 401 (0.002s)
2025-06-05 18:03:45,366 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:45.364Z"}
2025-06-05 18:03:45,469 - INFO - ❌ FAIL GET /api/financial/summary - 401 (0.002s)
2025-06-05 18:03:45,470 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:45.468Z"}
2025-06-05 18:03:45,574 - INFO - ❌ FAIL GET /api/financial/budget - 401 (0.003s)
2025-06-05 18:03:45,574 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:45.572Z"}
2025-06-05 18:03:45,675 - INFO - 🏭 Testing Asset Management...
2025-06-05 18:03:45,678 - INFO - ❌ FAIL GET /api/assets - 401 (0.003s)
2025-06-05 18:03:45,678 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:45.676Z"}
2025-06-05 18:03:45,782 - INFO - ❌ FAIL GET /api/assets/stats - 401 (0.003s)
2025-06-05 18:03:45,783 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:45.780Z"}
2025-06-05 18:03:45,886 - INFO - ❌ FAIL GET /api/assets/types - 401 (0.003s)
2025-06-05 18:03:45,886 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:45.885Z"}
2025-06-05 18:03:45,992 - INFO - ❌ FAIL POST /api/assets - 401 (0.004s)
2025-06-05 18:03:45,992 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:45.991Z"}
2025-06-05 18:03:46,095 - INFO - 🎬 Testing Entertainment Management...
2025-06-05 18:03:46,099 - INFO - ❌ FAIL GET /api/entertainment - 401 (0.004s)
2025-06-05 18:03:46,099 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:46.098Z"}
2025-06-05 18:03:46,203 - INFO - ❌ FAIL GET /api/entertainment/stats - 401 (0.002s)
2025-06-05 18:03:46,204 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:46.202Z"}
2025-06-05 18:03:46,309 - INFO - ❌ FAIL GET /api/entertainment/types - 401 (0.003s)
2025-06-05 18:03:46,309 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:46.307Z"}
2025-06-05 18:03:46,413 - INFO - ❌ FAIL GET /api/entertainment/languages - 401 (0.002s)
2025-06-05 18:03:46,414 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:46.412Z"}
2025-06-05 18:03:46,521 - INFO - ❌ FAIL POST /api/entertainment - 401 (0.006s)
2025-06-05 18:03:46,523 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:46.519Z"}
2025-06-05 18:03:46,624 - INFO - 📅 Testing Calendar Management...
2025-06-05 18:03:46,627 - INFO - ❌ FAIL GET /api/calendar/events - 401 (0.004s)
2025-06-05 18:03:46,627 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:46.625Z"}
2025-06-05 18:03:46,730 - INFO - ❌ FAIL GET /api/calendar/events/upcoming - 401 (0.002s)
2025-06-05 18:03:46,730 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:46.729Z"}
2025-06-05 18:03:46,835 - INFO - ❌ FAIL GET /api/calendar/events/today - 401 (0.004s)
2025-06-05 18:03:46,836 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:46.834Z"}
2025-06-05 18:03:46,940 - INFO - ❌ FAIL GET /api/calendar/events/week - 401 (0.003s)
2025-06-05 18:03:46,940 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:46.939Z"}
2025-06-05 18:03:47,045 - INFO - ❌ FAIL GET /api/calendar/events/month - 401 (0.003s)
2025-06-05 18:03:47,045 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:47.043Z"}
2025-06-05 18:03:47,152 - INFO - ❌ FAIL GET /api/calendar/events/stats - 401 (0.005s)
2025-06-05 18:03:47,152 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:47.149Z"}
2025-06-05 18:03:47,259 - INFO - ❌ FAIL POST /api/calendar/events - 401 (0.006s)
2025-06-05 18:03:47,259 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:47.256Z"}
2025-06-05 18:03:47,364 - INFO - ❌ FAIL GET /api/calendar/events/range?startDate=2025-06-05&endDate=2025-06-12 - 401 (0.004s)
2025-06-05 18:03:47,364 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:47.363Z"}
2025-06-05 18:03:47,466 - INFO - 🗺️ Testing Maps Integration...
2025-06-05 18:03:47,472 - INFO - ❌ FAIL POST /api/maps/geocode - 401 (0.006s)
2025-06-05 18:03:47,472 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:47.470Z"}
2025-06-05 18:03:47,575 - INFO - ❌ FAIL POST /api/maps/reverse-geocode - 401 (0.002s)
2025-06-05 18:03:47,576 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:47.575Z"}
2025-06-05 18:03:47,680 - INFO - ❌ FAIL POST /api/maps/detailed-geocode - 401 (0.003s)
2025-06-05 18:03:47,681 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:47.679Z"}
2025-06-05 18:03:47,786 - INFO - ❌ FAIL POST /api/maps/nearby-places - 401 (0.004s)
2025-06-05 18:03:47,787 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:47.785Z"}
2025-06-05 18:03:47,890 - INFO - ❌ FAIL POST /api/maps/distance - 401 (0.002s)
2025-06-05 18:03:47,890 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:47.889Z"}
2025-06-05 18:03:47,994 - INFO - ❌ FAIL GET /api/maps/static-map?lat=40.7128&lng=-74.0060&zoom=12 - 401 (0.002s)
2025-06-05 18:03:47,995 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:47.993Z"}
2025-06-05 18:03:48,101 - INFO - ❌ FAIL POST /api/maps/directions - 401 (0.005s)
2025-06-05 18:03:48,102 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:48.098Z"}
2025-06-05 18:03:48,205 - INFO - ❌ FAIL POST /api/maps/validate-coordinates - 401 (0.002s)
2025-06-05 18:03:48,205 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:48.204Z"}
2025-06-05 18:03:48,307 - INFO - 📊 Testing Dashboard...
2025-06-05 18:03:48,310 - INFO - ❌ FAIL GET /api/dashboard/stats - 401 (0.002s)
2025-06-05 18:03:48,310 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:48.309Z"}
2025-06-05 18:03:48,416 - INFO - ❌ FAIL GET /api/dashboard/financial-summary?startDate=2025-05-06&endDate=2025-06-05 - 401 (0.005s)
2025-06-05 18:03:48,417 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:48.415Z"}
2025-06-05 18:03:48,518 - INFO - 🧹 Cleaning up test data...
2025-06-05 18:03:48,519 - INFO - 🏁 Test suite completed in 5.76 seconds
2025-06-05 18:03:48,525 - INFO - Summary report saved to: logs/test_summary_20250605_180348.txt
