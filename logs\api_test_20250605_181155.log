2025-06-05 18:11:55,024 - INFO - 🚀 Starting Cymatics API Test Suite...
2025-06-05 18:11:55,024 - INFO - 🏥 Testing Health Check...
2025-06-05 18:11:55,030 - INFO - ✅ PASS GET /health - 200 (0.004s)
2025-06-05 18:11:55,030 - INFO - ℹ️ Testing API Info...
2025-06-05 18:11:55,036 - INFO - ✅ PASS GET /api - 200 (0.005s)
2025-06-05 18:11:55,037 - INFO - 🔐 Testing Authentication...
2025-06-05 18:11:55,120 - INFO - ❌ FAIL POST /api/auth/send-otp - 500 (0.081s)
2025-06-05 18:11:55,121 - ERROR - Error: {"success":false,"error":{"code":"DATABASE_ERROR","message":"Database operation failed","details":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database."},"timestamp":"2025-06-05T12:41:55.117Z"}
2025-06-05 18:11:55,122 - ERROR - Failed to send OTP, skipping auth tests
2025-06-05 18:11:55,122 - WARNING - ⚠️ Running tests without authentication token - some tests may fail
2025-06-05 18:11:55,122 - INFO - 👥 Testing Client Management...
2025-06-05 18:11:55,128 - INFO - ❌ FAIL GET /api/clients - 401 (0.005s)
2025-06-05 18:11:55,128 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.127Z"}
2025-06-05 18:11:55,134 - INFO - ❌ FAIL GET /api/clients/stats - 401 (0.004s)
2025-06-05 18:11:55,135 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.132Z"}
2025-06-05 18:11:55,138 - INFO - ❌ FAIL GET /api/clients/dropdown - 401 (0.003s)
2025-06-05 18:11:55,139 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.137Z"}
2025-06-05 18:11:55,142 - INFO - ❌ FAIL POST /api/clients - 401 (0.003s)
2025-06-05 18:11:55,142 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.141Z"}
2025-06-05 18:11:55,147 - INFO - ❌ FAIL GET /api/clients/99999 - 401 (0.005s)
2025-06-05 18:11:55,147 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.146Z"}
2025-06-05 18:11:55,147 - INFO - 🏢 Testing Outclient Management...
2025-06-05 18:11:55,154 - INFO - ❌ FAIL GET /api/outclients - 401 (0.005s)
2025-06-05 18:11:55,154 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.152Z"}
2025-06-05 18:11:55,161 - INFO - ❌ FAIL GET /api/outclients/stats - 401 (0.007s)
2025-06-05 18:11:55,161 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.158Z"}
2025-06-05 18:11:55,167 - INFO - ❌ FAIL GET /api/outclients/dropdown - 401 (0.006s)
2025-06-05 18:11:55,168 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.166Z"}
2025-06-05 18:11:55,172 - INFO - ❌ FAIL POST /api/outclients - 401 (0.004s)
2025-06-05 18:11:55,173 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.171Z"}
2025-06-05 18:11:55,173 - INFO - 📋 Testing Project Management...
2025-06-05 18:11:55,178 - INFO - ❌ FAIL GET /api/projects - 401 (0.004s)
2025-06-05 18:11:55,178 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.177Z"}
2025-06-05 18:11:55,183 - INFO - ❌ FAIL GET /api/projects/stats - 401 (0.004s)
2025-06-05 18:11:55,183 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.182Z"}
2025-06-05 18:11:55,187 - INFO - ❌ FAIL GET /api/projects/codes - 401 (0.003s)
2025-06-05 18:11:55,188 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.186Z"}
2025-06-05 18:11:55,192 - INFO - ❌ FAIL POST /api/projects - 401 (0.004s)
2025-06-05 18:11:55,192 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.191Z"}
2025-06-05 18:11:55,193 - INFO - 💰 Testing Financial Management...
2025-06-05 18:11:55,197 - INFO - ❌ FAIL GET /api/financial/income - 401 (0.004s)
2025-06-05 18:11:55,198 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.196Z"}
2025-06-05 18:11:55,202 - INFO - ❌ FAIL POST /api/financial/income - 401 (0.004s)
2025-06-05 18:11:55,202 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.201Z"}
2025-06-05 18:11:55,207 - INFO - ❌ FAIL GET /api/financial/expenses - 401 (0.004s)
2025-06-05 18:11:55,207 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.206Z"}
2025-06-05 18:11:55,217 - INFO - ❌ FAIL GET /api/financial/expenses/categories - 401 (0.007s)
2025-06-05 18:11:55,217 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.211Z"}
2025-06-05 18:11:55,224 - INFO - ❌ FAIL GET /api/financial/expenses/totals - 401 (0.006s)
2025-06-05 18:11:55,224 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.220Z"}
2025-06-05 18:11:55,228 - INFO - ❌ FAIL POST /api/financial/expenses - 401 (0.004s)
2025-06-05 18:11:55,229 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.227Z"}
2025-06-05 18:11:55,233 - INFO - ❌ FAIL GET /api/financial/summary - 401 (0.004s)
2025-06-05 18:11:55,234 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.232Z"}
2025-06-05 18:11:55,238 - INFO - ❌ FAIL GET /api/financial/budget - 401 (0.004s)
2025-06-05 18:11:55,238 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.236Z"}
2025-06-05 18:11:55,238 - INFO - 🏭 Testing Asset Management...
2025-06-05 18:11:55,240 - INFO - ❌ FAIL GET /api/assets - 401 (0.001s)
2025-06-05 18:11:55,241 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.240Z"}
2025-06-05 18:11:55,244 - INFO - ❌ FAIL GET /api/assets/stats - 401 (0.002s)
2025-06-05 18:11:55,245 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.244Z"}
2025-06-05 18:11:55,249 - INFO - ❌ FAIL GET /api/assets/types - 401 (0.004s)
2025-06-05 18:11:55,249 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.248Z"}
2025-06-05 18:11:55,253 - INFO - ❌ FAIL POST /api/assets - 401 (0.004s)
2025-06-05 18:11:55,253 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.251Z"}
2025-06-05 18:11:55,253 - INFO - 🎬 Testing Entertainment Management...
2025-06-05 18:11:55,256 - INFO - ❌ FAIL GET /api/entertainment - 401 (0.003s)
2025-06-05 18:11:55,256 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.255Z"}
2025-06-05 18:11:55,259 - INFO - ❌ FAIL GET /api/entertainment/stats - 401 (0.002s)
2025-06-05 18:11:55,259 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.259Z"}
2025-06-05 18:11:55,261 - INFO - ❌ FAIL GET /api/entertainment/types - 401 (0.001s)
2025-06-05 18:11:55,263 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.261Z"}
2025-06-05 18:11:55,266 - INFO - ❌ FAIL GET /api/entertainment/languages - 401 (0.004s)
2025-06-05 18:11:55,266 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.265Z"}
2025-06-05 18:11:55,270 - INFO - ❌ FAIL POST /api/entertainment - 401 (0.003s)
2025-06-05 18:11:55,270 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.269Z"}
2025-06-05 18:11:55,270 - INFO - 📅 Testing Calendar Management...
2025-06-05 18:11:55,274 - INFO - ❌ FAIL GET /api/calendar/events - 401 (0.003s)
2025-06-05 18:11:55,274 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.273Z"}
2025-06-05 18:11:55,277 - INFO - ❌ FAIL GET /api/calendar/events/upcoming - 401 (0.002s)
2025-06-05 18:11:55,277 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.276Z"}
2025-06-05 18:11:55,280 - INFO - ❌ FAIL GET /api/calendar/events/today - 401 (0.002s)
2025-06-05 18:11:55,281 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.279Z"}
2025-06-05 18:11:55,284 - INFO - ❌ FAIL GET /api/calendar/events/week - 401 (0.003s)
2025-06-05 18:11:55,284 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.283Z"}
2025-06-05 18:11:55,287 - INFO - ❌ FAIL GET /api/calendar/events/month - 401 (0.002s)
2025-06-05 18:11:55,287 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.286Z"}
2025-06-05 18:11:55,290 - INFO - ❌ FAIL GET /api/calendar/events/stats - 401 (0.003s)
2025-06-05 18:11:55,290 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.289Z"}
2025-06-05 18:11:55,293 - INFO - ❌ FAIL POST /api/calendar/events - 401 (0.003s)
2025-06-05 18:11:55,293 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.293Z"}
2025-06-05 18:11:55,299 - INFO - ❌ FAIL GET /api/calendar/events/range?startDate=2025-06-05&endDate=2025-06-12 - 401 (0.004s)
2025-06-05 18:11:55,299 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.298Z"}
2025-06-05 18:11:55,300 - INFO - 🗺️ Testing Maps Integration...
2025-06-05 18:11:55,303 - INFO - ❌ FAIL POST /api/maps/geocode - 401 (0.004s)
2025-06-05 18:11:55,304 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.303Z"}
2025-06-05 18:11:55,307 - INFO - ❌ FAIL POST /api/maps/reverse-geocode - 401 (0.003s)
2025-06-05 18:11:55,307 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.306Z"}
2025-06-05 18:11:55,311 - INFO - ❌ FAIL POST /api/maps/detailed-geocode - 401 (0.002s)
2025-06-05 18:11:55,311 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.309Z"}
2025-06-05 18:11:55,315 - INFO - ❌ FAIL POST /api/maps/nearby-places - 401 (0.004s)
2025-06-05 18:11:55,316 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.314Z"}
2025-06-05 18:11:55,319 - INFO - ❌ FAIL POST /api/maps/distance - 401 (0.003s)
2025-06-05 18:11:55,320 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.318Z"}
2025-06-05 18:11:55,322 - INFO - ❌ FAIL GET /api/maps/static-map?lat=40.7128&lng=-74.0060&zoom=12 - 401 (0.003s)
2025-06-05 18:11:55,322 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.321Z"}
2025-06-05 18:11:55,326 - INFO - ❌ FAIL POST /api/maps/directions - 401 (0.003s)
2025-06-05 18:11:55,326 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.325Z"}
2025-06-05 18:11:55,330 - INFO - ❌ FAIL POST /api/maps/validate-coordinates - 401 (0.004s)
2025-06-05 18:11:55,330 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.329Z"}
2025-06-05 18:11:55,331 - INFO - 📊 Testing Dashboard...
2025-06-05 18:11:55,334 - INFO - ❌ FAIL GET /api/dashboard/stats - 401 (0.003s)
2025-06-05 18:11:55,334 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.333Z"}
2025-06-05 18:11:55,337 - INFO - ❌ FAIL GET /api/dashboard/financial-summary?startDate=2025-05-06&endDate=2025-06-05 - 401 (0.002s)
2025-06-05 18:11:55,338 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.337Z"}
2025-06-05 18:11:55,338 - INFO - 🧹 Cleaning up test data...
2025-06-05 18:11:55,338 - INFO - 🏁 Test suite completed in 0.31 seconds
2025-06-05 18:11:55,342 - INFO - Summary report saved to: logs/test_summary_20250605_181155.txt
