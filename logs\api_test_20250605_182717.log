2025-06-05 18:27:17,896 - INFO - 🚀 Starting Cymatics API Test Suite...
2025-06-05 18:27:17,896 - INFO - 🏥 Testing Health Check...
2025-06-05 18:27:17,901 - INFO - ✅ PASS GET /health - 200 (0.004s)
2025-06-05 18:27:17,902 - INFO - ℹ️ Testing API Info...
2025-06-05 18:27:17,911 - INFO - ✅ PASS GET /api - 200 (0.007s)
2025-06-05 18:27:17,912 - INFO - 🔐 Testing Authentication...
2025-06-05 18:27:18,046 - INFO - ❌ FAIL POST /api/auth/send-otp - 500 (0.134s)
2025-06-05 18:27:18,047 - ERROR - Error: {"success":false,"error":{"code":"DATABASE_ERROR","message":"Database operation failed","details":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database."},"timestamp":"2025-06-05T12:57:18.044Z"}
2025-06-05 18:27:18,048 - ERROR - Failed to send OTP, skipping auth tests
2025-06-05 18:27:18,048 - WARNING - ⚠️ Running tests without authentication token - some tests may fail
2025-06-05 18:27:18,048 - INFO - 👥 Testing Client Management...
2025-06-05 18:27:18,055 - INFO - ❌ FAIL GET /api/clients - 401 (0.007s)
2025-06-05 18:27:18,055 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.053Z"}
2025-06-05 18:27:18,059 - INFO - ❌ FAIL GET /api/clients/stats - 401 (0.003s)
2025-06-05 18:27:18,059 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.058Z"}
2025-06-05 18:27:18,064 - INFO - ❌ FAIL GET /api/clients/dropdown - 401 (0.003s)
2025-06-05 18:27:18,064 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.062Z"}
2025-06-05 18:27:18,068 - INFO - ❌ FAIL POST /api/clients - 401 (0.005s)
2025-06-05 18:27:18,070 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.066Z"}
2025-06-05 18:27:18,074 - INFO - ❌ FAIL GET /api/clients/99999 - 401 (0.005s)
2025-06-05 18:27:18,075 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.073Z"}
2025-06-05 18:27:18,075 - INFO - 🏢 Testing Outclient Management...
2025-06-05 18:27:18,080 - INFO - ❌ FAIL GET /api/outclients - 401 (0.003s)
2025-06-05 18:27:18,080 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.078Z"}
2025-06-05 18:27:18,085 - INFO - ❌ FAIL GET /api/outclients/stats - 401 (0.005s)
2025-06-05 18:27:18,086 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.084Z"}
2025-06-05 18:27:18,090 - INFO - ❌ FAIL GET /api/outclients/dropdown - 401 (0.004s)
2025-06-05 18:27:18,090 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.089Z"}
2025-06-05 18:27:18,096 - INFO - ❌ FAIL POST /api/outclients - 401 (0.005s)
2025-06-05 18:27:18,096 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.095Z"}
2025-06-05 18:27:18,097 - INFO - 📋 Testing Project Management...
2025-06-05 18:27:18,103 - INFO - ❌ FAIL GET /api/projects - 401 (0.005s)
2025-06-05 18:27:18,104 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.101Z"}
2025-06-05 18:27:18,109 - INFO - ❌ FAIL GET /api/projects/stats - 401 (0.005s)
2025-06-05 18:27:18,109 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.107Z"}
2025-06-05 18:27:18,113 - INFO - ❌ FAIL GET /api/projects/codes - 401 (0.003s)
2025-06-05 18:27:18,114 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.112Z"}
2025-06-05 18:27:18,119 - INFO - ❌ FAIL POST /api/projects - 401 (0.005s)
2025-06-05 18:27:18,119 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.116Z"}
2025-06-05 18:27:18,120 - INFO - 💰 Testing Financial Management...
2025-06-05 18:27:18,123 - INFO - ❌ FAIL GET /api/financial/income - 401 (0.003s)
2025-06-05 18:27:18,124 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.122Z"}
2025-06-05 18:27:18,129 - INFO - ❌ FAIL POST /api/financial/income - 401 (0.004s)
2025-06-05 18:27:18,129 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.127Z"}
2025-06-05 18:27:18,132 - INFO - ❌ FAIL GET /api/financial/expenses - 401 (0.004s)
2025-06-05 18:27:18,132 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.131Z"}
2025-06-05 18:27:18,138 - INFO - ❌ FAIL GET /api/financial/expenses/categories - 401 (0.006s)
2025-06-05 18:27:18,139 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.137Z"}
2025-06-05 18:27:18,143 - INFO - ❌ FAIL GET /api/financial/expenses/totals - 401 (0.004s)
2025-06-05 18:27:18,143 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.141Z"}
2025-06-05 18:27:18,148 - INFO - ❌ FAIL POST /api/financial/expenses - 401 (0.004s)
2025-06-05 18:27:18,149 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.147Z"}
2025-06-05 18:27:18,154 - INFO - ❌ FAIL GET /api/financial/summary - 401 (0.004s)
2025-06-05 18:27:18,155 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.153Z"}
2025-06-05 18:27:18,159 - INFO - ❌ FAIL GET /api/financial/budget - 401 (0.003s)
2025-06-05 18:27:18,160 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.157Z"}
2025-06-05 18:27:18,160 - INFO - 🏭 Testing Asset Management...
2025-06-05 18:27:18,164 - INFO - ❌ FAIL GET /api/assets - 401 (0.004s)
2025-06-05 18:27:18,164 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.162Z"}
2025-06-05 18:27:18,170 - INFO - ❌ FAIL GET /api/assets/stats - 401 (0.006s)
2025-06-05 18:27:18,171 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.168Z"}
2025-06-05 18:27:18,179 - INFO - ❌ FAIL GET /api/assets/types - 401 (0.006s)
2025-06-05 18:27:18,180 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.176Z"}
2025-06-05 18:27:18,190 - INFO - ❌ FAIL POST /api/assets - 401 (0.010s)
2025-06-05 18:27:18,191 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.189Z"}
2025-06-05 18:27:18,192 - INFO - 🎬 Testing Entertainment Management...
2025-06-05 18:27:18,208 - INFO - ❌ FAIL GET /api/entertainment - 401 (0.016s)
2025-06-05 18:27:18,208 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.206Z"}
2025-06-05 18:27:18,214 - INFO - ❌ FAIL GET /api/entertainment/stats - 401 (0.006s)
2025-06-05 18:27:18,214 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.212Z"}
2025-06-05 18:27:18,220 - INFO - ❌ FAIL GET /api/entertainment/types - 401 (0.004s)
2025-06-05 18:27:18,220 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.218Z"}
2025-06-05 18:27:18,226 - INFO - ❌ FAIL GET /api/entertainment/languages - 401 (0.006s)
2025-06-05 18:27:18,226 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.224Z"}
2025-06-05 18:27:18,231 - INFO - ❌ FAIL POST /api/entertainment - 401 (0.005s)
2025-06-05 18:27:18,232 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.230Z"}
2025-06-05 18:27:18,232 - INFO - 📅 Testing Calendar Management...
2025-06-05 18:27:18,238 - INFO - ❌ FAIL GET /api/calendar/events - 401 (0.005s)
2025-06-05 18:27:18,238 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.236Z"}
2025-06-05 18:27:18,244 - INFO - ❌ FAIL GET /api/calendar/events/upcoming - 401 (0.004s)
2025-06-05 18:27:18,244 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.241Z"}
2025-06-05 18:27:18,248 - INFO - ❌ FAIL GET /api/calendar/events/today - 401 (0.004s)
2025-06-05 18:27:18,249 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.247Z"}
2025-06-05 18:27:18,254 - INFO - ❌ FAIL GET /api/calendar/events/week - 401 (0.005s)
2025-06-05 18:27:18,254 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.252Z"}
2025-06-05 18:27:18,259 - INFO - ❌ FAIL GET /api/calendar/events/month - 401 (0.004s)
2025-06-05 18:27:18,259 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.258Z"}
2025-06-05 18:27:18,264 - INFO - ❌ FAIL GET /api/calendar/events/stats - 401 (0.004s)
2025-06-05 18:27:18,264 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.263Z"}
2025-06-05 18:27:18,269 - INFO - ❌ FAIL POST /api/calendar/events - 401 (0.005s)
2025-06-05 18:27:18,269 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.268Z"}
2025-06-05 18:27:18,273 - INFO - ❌ FAIL GET /api/calendar/events/range?startDate=2025-06-05&endDate=2025-06-12 - 401 (0.004s)
2025-06-05 18:27:18,273 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.272Z"}
2025-06-05 18:27:18,274 - INFO - 🗺️ Testing Maps Integration...
2025-06-05 18:27:18,281 - INFO - ❌ FAIL POST /api/maps/geocode - 401 (0.006s)
2025-06-05 18:27:18,282 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.278Z"}
2025-06-05 18:27:18,289 - INFO - ❌ FAIL POST /api/maps/reverse-geocode - 401 (0.007s)
2025-06-05 18:27:18,289 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.286Z"}
2025-06-05 18:27:18,294 - INFO - ❌ FAIL POST /api/maps/detailed-geocode - 401 (0.004s)
2025-06-05 18:27:18,296 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.294Z"}
2025-06-05 18:27:18,299 - INFO - ❌ FAIL POST /api/maps/nearby-places - 401 (0.003s)
2025-06-05 18:27:18,300 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.298Z"}
2025-06-05 18:27:18,303 - INFO - ❌ FAIL POST /api/maps/distance - 401 (0.004s)
2025-06-05 18:27:18,304 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.303Z"}
2025-06-05 18:27:18,308 - INFO - ❌ FAIL GET /api/maps/static-map?lat=40.7128&lng=-74.0060&zoom=12 - 401 (0.003s)
2025-06-05 18:27:18,309 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.307Z"}
2025-06-05 18:27:18,313 - INFO - ❌ FAIL POST /api/maps/directions - 401 (0.004s)
2025-06-05 18:27:18,314 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.311Z"}
2025-06-05 18:27:18,318 - INFO - ❌ FAIL POST /api/maps/validate-coordinates - 401 (0.005s)
2025-06-05 18:27:18,318 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.317Z"}
2025-06-05 18:27:18,319 - INFO - 📊 Testing Dashboard...
2025-06-05 18:27:18,323 - INFO - ❌ FAIL GET /api/dashboard/stats - 401 (0.003s)
2025-06-05 18:27:18,323 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.321Z"}
2025-06-05 18:27:18,326 - INFO - ❌ FAIL GET /api/dashboard/financial-summary?startDate=2025-05-06&endDate=2025-06-05 - 401 (0.003s)
2025-06-05 18:27:18,326 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.325Z"}
2025-06-05 18:27:18,327 - INFO - 🧹 Cleaning up test data...
2025-06-05 18:27:18,327 - INFO - 🏁 Test suite completed in 0.43 seconds
2025-06-05 18:27:18,331 - INFO - Summary report saved to: logs/test_summary_20250605_182718.txt
