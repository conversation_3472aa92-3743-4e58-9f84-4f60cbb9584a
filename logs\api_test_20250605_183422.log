2025-06-05 18:34:22,874 - INFO - 🚀 Starting Cymatics API Test Suite...
2025-06-05 18:34:22,875 - INFO - 🏥 Testing Health Check...
2025-06-05 18:34:22,885 - INFO - ✅ PASS GET /health - 200 (0.010s)
2025-06-05 18:34:22,885 - INFO - ℹ️ Testing API Info...
2025-06-05 18:34:22,890 - INFO - ✅ PASS GET /api - 200 (0.005s)
2025-06-05 18:34:22,890 - INFO - 🔐 Testing Authentication...
2025-06-05 18:34:22,890 - INFO - 📧 Sending <NAME_EMAIL>...
2025-06-05 18:34:23,137 - INFO - ❌ FAIL POST /api/auth/send-otp - 500 (0.247s)
2025-06-05 18:34:23,138 - ERROR - Error: {"success":false,"error":{"code":"DATABASE_ERROR","message":"Database operation failed","details":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database."},"timestamp":"2025-06-05T13:04:23.135Z"}
2025-06-05 18:34:23,138 - ERROR - Failed to send OTP, skipping auth tests
2025-06-05 18:34:23,139 - WARNING - ⚠️ Authentication failed - some tests may fail with 401 errors
2025-06-05 18:34:41,751 - INFO - 👥 Testing Client Management...
2025-06-05 18:34:41,760 - INFO - ❌ FAIL GET /api/clients - 401 (0.008s)
2025-06-05 18:34:41,760 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.759Z"}
2025-06-05 18:34:41,765 - INFO - ❌ FAIL GET /api/clients/stats - 401 (0.004s)
2025-06-05 18:34:41,766 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.765Z"}
2025-06-05 18:34:41,770 - INFO - ❌ FAIL GET /api/clients/dropdown - 401 (0.004s)
2025-06-05 18:34:41,770 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.769Z"}
2025-06-05 18:34:41,779 - INFO - ❌ FAIL POST /api/clients - 401 (0.009s)
2025-06-05 18:34:41,781 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.775Z"}
2025-06-05 18:34:41,785 - INFO - ❌ FAIL GET /api/clients/99999 - 401 (0.005s)
2025-06-05 18:34:41,785 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.784Z"}
2025-06-05 18:34:41,786 - INFO - 🏢 Testing Outclient Management...
2025-06-05 18:34:41,790 - INFO - ❌ FAIL GET /api/outclients - 401 (0.004s)
2025-06-05 18:34:41,790 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.790Z"}
2025-06-05 18:34:41,802 - INFO - ❌ FAIL GET /api/outclients/stats - 401 (0.011s)
2025-06-05 18:34:41,803 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.794Z"}
2025-06-05 18:34:41,807 - INFO - ❌ FAIL GET /api/outclients/dropdown - 401 (0.003s)
2025-06-05 18:34:41,808 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.806Z"}
2025-06-05 18:34:41,815 - INFO - ❌ FAIL POST /api/outclients - 401 (0.006s)
2025-06-05 18:34:41,815 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.813Z"}
2025-06-05 18:34:41,816 - INFO - 📋 Testing Project Management...
2025-06-05 18:34:41,819 - INFO - ❌ FAIL GET /api/projects - 401 (0.003s)
2025-06-05 18:34:41,819 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.819Z"}
2025-06-05 18:34:41,822 - INFO - ❌ FAIL GET /api/projects/stats - 401 (0.002s)
2025-06-05 18:34:41,823 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.822Z"}
2025-06-05 18:34:41,827 - INFO - ❌ FAIL GET /api/projects/codes - 401 (0.004s)
2025-06-05 18:34:41,828 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.826Z"}
2025-06-05 18:34:41,836 - INFO - ❌ FAIL POST /api/projects - 401 (0.007s)
2025-06-05 18:34:41,836 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.835Z"}
2025-06-05 18:34:41,837 - INFO - 💰 Testing Financial Management...
2025-06-05 18:34:41,840 - INFO - ❌ FAIL GET /api/financial/income - 401 (0.003s)
2025-06-05 18:34:41,841 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.840Z"}
2025-06-05 18:34:41,849 - INFO - ❌ FAIL POST /api/financial/income - 401 (0.007s)
2025-06-05 18:34:41,850 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.848Z"}
2025-06-05 18:34:41,854 - INFO - ❌ FAIL GET /api/financial/expenses - 401 (0.004s)
2025-06-05 18:34:41,855 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.853Z"}
2025-06-05 18:34:41,858 - INFO - ❌ FAIL GET /api/financial/expenses/categories - 401 (0.003s)
2025-06-05 18:34:41,859 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.857Z"}
2025-06-05 18:34:41,866 - INFO - ❌ FAIL GET /api/financial/expenses/totals - 401 (0.006s)
2025-06-05 18:34:41,866 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.864Z"}
2025-06-05 18:34:41,871 - INFO - ❌ FAIL POST /api/financial/expenses - 401 (0.004s)
2025-06-05 18:34:41,871 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.870Z"}
2025-06-05 18:34:41,874 - INFO - ❌ FAIL GET /api/financial/summary - 401 (0.004s)
2025-06-05 18:34:41,875 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.874Z"}
2025-06-05 18:34:41,879 - INFO - ❌ FAIL GET /api/financial/budget - 401 (0.004s)
2025-06-05 18:34:41,881 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.878Z"}
2025-06-05 18:34:41,882 - INFO - 🏭 Testing Asset Management...
2025-06-05 18:34:41,887 - INFO - ❌ FAIL GET /api/assets - 401 (0.006s)
2025-06-05 18:34:41,887 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.886Z"}
2025-06-05 18:34:41,890 - INFO - ❌ FAIL GET /api/assets/stats - 401 (0.003s)
2025-06-05 18:34:41,891 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.890Z"}
2025-06-05 18:34:41,896 - INFO - ❌ FAIL GET /api/assets/types - 401 (0.004s)
2025-06-05 18:34:41,896 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.894Z"}
2025-06-05 18:34:41,901 - INFO - ❌ FAIL POST /api/assets - 401 (0.004s)
2025-06-05 18:34:41,903 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.901Z"}
2025-06-05 18:34:41,903 - INFO - 🎬 Testing Entertainment Management...
2025-06-05 18:34:41,907 - INFO - ❌ FAIL GET /api/entertainment - 401 (0.004s)
2025-06-05 18:34:41,908 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.907Z"}
2025-06-05 18:34:41,913 - INFO - ❌ FAIL GET /api/entertainment/stats - 401 (0.005s)
2025-06-05 18:34:41,913 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.911Z"}
2025-06-05 18:34:41,917 - INFO - ❌ FAIL GET /api/entertainment/types - 401 (0.003s)
2025-06-05 18:34:41,917 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.917Z"}
2025-06-05 18:34:41,920 - INFO - ❌ FAIL GET /api/entertainment/languages - 401 (0.002s)
2025-06-05 18:34:41,921 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.920Z"}
2025-06-05 18:34:41,927 - INFO - ❌ FAIL POST /api/entertainment - 401 (0.006s)
2025-06-05 18:34:41,929 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.926Z"}
2025-06-05 18:34:41,929 - INFO - 📅 Testing Calendar Management...
2025-06-05 18:34:41,937 - INFO - ❌ FAIL GET /api/calendar/events - 401 (0.008s)
2025-06-05 18:34:41,937 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.935Z"}
2025-06-05 18:34:41,942 - INFO - ❌ FAIL GET /api/calendar/events/upcoming - 401 (0.004s)
2025-06-05 18:34:41,942 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.941Z"}
2025-06-05 18:34:41,946 - INFO - ❌ FAIL GET /api/calendar/events/today - 401 (0.004s)
2025-06-05 18:34:41,946 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.945Z"}
2025-06-05 18:34:41,951 - INFO - ❌ FAIL GET /api/calendar/events/week - 401 (0.004s)
2025-06-05 18:34:41,952 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.951Z"}
2025-06-05 18:34:41,956 - INFO - ❌ FAIL GET /api/calendar/events/month - 401 (0.004s)
2025-06-05 18:34:41,956 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.955Z"}
2025-06-05 18:34:41,959 - INFO - ❌ FAIL GET /api/calendar/events/stats - 401 (0.002s)
2025-06-05 18:34:41,961 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.959Z"}
2025-06-05 18:34:41,966 - INFO - ❌ FAIL POST /api/calendar/events - 401 (0.005s)
2025-06-05 18:34:41,966 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.966Z"}
2025-06-05 18:34:41,981 - INFO - ❌ FAIL GET /api/calendar/events/range?startDate=2025-06-05&endDate=2025-06-12 - 401 (0.014s)
2025-06-05 18:34:41,981 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.980Z"}
2025-06-05 18:34:41,981 - INFO - 🗺️ Testing Maps Integration...
2025-06-05 18:34:41,985 - INFO - ❌ FAIL POST /api/maps/geocode - 401 (0.003s)
2025-06-05 18:34:41,986 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.985Z"}
2025-06-05 18:34:41,990 - INFO - ❌ FAIL POST /api/maps/reverse-geocode - 401 (0.004s)
2025-06-05 18:34:41,990 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.990Z"}
2025-06-05 18:34:41,995 - INFO - ❌ FAIL POST /api/maps/detailed-geocode - 401 (0.004s)
2025-06-05 18:34:41,995 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.994Z"}
2025-06-05 18:34:42,000 - INFO - ❌ FAIL POST /api/maps/nearby-places - 401 (0.003s)
2025-06-05 18:34:42,000 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:42.000Z"}
2025-06-05 18:34:42,005 - INFO - ❌ FAIL POST /api/maps/distance - 401 (0.003s)
2025-06-05 18:34:42,005 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:42.004Z"}
2025-06-05 18:34:42,009 - INFO - ❌ FAIL GET /api/maps/static-map?lat=40.7128&lng=-74.0060&zoom=12 - 401 (0.004s)
2025-06-05 18:34:42,009 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:42.008Z"}
2025-06-05 18:34:42,015 - INFO - ❌ FAIL POST /api/maps/directions - 401 (0.006s)
2025-06-05 18:34:42,015 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:42.014Z"}
2025-06-05 18:34:42,021 - INFO - ❌ FAIL POST /api/maps/validate-coordinates - 401 (0.005s)
2025-06-05 18:34:42,021 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:42.020Z"}
2025-06-05 18:34:42,022 - INFO - 📊 Testing Dashboard...
2025-06-05 18:34:42,025 - INFO - ❌ FAIL GET /api/dashboard/stats - 401 (0.004s)
2025-06-05 18:34:42,025 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:42.024Z"}
2025-06-05 18:34:42,031 - INFO - ❌ FAIL GET /api/dashboard/financial-summary?startDate=2025-05-06&endDate=2025-06-05 - 401 (0.005s)
2025-06-05 18:34:42,031 - ERROR - Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:42.030Z"}
2025-06-05 18:34:42,032 - INFO - 🧹 Cleaning up test data...
2025-06-05 18:34:42,032 - INFO - 🏁 Test suite completed in 19.16 seconds
2025-06-05 18:34:42,039 - INFO - Summary report saved to: logs/test_summary_20250605_183442.txt
