2025-06-05 18:34:54,173 - INFO - 🚀 Starting Cymatics API Test Suite...
2025-06-05 18:34:54,174 - INFO - 🏥 Testing Health Check...
2025-06-05 18:34:54,182 - INFO - ✅ PASS GET /health - 200 (0.006s)
2025-06-05 18:34:54,182 - INFO - ℹ️ Testing API Info...
2025-06-05 18:34:54,185 - INFO - ✅ PASS GET /api - 200 (0.002s)
2025-06-05 18:34:54,186 - INFO - 🔐 Testing Authentication...
2025-06-05 18:34:54,186 - INFO - 📧 Sending <NAME_EMAIL>...
2025-06-05 18:34:54,217 - INFO - ❌ FAIL POST /api/auth/send-otp - 500 (0.032s)
2025-06-05 18:34:54,218 - ERROR - Error: {"success":false,"error":{"code":"DATABASE_ERROR","message":"Database operation failed","details":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database."},"timestamp":"2025-06-05T13:04:54.217Z"}
2025-06-05 18:34:54,219 - ERROR - Failed to send OTP, skipping auth tests
2025-06-05 18:34:54,219 - WARNING - ⚠️ Authentication failed - some tests may fail with 401 errors
2025-06-05 18:44:44,395 - INFO - Test suite cancelled by user
2025-06-05 18:44:44,414 - INFO - Summary report saved to: logs/test_summary_20250605_184444.txt
2025-06-05 18:44:44,415 - INFO - 🏁 Test suite completed in 590.24 seconds
2025-06-05 18:44:44,417 - INFO - Summary report saved to: logs/test_summary_20250605_184444.txt
