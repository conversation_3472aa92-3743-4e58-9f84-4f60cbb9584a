2025-06-05 18:36:52,857 - INFO - 🚀 Starting Cymatics API Test Suite...
2025-06-05 18:36:52,858 - INFO - 🏥 Testing Health Check...
2025-06-05 18:36:52,864 - INFO - ✅ PASS GET /health - 200 (0.005s)
2025-06-05 18:36:52,864 - INFO - ℹ️ Testing API Info...
2025-06-05 18:36:52,869 - INFO - ✅ PASS GET /api - 200 (0.004s)
2025-06-05 18:36:52,870 - INFO - 🔐 Testing Authentication...
2025-06-05 18:36:52,870 - INFO - 📧 Sending <NAME_EMAIL>...
2025-06-05 18:36:57,340 - INFO - ✅ PASS POST /api/auth/send-otp - 200 (4.470s)
2025-06-05 18:36:57,341 - INFO - ✅ OTP sent successfully!
2025-06-05 18:37:26,255 - INFO - 🔍 Verifying OTP: ***********-06-05 18:37:26,343 - INFO - ✅ PASS POST /api/auth/verify-otp - 200 (0.088s)
2025-06-05 18:37:26,345 - INFO - Auth token saved to file
2025-06-05 18:37:26,346 - INFO - ✅ Authentication successful! Token stored.
2025-06-05 18:37:26,365 - INFO - ✅ PASS GET /api/auth/profile - 200 (0.020s)
2025-06-05 18:37:26,366 - INFO - 🎉 Authentication successful! All endpoints will be tested with proper authorization.
2025-06-05 18:37:26,367 - INFO - 👥 Testing Client Management...
2025-06-05 18:37:26,434 - INFO - ✅ PASS GET /api/clients - 200 (0.066s)
2025-06-05 18:37:26,546 - INFO - ✅ PASS GET /api/clients/stats - 200 (0.112s)
2025-06-05 18:37:26,559 - INFO - ✅ PASS GET /api/clients/dropdown - 200 (0.013s)
2025-06-05 18:37:26,600 - INFO - ✅ PASS POST /api/clients - 201 (0.041s)
2025-06-05 18:37:26,615 - INFO - ✅ PASS GET /api/clients/1 - 200 (0.014s)
2025-06-05 18:37:26,629 - INFO - ✅ PASS GET /api/clients/1/data - 200 (0.014s)
2025-06-05 18:37:26,652 - INFO - ✅ PASS PUT /api/clients/1 - 200 (0.022s)
2025-06-05 18:37:26,682 - INFO - ❌ FAIL GET /api/clients/99999 - 404 (0.029s)
2025-06-05 18:37:26,682 - ERROR - Error: {"success":false,"error":{"code":"NOT_FOUND_ERROR","message":"Client not found"},"timestamp":"2025-06-05T13:07:26.681Z"}
2025-06-05 18:37:26,683 - INFO - 🏢 Testing Outclient Management...
2025-06-05 18:37:26,698 - INFO - ✅ PASS GET /api/outclients - 200 (0.015s)
2025-06-05 18:37:26,707 - INFO - ✅ PASS GET /api/outclients/stats - 200 (0.009s)
2025-06-05 18:37:26,718 - INFO - ✅ PASS GET /api/outclients/dropdown - 200 (0.011s)
2025-06-05 18:37:26,735 - INFO - ✅ PASS POST /api/outclients - 201 (0.016s)
2025-06-05 18:37:26,750 - INFO - ✅ PASS GET /api/outclients/1 - 200 (0.014s)
2025-06-05 18:37:26,781 - INFO - ✅ PASS PUT /api/outclients/1 - 200 (0.031s)
2025-06-05 18:37:26,781 - INFO - 📋 Testing Project Management...
2025-06-05 18:37:26,806 - INFO - ✅ PASS GET /api/projects - 200 (0.024s)
2025-06-05 18:37:26,894 - INFO - ✅ PASS GET /api/projects/stats - 200 (0.087s)
2025-06-05 18:37:26,903 - INFO - ✅ PASS GET /api/projects/codes - 200 (0.009s)
2025-06-05 18:37:27,478 - INFO - ✅ PASS POST /api/projects - 201 (0.574s)
2025-06-05 18:37:27,489 - INFO - ✅ PASS GET /api/projects/1 - 200 (0.010s)
2025-06-05 18:37:27,499 - INFO - ✅ PASS GET /api/projects/code/CYM-1 - 200 (0.009s)
2025-06-05 18:37:27,510 - INFO - ✅ PASS GET /api/projects/CYM-1/data - 200 (0.011s)
2025-06-05 18:37:27,546 - INFO - ✅ PASS PUT /api/projects/1 - 200 (0.035s)
2025-06-05 18:37:27,573 - INFO - ✅ PASS PUT /api/projects/1/status - 200 (0.025s)
2025-06-05 18:37:27,573 - INFO - 💰 Testing Financial Management...
2025-06-05 18:37:27,590 - INFO - ✅ PASS GET /api/financial/income - 200 (0.016s)
2025-06-05 18:37:27,607 - INFO - ✅ PASS POST /api/financial/income - 201 (0.016s)
2025-06-05 18:37:27,616 - INFO - ✅ PASS GET /api/financial/income/1 - 200 (0.008s)
2025-06-05 18:37:27,629 - INFO - ✅ PASS PUT /api/financial/income/1 - 200 (0.014s)
2025-06-05 18:37:27,643 - INFO - ✅ PASS GET /api/financial/expenses - 200 (0.013s)
2025-06-05 18:37:27,653 - INFO - ❌ FAIL GET /api/financial/expenses/categories - 400 (0.011s)
2025-06-05 18:37:27,654 - ERROR - Error: {"success":false,"error":{"code":"PARAMS_VALIDATION_ERROR","message":"Parameters validation failed","details":[{"field":"id","message":"\"id\" must be a number"}]},"timestamp":"2025-06-05T13:07:27.652Z"}
2025-06-05 18:37:27,659 - INFO - ❌ FAIL GET /api/financial/expenses/totals - 400 (0.005s)
2025-06-05 18:37:27,660 - ERROR - Error: {"success":false,"error":{"code":"PARAMS_VALIDATION_ERROR","message":"Parameters validation failed","details":[{"field":"id","message":"\"id\" must be a number"}]},"timestamp":"2025-06-05T13:07:27.658Z"}
2025-06-05 18:37:27,673 - INFO - ✅ PASS POST /api/financial/expenses - 201 (0.012s)
2025-06-05 18:37:27,680 - INFO - ✅ PASS GET /api/financial/expenses/1 - 200 (0.006s)
2025-06-05 18:37:27,692 - INFO - ✅ PASS PUT /api/financial/expenses/1 - 200 (0.012s)
2025-06-05 18:37:27,702 - INFO - ✅ PASS GET /api/financial/summary - 200 (0.009s)
2025-06-05 18:37:27,776 - INFO - ✅ PASS GET /api/financial/budget - 200 (0.074s)
2025-06-05 18:37:27,777 - INFO - 🏭 Testing Asset Management...
2025-06-05 18:37:27,790 - INFO - ✅ PASS GET /api/assets - 200 (0.013s)
2025-06-05 18:37:27,801 - INFO - ✅ PASS GET /api/assets/stats - 200 (0.012s)
2025-06-05 18:37:27,812 - INFO - ✅ PASS GET /api/assets/types - 200 (0.010s)
2025-06-05 18:37:27,829 - INFO - ✅ PASS POST /api/assets - 201 (0.017s)
2025-06-05 18:37:27,839 - INFO - ✅ PASS GET /api/assets/1 - 200 (0.009s)
2025-06-05 18:37:27,850 - INFO - ✅ PASS PUT /api/assets/1 - 200 (0.011s)
2025-06-05 18:37:27,851 - INFO - 🎬 Testing Entertainment Management...
2025-06-05 18:37:27,866 - INFO - ✅ PASS GET /api/entertainment - 200 (0.014s)
2025-06-05 18:37:27,877 - INFO - ✅ PASS GET /api/entertainment/stats - 200 (0.010s)
2025-06-05 18:37:27,884 - INFO - ✅ PASS GET /api/entertainment/types - 200 (0.007s)
2025-06-05 18:37:27,892 - INFO - ✅ PASS GET /api/entertainment/languages - 200 (0.008s)
2025-06-05 18:37:27,905 - INFO - ✅ PASS POST /api/entertainment - 201 (0.012s)
2025-06-05 18:37:27,913 - INFO - ✅ PASS GET /api/entertainment/1 - 200 (0.007s)
2025-06-05 18:37:27,928 - INFO - ✅ PASS PUT /api/entertainment/1 - 200 (0.015s)
2025-06-05 18:37:27,929 - INFO - 📅 Testing Calendar Management...
2025-06-05 18:37:27,948 - INFO - ✅ PASS GET /api/calendar/events - 200 (0.019s)
2025-06-05 18:37:27,961 - INFO - ✅ PASS GET /api/calendar/events/upcoming - 200 (0.011s)
2025-06-05 18:37:27,976 - INFO - ✅ PASS GET /api/calendar/events/today - 200 (0.015s)
2025-06-05 18:37:27,991 - INFO - ✅ PASS GET /api/calendar/events/week - 200 (0.014s)
2025-06-05 18:37:28,004 - INFO - ✅ PASS GET /api/calendar/events/month - 200 (0.011s)
2025-06-05 18:37:28,022 - INFO - ✅ PASS GET /api/calendar/events/stats - 200 (0.018s)
2025-06-05 18:37:28,038 - INFO - ✅ PASS POST /api/calendar/events - 201 (0.017s)
2025-06-05 18:37:28,049 - INFO - ✅ PASS GET /api/calendar/events/1 - 200 (0.010s)
2025-06-05 18:37:28,068 - INFO - ✅ PASS PUT /api/calendar/events/1 - 200 (0.019s)
2025-06-05 18:37:28,080 - INFO - ✅ PASS GET /api/calendar/events/range?startDate=2025-06-05&endDate=2025-06-12 - 200 (0.010s)
2025-06-05 18:37:28,081 - INFO - 🗺️ Testing Maps Integration...
2025-06-05 18:37:28,294 - INFO - ✅ PASS POST /api/maps/geocode - 200 (0.213s)
2025-06-05 18:37:28,300 - INFO - ❌ FAIL POST /api/maps/reverse-geocode - 400 (0.006s)
2025-06-05 18:37:28,300 - ERROR - Error: {"success":false,"error":{"code":"VALIDATION_ERROR","message":"Validation failed","details":[{"field":"latitude","message":"\"latitude\" is required"},{"field":"longitude","message":"\"longitude\" is required"}]},"timestamp":"2025-06-05T13:07:28.300Z"}
2025-06-05 18:37:28,422 - INFO - ✅ PASS POST /api/maps/detailed-geocode - 200 (0.121s)
2025-06-05 18:37:28,428 - INFO - ❌ FAIL POST /api/maps/nearby-places - 400 (0.005s)
2025-06-05 18:37:28,428 - ERROR - Error: {"success":false,"error":{"code":"VALIDATION_ERROR","message":"Validation failed","details":[{"field":"latitude","message":"\"latitude\" is required"},{"field":"longitude","message":"\"longitude\" is required"}]},"timestamp":"2025-06-05T13:07:28.427Z"}
2025-06-05 18:37:28,435 - INFO - ❌ FAIL POST /api/maps/distance - 400 (0.007s)
2025-06-05 18:37:28,436 - ERROR - Error: {"success":false,"error":{"code":"VALIDATION_ERROR","message":"Validation failed","details":[{"field":"origin.latitude","message":"\"origin.latitude\" is required"},{"field":"origin.longitude","message":"\"origin.longitude\" is required"},{"field":"destination.latitude","message":"\"destination.latitude\" is required"},{"field":"destination.longitude","message":"\"destination.longitude\" is required"}]},"timestamp":"2025-06-05T13:07:28.434Z"}
2025-06-05 18:37:28,443 - INFO - ❌ FAIL GET /api/maps/static-map?lat=40.7128&lng=-74.0060&zoom=12 - 400 (0.006s)
2025-06-05 18:37:28,443 - ERROR - Error: {"success":false,"error":{"code":"QUERY_VALIDATION_ERROR","message":"Query validation failed","details":[{"field":"latitude","message":"\"latitude\" is required"},{"field":"longitude","message":"\"longitude\" is required"}]},"timestamp":"2025-06-05T13:07:28.442Z"}
2025-06-05 18:37:28,449 - INFO - ❌ FAIL POST /api/maps/directions - 400 (0.004s)
2025-06-05 18:37:28,449 - ERROR - Error: {"success":false,"error":{"code":"VALIDATION_ERROR","message":"Validation failed","details":[{"field":"origin.latitude","message":"\"origin.latitude\" is required"},{"field":"origin.longitude","message":"\"origin.longitude\" is required"},{"field":"destination.latitude","message":"\"destination.latitude\" is required"},{"field":"destination.longitude","message":"\"destination.longitude\" is required"}]},"timestamp":"2025-06-05T13:07:28.447Z"}
2025-06-05 18:37:28,455 - INFO - ❌ FAIL POST /api/maps/validate-coordinates - 400 (0.005s)
2025-06-05 18:37:28,455 - ERROR - Error: {"success":false,"error":{"code":"VALIDATION_ERROR","message":"Validation failed","details":[{"field":"latitude","message":"\"latitude\" is required"},{"field":"longitude","message":"\"longitude\" is required"}]},"timestamp":"2025-06-05T13:07:28.453Z"}
2025-06-05 18:37:28,455 - INFO - 📊 Testing Dashboard...
2025-06-05 18:37:28,730 - INFO - ✅ PASS GET /api/dashboard/stats - 200 (0.274s)
2025-06-05 18:37:28,744 - INFO - ✅ PASS GET /api/dashboard/financial-summary?startDate=2025-05-06&endDate=2025-06-05 - 200 (0.014s)
2025-06-05 18:37:28,745 - INFO - 🧹 Cleaning up test data...
2025-06-05 18:37:28,760 - INFO - ✅ PASS DELETE /api/calendar/events/1 - 200 (0.015s)
2025-06-05 18:37:28,776 - INFO - ✅ PASS DELETE /api/entertainment/1 - 200 (0.015s)
2025-06-05 18:37:28,792 - INFO - ✅ PASS DELETE /api/assets/1 - 200 (0.015s)
2025-06-05 18:37:28,805 - INFO - ✅ PASS DELETE /api/financial/expenses/1 - 200 (0.012s)
2025-06-05 18:37:28,815 - INFO - ✅ PASS DELETE /api/financial/income/1 - 200 (0.010s)
2025-06-05 18:37:28,838 - INFO - ✅ PASS DELETE /api/projects/1 - 200 (0.022s)
2025-06-05 18:37:28,858 - INFO - ✅ PASS DELETE /api/outclients/1 - 200 (0.020s)
2025-06-05 18:37:28,875 - INFO - ✅ PASS DELETE /api/clients/1 - 200 (0.016s)
2025-06-05 18:37:28,875 - INFO - 🏁 Test suite completed in 36.02 seconds
2025-06-05 18:37:28,878 - INFO - Summary report saved to: logs/test_summary_20250605_183728.txt
