2025-06-05 18:44:48,239 - INFO - Loaded existing auth token from file
2025-06-05 18:44:48,239 - INFO - 🚀 Starting Cymatics API Test Suite...
2025-06-05 18:44:48,240 - INFO - 🏥 Testing Health Check...
2025-06-05 18:44:48,251 - INFO - ✅ PASS GET /health - 200 (0.011s)
2025-06-05 18:44:48,251 - INFO - ℹ️ Testing API Info...
2025-06-05 18:44:48,258 - INFO - ✅ PASS GET /api - 200 (0.006s)
2025-06-05 18:44:48,259 - INFO - 🔐 Testing Authentication...
2025-06-05 18:44:48,474 - INFO - ✅ PASS GET /api/auth/profile - 200 (0.214s)
2025-06-05 18:44:48,475 - INFO - ✅ Using existing valid authentication token
2025-06-05 18:44:48,476 - INFO - 🎉 Authentication successful! All endpoints will be tested with proper authorization.
2025-06-05 18:44:48,477 - INFO - 👥 Testing Client Management...
2025-06-05 18:44:48,507 - INFO - ✅ PASS GET /api/clients - 200 (0.028s)
2025-06-05 18:44:48,646 - INFO - ✅ PASS GET /api/clients/stats - 200 (0.138s)
2025-06-05 18:44:48,657 - INFO - ✅ PASS GET /api/clients/dropdown - 200 (0.011s)
2025-06-05 18:44:48,689 - INFO - ✅ PASS POST /api/clients - 201 (0.031s)
2025-06-05 18:44:48,700 - INFO - ✅ PASS GET /api/clients/2 - 200 (0.010s)
2025-06-05 18:44:48,708 - INFO - ✅ PASS GET /api/clients/2/data - 200 (0.007s)
2025-06-05 18:44:48,739 - INFO - ✅ PASS PUT /api/clients/2 - 200 (0.030s)
2025-06-05 18:44:48,752 - INFO - ❌ FAIL GET /api/clients/99999 - 404 (0.013s)
2025-06-05 18:44:48,752 - ERROR - Error: {"success":false,"error":{"code":"NOT_FOUND_ERROR","message":"Client not found"},"timestamp":"2025-06-05T13:14:48.750Z"}
2025-06-05 18:44:48,752 - INFO - 🏢 Testing Outclient Management...
2025-06-05 18:44:48,783 - INFO - ✅ PASS GET /api/outclients - 200 (0.030s)
2025-06-05 18:44:48,791 - INFO - ✅ PASS GET /api/outclients/stats - 200 (0.007s)
2025-06-05 18:44:48,801 - INFO - ✅ PASS GET /api/outclients/dropdown - 200 (0.010s)
2025-06-05 18:44:48,818 - INFO - ✅ PASS POST /api/outclients - 201 (0.016s)
2025-06-05 18:44:48,829 - INFO - ✅ PASS GET /api/outclients/2 - 200 (0.010s)
2025-06-05 18:44:48,850 - INFO - ✅ PASS PUT /api/outclients/2 - 200 (0.022s)
2025-06-05 18:44:48,850 - INFO - 📋 Testing Project Management...
2025-06-05 18:44:48,871 - INFO - ✅ PASS GET /api/projects - 200 (0.018s)
2025-06-05 18:44:48,949 - INFO - ✅ PASS GET /api/projects/stats - 200 (0.077s)
2025-06-05 18:44:48,955 - INFO - ✅ PASS GET /api/projects/codes - 200 (0.006s)
2025-06-05 18:44:48,982 - INFO - ❌ FAIL POST /api/projects - 400 (0.026s)
2025-06-05 18:44:48,983 - ERROR - Error: {"success":false,"error":{"code":"VALIDATION_ERROR","message":"Client not found"},"timestamp":"2025-06-05T13:14:48.980Z"}
2025-06-05 18:44:48,983 - INFO - 💰 Testing Financial Management...
2025-06-05 18:44:49,019 - INFO - ✅ PASS GET /api/financial/income - 200 (0.037s)
2025-06-05 18:44:49,032 - INFO - ✅ PASS POST /api/financial/income - 201 (0.013s)
2025-06-05 18:44:49,040 - INFO - ✅ PASS GET /api/financial/income/2 - 200 (0.006s)
2025-06-05 18:44:49,054 - INFO - ✅ PASS PUT /api/financial/income/2 - 200 (0.013s)
2025-06-05 18:44:49,066 - INFO - ✅ PASS GET /api/financial/expenses - 200 (0.012s)
2025-06-05 18:44:49,071 - INFO - ❌ FAIL GET /api/financial/expenses/categories - 400 (0.005s)
2025-06-05 18:44:49,071 - ERROR - Error: {"success":false,"error":{"code":"PARAMS_VALIDATION_ERROR","message":"Parameters validation failed","details":[{"field":"id","message":"\"id\" must be a number"}]},"timestamp":"2025-06-05T13:14:49.070Z"}
2025-06-05 18:44:49,080 - INFO - ❌ FAIL GET /api/financial/expenses/totals - 400 (0.007s)
2025-06-05 18:44:49,081 - ERROR - Error: {"success":false,"error":{"code":"PARAMS_VALIDATION_ERROR","message":"Parameters validation failed","details":[{"field":"id","message":"\"id\" must be a number"}]},"timestamp":"2025-06-05T13:14:49.078Z"}
2025-06-05 18:44:49,091 - INFO - ✅ PASS POST /api/financial/expenses - 201 (0.010s)
2025-06-05 18:44:49,103 - INFO - ✅ PASS GET /api/financial/expenses/2 - 200 (0.012s)
2025-06-05 18:44:49,118 - INFO - ✅ PASS PUT /api/financial/expenses/2 - 200 (0.015s)
2025-06-05 18:44:49,182 - INFO - ✅ PASS GET /api/financial/summary - 200 (0.063s)
2025-06-05 18:44:49,252 - INFO - ✅ PASS GET /api/financial/budget - 200 (0.069s)
2025-06-05 18:44:49,253 - INFO - 🏭 Testing Asset Management...
2025-06-05 18:44:49,268 - INFO - ✅ PASS GET /api/assets - 200 (0.016s)
2025-06-05 18:44:49,280 - INFO - ✅ PASS GET /api/assets/stats - 200 (0.012s)
2025-06-05 18:44:49,286 - INFO - ✅ PASS GET /api/assets/types - 200 (0.006s)
2025-06-05 18:44:49,301 - INFO - ✅ PASS POST /api/assets - 201 (0.015s)
2025-06-05 18:44:49,312 - INFO - ✅ PASS GET /api/assets/2 - 200 (0.010s)
2025-06-05 18:44:49,329 - INFO - ✅ PASS PUT /api/assets/2 - 200 (0.017s)
2025-06-05 18:44:49,330 - INFO - 🎬 Testing Entertainment Management...
2025-06-05 18:44:49,344 - INFO - ✅ PASS GET /api/entertainment - 200 (0.014s)
2025-06-05 18:44:49,355 - INFO - ✅ PASS GET /api/entertainment/stats - 200 (0.011s)
2025-06-05 18:44:49,367 - INFO - ✅ PASS GET /api/entertainment/types - 200 (0.010s)
2025-06-05 18:44:49,378 - INFO - ✅ PASS GET /api/entertainment/languages - 200 (0.010s)
2025-06-05 18:44:49,394 - INFO - ✅ PASS POST /api/entertainment - 201 (0.015s)
2025-06-05 18:44:49,402 - INFO - ✅ PASS GET /api/entertainment/2 - 200 (0.007s)
2025-06-05 18:44:49,418 - INFO - ✅ PASS PUT /api/entertainment/2 - 200 (0.015s)
2025-06-05 18:44:49,418 - INFO - 📅 Testing Calendar Management...
2025-06-05 18:44:49,435 - INFO - ✅ PASS GET /api/calendar/events - 200 (0.016s)
2025-06-05 18:44:49,448 - INFO - ✅ PASS GET /api/calendar/events/upcoming - 200 (0.011s)
2025-06-05 18:44:49,458 - INFO - ✅ PASS GET /api/calendar/events/today - 200 (0.009s)
2025-06-05 18:44:49,466 - INFO - ✅ PASS GET /api/calendar/events/week - 200 (0.008s)
2025-06-05 18:44:49,474 - INFO - ✅ PASS GET /api/calendar/events/month - 200 (0.008s)
2025-06-05 18:44:49,489 - INFO - ✅ PASS GET /api/calendar/events/stats - 200 (0.015s)
2025-06-05 18:44:49,500 - INFO - ✅ PASS POST /api/calendar/events - 201 (0.010s)
2025-06-05 18:44:49,507 - INFO - ✅ PASS GET /api/calendar/events/2 - 200 (0.005s)
2025-06-05 18:44:49,518 - INFO - ✅ PASS PUT /api/calendar/events/2 - 200 (0.012s)
2025-06-05 18:44:49,527 - INFO - ✅ PASS GET /api/calendar/events/range?startDate=2025-06-05&endDate=2025-06-12 - 200 (0.008s)
2025-06-05 18:44:49,528 - INFO - 🗺️ Testing Maps Integration...
2025-06-05 18:44:50,175 - INFO - ✅ PASS POST /api/maps/geocode - 200 (0.647s)
2025-06-05 18:44:50,336 - INFO - ✅ PASS POST /api/maps/reverse-geocode - 200 (0.159s)
2025-06-05 18:44:50,452 - INFO - ✅ PASS POST /api/maps/detailed-geocode - 200 (0.116s)
2025-06-05 18:44:50,782 - INFO - ✅ PASS POST /api/maps/nearby-places - 200 (0.330s)
2025-06-05 18:44:51,004 - INFO - ✅ PASS POST /api/maps/distance - 200 (0.221s)
2025-06-05 18:44:51,020 - INFO - ✅ PASS GET /api/maps/static-map?latitude=40.7128&longitude=-74.0060&zoom=12 - 200 (0.015s)
2025-06-05 18:44:51,027 - INFO - ✅ PASS POST /api/maps/directions - 200 (0.006s)
2025-06-05 18:44:51,036 - INFO - ✅ PASS POST /api/maps/validate-coordinates - 200 (0.009s)
2025-06-05 18:44:51,037 - INFO - 📊 Testing Dashboard...
2025-06-05 18:44:51,270 - INFO - ✅ PASS GET /api/dashboard/stats - 200 (0.231s)
2025-06-05 18:44:51,279 - INFO - ✅ PASS GET /api/dashboard/financial-summary?startDate=2025-05-06&endDate=2025-06-05 - 200 (0.009s)
2025-06-05 18:44:51,279 - INFO - 🧹 Cleaning up test data...
2025-06-05 18:44:51,291 - INFO - ✅ PASS DELETE /api/calendar/events/2 - 200 (0.011s)
2025-06-05 18:44:51,305 - INFO - ✅ PASS DELETE /api/entertainment/2 - 200 (0.015s)
2025-06-05 18:44:51,330 - INFO - ✅ PASS DELETE /api/assets/2 - 200 (0.024s)
2025-06-05 18:44:51,339 - INFO - ✅ PASS DELETE /api/financial/expenses/2 - 200 (0.009s)
2025-06-05 18:44:51,350 - INFO - ✅ PASS DELETE /api/financial/income/2 - 200 (0.010s)
2025-06-05 18:44:51,368 - INFO - ✅ PASS DELETE /api/outclients/2 - 200 (0.019s)
2025-06-05 18:44:51,386 - INFO - ✅ PASS DELETE /api/clients/2 - 200 (0.017s)
2025-06-05 18:44:51,387 - INFO - 🏁 Test suite completed in 3.15 seconds
2025-06-05 18:44:51,389 - INFO - Summary report saved to: logs/test_summary_20250605_184451.txt
