2025-06-05 18:48:58,302 - INFO - Loaded existing auth token from file
2025-06-05 18:48:58,303 - INFO - 🚀 Starting Cymatics API Test Suite...
2025-06-05 18:48:58,304 - INFO - 🏥 Testing Health Check...
2025-06-05 18:48:58,311 - INFO - ✅ PASS GET /health - 200 (0.005s)
2025-06-05 18:48:58,313 - INFO - ℹ️ Testing API Info...
2025-06-05 18:48:58,322 - INFO - ✅ PASS GET /api - 200 (0.008s)
2025-06-05 18:48:58,323 - INFO - 🔐 Testing Authentication...
2025-06-05 18:48:58,438 - INFO - ✅ PASS GET /api/auth/profile - 200 (0.115s)
2025-06-05 18:48:58,439 - INFO - ✅ Using existing valid authentication token
2025-06-05 18:48:58,439 - INFO - 🎉 Authentication successful! All endpoints will be tested with proper authorization.
2025-06-05 18:48:58,440 - INFO - 👥 Testing Client Management...
2025-06-05 18:48:58,474 - INFO - ✅ PASS GET /api/clients - 200 (0.033s)
2025-06-05 18:48:58,540 - INFO - ✅ PASS GET /api/clients/stats - 200 (0.065s)
2025-06-05 18:48:58,550 - INFO - ✅ PASS GET /api/clients/dropdown - 200 (0.010s)
2025-06-05 18:48:58,618 - INFO - ✅ PASS POST /api/clients - 201 (0.067s)
2025-06-05 18:48:58,631 - INFO - ✅ PASS GET /api/clients/3 - 200 (0.013s)
2025-06-05 18:48:58,644 - INFO - ✅ PASS GET /api/clients/3/data - 200 (0.013s)
2025-06-05 18:48:58,694 - INFO - ✅ PASS PUT /api/clients/3 - 200 (0.049s)
2025-06-05 18:48:58,716 - INFO - ✅ PASS GET /api/clients/99999 - 404 (0.022s)
2025-06-05 18:48:58,716 - INFO - ✅ Invalid client ID test passed - correctly returned 404
2025-06-05 18:48:58,716 - INFO - 🏢 Testing Outclient Management...
2025-06-05 18:48:58,742 - INFO - ✅ PASS GET /api/outclients - 200 (0.024s)
2025-06-05 18:48:58,754 - INFO - ✅ PASS GET /api/outclients/stats - 200 (0.012s)
2025-06-05 18:48:58,768 - INFO - ✅ PASS GET /api/outclients/dropdown - 200 (0.013s)
2025-06-05 18:48:58,787 - INFO - ✅ PASS POST /api/outclients - 201 (0.019s)
2025-06-05 18:48:58,800 - INFO - ✅ PASS GET /api/outclients/3 - 200 (0.012s)
2025-06-05 18:48:58,826 - INFO - ✅ PASS PUT /api/outclients/3 - 200 (0.025s)
2025-06-05 18:48:58,826 - INFO - 📋 Testing Project Management...
2025-06-05 18:48:58,843 - INFO - ✅ PASS GET /api/projects - 200 (0.016s)
2025-06-05 18:48:58,915 - INFO - ✅ PASS GET /api/projects/stats - 200 (0.071s)
2025-06-05 18:48:58,925 - INFO - ✅ PASS GET /api/projects/codes - 200 (0.010s)
2025-06-05 18:48:59,279 - INFO - ✅ PASS POST /api/projects - 201 (0.353s)
2025-06-05 18:48:59,290 - INFO - ✅ PASS GET /api/projects/2 - 200 (0.011s)
2025-06-05 18:48:59,301 - INFO - ✅ PASS GET /api/projects/code/CYM-2 - 200 (0.010s)
2025-06-05 18:48:59,317 - INFO - ✅ PASS GET /api/projects/CYM-2/data - 200 (0.015s)
2025-06-05 18:48:59,353 - INFO - ✅ PASS PUT /api/projects/2 - 200 (0.037s)
2025-06-05 18:48:59,380 - INFO - ✅ PASS PUT /api/projects/2/status - 200 (0.027s)
2025-06-05 18:48:59,381 - INFO - 💰 Testing Financial Management...
2025-06-05 18:48:59,393 - INFO - ✅ PASS GET /api/financial/income - 200 (0.011s)
2025-06-05 18:48:59,407 - INFO - ✅ PASS POST /api/financial/income - 201 (0.013s)
2025-06-05 18:48:59,414 - INFO - ✅ PASS GET /api/financial/income/3 - 200 (0.006s)
2025-06-05 18:48:59,431 - INFO - ✅ PASS PUT /api/financial/income/3 - 200 (0.015s)
2025-06-05 18:48:59,444 - INFO - ✅ PASS GET /api/financial/expenses - 200 (0.012s)
2025-06-05 18:48:59,454 - INFO - ✅ PASS GET /api/financial/expenses/categories - 200 (0.010s)
2025-06-05 18:48:59,462 - INFO - ✅ PASS GET /api/financial/expenses/totals - 200 (0.009s)
2025-06-05 18:48:59,475 - INFO - ✅ PASS POST /api/financial/expenses - 201 (0.012s)
2025-06-05 18:48:59,485 - INFO - ✅ PASS GET /api/financial/expenses/3 - 200 (0.010s)
2025-06-05 18:48:59,500 - INFO - ✅ PASS PUT /api/financial/expenses/3 - 200 (0.014s)
2025-06-05 18:48:59,643 - INFO - ✅ PASS GET /api/financial/summary - 200 (0.142s)
2025-06-05 18:48:59,745 - INFO - ✅ PASS GET /api/financial/budget - 200 (0.102s)
2025-06-05 18:48:59,745 - INFO - 🏭 Testing Asset Management...
2025-06-05 18:48:59,772 - INFO - ✅ PASS GET /api/assets - 200 (0.027s)
2025-06-05 18:48:59,788 - INFO - ✅ PASS GET /api/assets/stats - 200 (0.015s)
2025-06-05 18:48:59,799 - INFO - ✅ PASS GET /api/assets/types - 200 (0.011s)
2025-06-05 18:48:59,814 - INFO - ✅ PASS POST /api/assets - 201 (0.014s)
2025-06-05 18:48:59,826 - INFO - ✅ PASS GET /api/assets/3 - 200 (0.011s)
2025-06-05 18:48:59,846 - INFO - ✅ PASS PUT /api/assets/3 - 200 (0.020s)
2025-06-05 18:48:59,847 - INFO - 🎬 Testing Entertainment Management...
2025-06-05 18:48:59,863 - INFO - ✅ PASS GET /api/entertainment - 200 (0.015s)
2025-06-05 18:48:59,876 - INFO - ✅ PASS GET /api/entertainment/stats - 200 (0.013s)
2025-06-05 18:48:59,887 - INFO - ✅ PASS GET /api/entertainment/types - 200 (0.009s)
2025-06-05 18:48:59,896 - INFO - ✅ PASS GET /api/entertainment/languages - 200 (0.009s)
2025-06-05 18:48:59,910 - INFO - ✅ PASS POST /api/entertainment - 201 (0.014s)
2025-06-05 18:48:59,922 - INFO - ✅ PASS GET /api/entertainment/3 - 200 (0.012s)
2025-06-05 18:48:59,941 - INFO - ✅ PASS PUT /api/entertainment/3 - 200 (0.017s)
2025-06-05 18:48:59,941 - INFO - 📅 Testing Calendar Management...
2025-06-05 18:48:59,956 - INFO - ✅ PASS GET /api/calendar/events - 200 (0.014s)
2025-06-05 18:48:59,964 - INFO - ✅ PASS GET /api/calendar/events/upcoming - 200 (0.008s)
2025-06-05 18:48:59,975 - INFO - ✅ PASS GET /api/calendar/events/today - 200 (0.010s)
2025-06-05 18:48:59,988 - INFO - ✅ PASS GET /api/calendar/events/week - 200 (0.012s)
2025-06-05 18:48:59,996 - INFO - ✅ PASS GET /api/calendar/events/month - 200 (0.008s)
2025-06-05 18:49:00,013 - INFO - ✅ PASS GET /api/calendar/events/stats - 200 (0.016s)
2025-06-05 18:49:00,029 - INFO - ✅ PASS POST /api/calendar/events - 201 (0.015s)
2025-06-05 18:49:00,039 - INFO - ✅ PASS GET /api/calendar/events/3 - 200 (0.010s)
2025-06-05 18:49:00,054 - INFO - ✅ PASS PUT /api/calendar/events/3 - 200 (0.014s)
2025-06-05 18:49:00,063 - INFO - ✅ PASS GET /api/calendar/events/range?startDate=2025-06-05&endDate=2025-06-12 - 200 (0.009s)
2025-06-05 18:49:00,063 - INFO - 🗺️ Testing Maps Integration...
2025-06-05 18:49:00,182 - INFO - ✅ PASS POST /api/maps/geocode - 200 (0.119s)
2025-06-05 18:49:00,431 - INFO - ✅ PASS POST /api/maps/reverse-geocode - 200 (0.249s)
2025-06-05 18:49:00,541 - INFO - ✅ PASS POST /api/maps/detailed-geocode - 200 (0.108s)
2025-06-05 18:49:00,791 - INFO - ✅ PASS POST /api/maps/nearby-places - 200 (0.250s)
2025-06-05 18:49:00,969 - INFO - ✅ PASS POST /api/maps/distance - 200 (0.178s)
2025-06-05 18:49:00,977 - INFO - ✅ PASS GET /api/maps/static-map?latitude=40.7128&longitude=-74.0060&zoom=12 - 200 (0.007s)
2025-06-05 18:49:00,985 - INFO - ✅ PASS POST /api/maps/directions - 200 (0.007s)
2025-06-05 18:49:00,989 - INFO - ✅ PASS POST /api/maps/validate-coordinates - 200 (0.004s)
2025-06-05 18:49:00,989 - INFO - 📊 Testing Dashboard...
2025-06-05 18:49:01,287 - INFO - ✅ PASS GET /api/dashboard/stats - 200 (0.297s)
2025-06-05 18:49:01,295 - INFO - ✅ PASS GET /api/dashboard/financial-summary?startDate=2025-05-06&endDate=2025-06-05 - 200 (0.007s)
2025-06-05 18:49:01,296 - INFO - 🧹 Cleaning up test data...
2025-06-05 18:49:01,309 - INFO - ✅ PASS DELETE /api/calendar/events/3 - 200 (0.013s)
2025-06-05 18:49:01,325 - INFO - ✅ PASS DELETE /api/entertainment/3 - 200 (0.015s)
2025-06-05 18:49:01,340 - INFO - ✅ PASS DELETE /api/assets/3 - 200 (0.015s)
2025-06-05 18:49:01,352 - INFO - ✅ PASS DELETE /api/financial/expenses/3 - 200 (0.011s)
2025-06-05 18:49:01,361 - INFO - ✅ PASS DELETE /api/financial/income/3 - 200 (0.008s)
2025-06-05 18:49:01,376 - INFO - ✅ PASS DELETE /api/projects/2 - 200 (0.015s)
2025-06-05 18:49:01,393 - INFO - ✅ PASS DELETE /api/outclients/3 - 200 (0.016s)
2025-06-05 18:49:01,407 - INFO - ✅ PASS DELETE /api/clients/3 - 200 (0.014s)
2025-06-05 18:49:01,408 - INFO - 🏁 Test suite completed in 3.10 seconds
2025-06-05 18:49:01,409 - INFO - Summary report saved to: logs/test_summary_20250605_184901.txt
