2025-06-06 10:38:46,917 - INFO - Loaded existing auth token from file
2025-06-06 10:38:46,918 - INFO - 🚀 Starting Cymatics API Test Suite...
2025-06-06 10:38:46,919 - INFO - 🏥 Testing Health Check...
2025-06-06 10:38:46,925 - INFO - ✅ PASS GET /health - 200 (0.005s)
2025-06-06 10:38:46,925 - INFO - ℹ️ Testing API Info...
2025-06-06 10:38:46,931 - INFO - ✅ PASS GET /api - 200 (0.005s)
2025-06-06 10:38:46,932 - INFO - 🔐 Testing Authentication...
2025-06-06 10:38:47,068 - INFO - ✅ PASS GET /api/auth/profile - 200 (0.135s)
2025-06-06 10:38:47,068 - INFO - ✅ Using existing valid authentication token
2025-06-06 10:38:47,069 - INFO - 🎉 Authentication successful! All endpoints will be tested with proper authorization.
2025-06-06 10:38:47,070 - INFO - 👥 Testing Client Management...
2025-06-06 10:38:47,110 - INFO - ✅ PASS GET /api/clients - 200 (0.039s)
2025-06-06 10:38:47,174 - INFO - ✅ PASS GET /api/clients/stats - 200 (0.064s)
2025-06-06 10:38:47,182 - INFO - ✅ PASS GET /api/clients/dropdown - 200 (0.008s)
2025-06-06 10:38:47,238 - INFO - ✅ PASS POST /api/clients - 201 (0.056s)
2025-06-06 10:38:47,248 - INFO - ✅ PASS GET /api/clients/4 - 200 (0.010s)
2025-06-06 10:38:47,257 - INFO - ✅ PASS GET /api/clients/4/data - 200 (0.008s)
2025-06-06 10:38:47,280 - INFO - ✅ PASS PUT /api/clients/4 - 200 (0.023s)
2025-06-06 10:38:47,296 - INFO - ✅ PASS GET /api/clients/99999 - 404 (0.017s)
2025-06-06 10:38:47,297 - INFO - ✅ Invalid client ID test passed - correctly returned 404
2025-06-06 10:38:47,297 - INFO - 🏢 Testing Outclient Management...
2025-06-06 10:38:47,308 - INFO - ✅ PASS GET /api/outclients - 200 (0.010s)
2025-06-06 10:38:47,318 - INFO - ✅ PASS GET /api/outclients/stats - 200 (0.010s)
2025-06-06 10:38:47,324 - INFO - ✅ PASS GET /api/outclients/dropdown - 200 (0.006s)
2025-06-06 10:38:47,336 - INFO - ✅ PASS POST /api/outclients - 201 (0.012s)
2025-06-06 10:38:47,345 - INFO - ✅ PASS GET /api/outclients/4 - 200 (0.009s)
2025-06-06 10:38:47,364 - INFO - ✅ PASS PUT /api/outclients/4 - 200 (0.019s)
2025-06-06 10:38:47,365 - INFO - 📋 Testing Project Management...
2025-06-06 10:38:47,378 - INFO - ✅ PASS GET /api/projects - 200 (0.013s)
2025-06-06 10:38:47,467 - INFO - ✅ PASS GET /api/projects/stats - 200 (0.088s)
2025-06-06 10:38:47,475 - INFO - ✅ PASS GET /api/projects/codes - 200 (0.007s)
2025-06-06 10:38:47,897 - INFO - ✅ PASS POST /api/projects - 201 (0.422s)
2025-06-06 10:38:47,907 - INFO - ✅ PASS GET /api/projects/3 - 200 (0.009s)
2025-06-06 10:38:47,917 - INFO - ✅ PASS GET /api/projects/code/CYM-3 - 200 (0.010s)
2025-06-06 10:38:47,928 - INFO - ✅ PASS GET /api/projects/CYM-3/data - 200 (0.010s)
2025-06-06 10:38:47,970 - INFO - ✅ PASS PUT /api/projects/3 - 200 (0.041s)
2025-06-06 10:38:48,004 - INFO - ✅ PASS PUT /api/projects/3/status - 200 (0.033s)
2025-06-06 10:38:48,004 - INFO - 📋 Testing Enhanced Project Management...
2025-06-06 10:38:48,014 - INFO - ✅ PASS GET /api/projects/status/pending - 200 (0.010s)
2025-06-06 10:38:48,023 - INFO - ✅ PASS GET /api/projects/status/ongoing - 200 (0.008s)
2025-06-06 10:38:48,032 - INFO - ✅ PASS GET /api/projects/status/completed - 200 (0.008s)
2025-06-06 10:38:48,032 - INFO - 💰 Testing Financial Management...
2025-06-06 10:38:48,046 - INFO - ✅ PASS GET /api/financial/income - 200 (0.012s)
2025-06-06 10:38:48,061 - INFO - ✅ PASS POST /api/financial/income - 201 (0.013s)
2025-06-06 10:38:48,068 - INFO - ✅ PASS GET /api/financial/income/4 - 200 (0.007s)
2025-06-06 10:38:48,081 - INFO - ✅ PASS PUT /api/financial/income/4 - 200 (0.013s)
2025-06-06 10:38:48,092 - INFO - ✅ PASS GET /api/financial/expenses - 200 (0.011s)
2025-06-06 10:38:48,100 - INFO - ✅ PASS GET /api/financial/expenses/categories - 200 (0.007s)
2025-06-06 10:38:48,109 - INFO - ✅ PASS GET /api/financial/expenses/totals - 200 (0.009s)
2025-06-06 10:38:48,120 - INFO - ✅ PASS POST /api/financial/expenses - 201 (0.011s)
2025-06-06 10:38:48,130 - INFO - ✅ PASS GET /api/financial/expenses/4 - 200 (0.009s)
2025-06-06 10:38:48,142 - INFO - ✅ PASS PUT /api/financial/expenses/4 - 200 (0.012s)
2025-06-06 10:38:48,202 - INFO - ✅ PASS GET /api/financial/summary - 200 (0.059s)
2025-06-06 10:38:48,255 - INFO - ✅ PASS GET /api/financial/budget - 200 (0.053s)
2025-06-06 10:38:48,359 - INFO - ✅ PASS GET /api/financial/income/chart-data - 200 (0.104s)
2025-06-06 10:38:48,403 - INFO - ✅ PASS GET /api/financial/income/chart-data?period=12months - 200 (0.044s)
2025-06-06 10:38:48,414 - INFO - ✅ PASS GET /api/financial/expenses/categorized - 200 (0.011s)
2025-06-06 10:38:48,424 - INFO - ✅ PASS GET /api/financial/expenses/categorized?period=12months - 200 (0.009s)
2025-06-06 10:38:48,425 - INFO - 💰 Testing Budget Management...
2025-06-06 10:38:48,466 - INFO - ✅ PASS GET /api/budget/overview - 200 (0.041s)
2025-06-06 10:38:48,475 - INFO - ✅ PASS GET /api/budget/categories - 200 (0.008s)
2025-06-06 10:38:48,481 - INFO - ✅ PASS GET /api/budget/investment-details - 200 (0.005s)
2025-06-06 10:38:48,488 - INFO - ✅ PASS POST /api/budget/categories - 201 (0.007s)
2025-06-06 10:38:48,494 - INFO - ✅ PASS PUT /api/budget/categories/1749186528485 - 200 (0.005s)
2025-06-06 10:38:48,499 - INFO - ✅ PASS DELETE /api/budget/categories/1749186528485 - 200 (0.004s)
2025-06-06 10:38:48,500 - INFO - 💳 Testing Payment Management...
2025-06-06 10:38:48,580 - INFO - ✅ PASS GET /api/payments/stats - 200 (0.080s)
2025-06-06 10:38:48,589 - INFO - ✅ PASS GET /api/payments - 200 (0.008s)
2025-06-06 10:38:48,598 - INFO - ✅ PASS GET /api/payments?page=1&limit=5 - 200 (0.008s)
2025-06-06 10:38:48,606 - INFO - ✅ PASS GET /api/payments/status/pending - 200 (0.008s)
2025-06-06 10:38:48,613 - INFO - ✅ PASS GET /api/payments/status/ongoing - 200 (0.007s)
2025-06-06 10:38:48,621 - INFO - ✅ PASS GET /api/payments/status/completed - 200 (0.007s)
2025-06-06 10:38:48,631 - INFO - ✅ PASS POST /api/payments - 201 (0.010s)
2025-06-06 10:38:48,640 - INFO - ✅ PASS GET /api/payments/4 - 200 (0.007s)
2025-06-06 10:38:48,662 - INFO - ❌ FAIL PUT /api/payments/4 - 400 (0.022s)
2025-06-06 10:38:48,663 - ERROR - Error: {"success":false,"error":{"code":"DATABASE_VALIDATION_ERROR","message":"Database validation error"},"timestamp":"2025-06-06T05:08:48.661Z"}
2025-06-06 10:38:48,676 - INFO - ✅ PASS PUT /api/payments/4/status - 200 (0.012s)
2025-06-06 10:38:48,688 - INFO - ✅ PASS DELETE /api/payments/4 - 200 (0.011s)
2025-06-06 10:38:48,688 - INFO - 🏭 Testing Asset Management...
2025-06-06 10:38:48,699 - INFO - ✅ PASS GET /api/assets - 200 (0.010s)
2025-06-06 10:38:48,708 - INFO - ✅ PASS GET /api/assets/stats - 200 (0.010s)
2025-06-06 10:38:48,714 - INFO - ✅ PASS GET /api/assets/types - 200 (0.006s)
2025-06-06 10:38:48,725 - INFO - ✅ PASS POST /api/assets - 201 (0.011s)
2025-06-06 10:38:48,731 - INFO - ✅ PASS GET /api/assets/4 - 200 (0.005s)
2025-06-06 10:38:48,744 - INFO - ✅ PASS PUT /api/assets/4 - 200 (0.012s)
2025-06-06 10:38:48,744 - INFO - 🎬 Testing Entertainment Management...
2025-06-06 10:38:48,755 - INFO - ✅ PASS GET /api/entertainment - 200 (0.010s)
2025-06-06 10:38:48,765 - INFO - ✅ PASS GET /api/entertainment/stats - 200 (0.010s)
2025-06-06 10:38:48,771 - INFO - ✅ PASS GET /api/entertainment/types - 200 (0.006s)
2025-06-06 10:38:48,777 - INFO - ✅ PASS GET /api/entertainment/languages - 200 (0.005s)
2025-06-06 10:38:48,787 - INFO - ✅ PASS POST /api/entertainment - 201 (0.010s)
2025-06-06 10:38:48,793 - INFO - ✅ PASS GET /api/entertainment/4 - 200 (0.006s)
2025-06-06 10:38:48,804 - INFO - ✅ PASS PUT /api/entertainment/4 - 200 (0.011s)
2025-06-06 10:38:48,804 - INFO - 📅 Testing Calendar Management...
2025-06-06 10:38:48,814 - INFO - ✅ PASS GET /api/calendar/events - 200 (0.009s)
2025-06-06 10:38:48,822 - INFO - ✅ PASS GET /api/calendar/events/upcoming - 200 (0.007s)
2025-06-06 10:38:48,829 - INFO - ✅ PASS GET /api/calendar/events/today - 200 (0.007s)
2025-06-06 10:38:48,836 - INFO - ✅ PASS GET /api/calendar/events/week - 200 (0.007s)
2025-06-06 10:38:48,845 - INFO - ✅ PASS GET /api/calendar/events/month - 200 (0.007s)
2025-06-06 10:38:48,854 - INFO - ✅ PASS GET /api/calendar/events/stats - 200 (0.009s)
2025-06-06 10:38:48,863 - INFO - ✅ PASS POST /api/calendar/events - 201 (0.008s)
2025-06-06 10:38:48,869 - INFO - ✅ PASS GET /api/calendar/events/4 - 200 (0.006s)
2025-06-06 10:38:48,879 - INFO - ✅ PASS PUT /api/calendar/events/4 - 200 (0.009s)
2025-06-06 10:38:48,887 - INFO - ✅ PASS GET /api/calendar/events/range?startDate=2025-06-06&endDate=2025-06-13 - 200 (0.008s)
2025-06-06 10:38:48,887 - INFO - 🗺️ Testing Maps Integration...
2025-06-06 10:38:49,013 - INFO - ✅ PASS POST /api/maps/geocode - 200 (0.125s)
2025-06-06 10:38:49,226 - INFO - ✅ PASS POST /api/maps/reverse-geocode - 200 (0.212s)
2025-06-06 10:38:49,351 - INFO - ✅ PASS POST /api/maps/detailed-geocode - 200 (0.122s)
2025-06-06 10:38:49,830 - INFO - ✅ PASS POST /api/maps/nearby-places - 200 (0.479s)
2025-06-06 10:38:50,077 - INFO - ✅ PASS POST /api/maps/distance - 200 (0.248s)
2025-06-06 10:38:50,090 - INFO - ✅ PASS GET /api/maps/static-map?latitude=40.7128&longitude=-74.0060&zoom=12 - 200 (0.011s)
2025-06-06 10:38:50,098 - INFO - ✅ PASS POST /api/maps/directions - 200 (0.008s)
2025-06-06 10:38:50,107 - INFO - ✅ PASS POST /api/maps/validate-coordinates - 200 (0.009s)
2025-06-06 10:38:50,108 - INFO - 📊 Testing Dashboard...
2025-06-06 10:38:50,273 - INFO - ✅ PASS GET /api/dashboard/stats - 200 (0.165s)
2025-06-06 10:38:50,282 - INFO - ✅ PASS GET /api/dashboard/financial-summary?startDate=2025-05-07&endDate=2025-06-06 - 200 (0.008s)
2025-06-06 10:38:50,295 - INFO - ✅ PASS GET /api/dashboard/today-schedule - 200 (0.012s)
2025-06-06 10:38:50,314 - INFO - ✅ PASS GET /api/dashboard/charts/income-expense - 200 (0.018s)
2025-06-06 10:38:50,340 - INFO - ✅ PASS GET /api/dashboard/charts/income-expense?period=12months - 200 (0.026s)
2025-06-06 10:38:50,347 - INFO - ✅ PASS GET /api/dashboard/charts/project-details - 200 (0.006s)
2025-06-06 10:38:50,354 - INFO - ✅ PASS GET /api/dashboard/charts/expense-breakdown - 200 (0.006s)
2025-06-06 10:38:50,361 - INFO - ✅ PASS GET /api/dashboard/charts/expense-breakdown?period=12months - 200 (0.006s)
2025-06-06 10:38:50,361 - INFO - 🧹 Cleaning up test data...
2025-06-06 10:38:50,371 - INFO - ✅ PASS DELETE /api/calendar/events/4 - 200 (0.009s)
2025-06-06 10:38:50,385 - INFO - ✅ PASS DELETE /api/entertainment/4 - 200 (0.014s)
2025-06-06 10:38:50,398 - INFO - ✅ PASS DELETE /api/assets/4 - 200 (0.013s)
2025-06-06 10:38:50,408 - INFO - ✅ PASS DELETE /api/financial/expenses/4 - 200 (0.010s)
2025-06-06 10:38:50,417 - INFO - ✅ PASS DELETE /api/financial/income/4 - 200 (0.009s)
2025-06-06 10:38:50,435 - INFO - ✅ PASS DELETE /api/projects/3 - 200 (0.017s)
2025-06-06 10:38:50,454 - INFO - ✅ PASS DELETE /api/outclients/4 - 200 (0.019s)
2025-06-06 10:38:50,465 - INFO - ✅ PASS DELETE /api/clients/4 - 200 (0.011s)
2025-06-06 10:38:50,466 - INFO - 🏁 Test suite completed in 3.55 seconds
2025-06-06 10:38:50,468 - INFO - Summary report saved to: logs/test_summary_20250606_103850.txt
