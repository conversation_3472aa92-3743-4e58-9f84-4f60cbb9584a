2025-06-06 10:40:45,398 - INFO - Loaded existing auth token from file
2025-06-06 10:40:45,399 - INFO - 🚀 Starting Cymatics API Test Suite...
2025-06-06 10:40:45,401 - INFO - 🏥 Testing Health Check...
2025-06-06 10:40:45,407 - INFO - ✅ PASS GET /health - 200 (0.006s)
2025-06-06 10:40:45,407 - INFO - ℹ️ Testing API Info...
2025-06-06 10:40:45,415 - INFO - ✅ PASS GET /api - 200 (0.006s)
2025-06-06 10:40:45,415 - INFO - 🔐 Testing Authentication...
2025-06-06 10:40:45,438 - INFO - ✅ PASS GET /api/auth/profile - 200 (0.022s)
2025-06-06 10:40:45,439 - INFO - ✅ Using existing valid authentication token
2025-06-06 10:40:45,440 - INFO - 🎉 Authentication successful! All endpoints will be tested with proper authorization.
2025-06-06 10:40:45,440 - INFO - 👥 Testing Client Management...
2025-06-06 10:40:45,468 - INFO - ✅ PASS GET /api/clients - 200 (0.027s)
2025-06-06 10:40:45,543 - INFO - ✅ PASS GET /api/clients/stats - 200 (0.075s)
2025-06-06 10:40:45,554 - INFO - ✅ PASS GET /api/clients/dropdown - 200 (0.010s)
2025-06-06 10:40:45,585 - INFO - ✅ PASS POST /api/clients - 201 (0.030s)
2025-06-06 10:40:45,596 - INFO - ✅ PASS GET /api/clients/5 - 200 (0.011s)
2025-06-06 10:40:45,604 - INFO - ✅ PASS GET /api/clients/5/data - 200 (0.007s)
2025-06-06 10:40:45,621 - INFO - ✅ PASS PUT /api/clients/5 - 200 (0.017s)
2025-06-06 10:40:45,632 - INFO - ✅ PASS GET /api/clients/99999 - 404 (0.010s)
2025-06-06 10:40:45,632 - INFO - ✅ Invalid client ID test passed - correctly returned 404
2025-06-06 10:40:45,632 - INFO - 🏢 Testing Outclient Management...
2025-06-06 10:40:45,644 - INFO - ✅ PASS GET /api/outclients - 200 (0.009s)
2025-06-06 10:40:45,653 - INFO - ✅ PASS GET /api/outclients/stats - 200 (0.009s)
2025-06-06 10:40:45,662 - INFO - ✅ PASS GET /api/outclients/dropdown - 200 (0.009s)
2025-06-06 10:40:45,676 - INFO - ✅ PASS POST /api/outclients - 201 (0.014s)
2025-06-06 10:40:45,686 - INFO - ✅ PASS GET /api/outclients/5 - 200 (0.009s)
2025-06-06 10:40:45,706 - INFO - ✅ PASS PUT /api/outclients/5 - 200 (0.020s)
2025-06-06 10:40:45,707 - INFO - 📋 Testing Project Management...
2025-06-06 10:40:45,720 - INFO - ✅ PASS GET /api/projects - 200 (0.013s)
2025-06-06 10:40:45,798 - INFO - ✅ PASS GET /api/projects/stats - 200 (0.078s)
2025-06-06 10:40:45,806 - INFO - ✅ PASS GET /api/projects/codes - 200 (0.007s)
2025-06-06 10:40:46,096 - INFO - ✅ PASS POST /api/projects - 201 (0.290s)
2025-06-06 10:40:46,108 - INFO - ✅ PASS GET /api/projects/5 - 200 (0.011s)
2025-06-06 10:40:46,121 - INFO - ✅ PASS GET /api/projects/code/CYM-5 - 200 (0.011s)
2025-06-06 10:40:46,129 - INFO - ✅ PASS GET /api/projects/CYM-5/data - 200 (0.009s)
2025-06-06 10:40:46,164 - INFO - ✅ PASS PUT /api/projects/5 - 200 (0.034s)
2025-06-06 10:40:46,209 - INFO - ✅ PASS PUT /api/projects/5/status - 200 (0.045s)
2025-06-06 10:40:46,210 - INFO - 📋 Testing Enhanced Project Management...
2025-06-06 10:40:46,226 - INFO - ✅ PASS GET /api/projects/status/pending - 200 (0.016s)
2025-06-06 10:40:46,239 - INFO - ✅ PASS GET /api/projects/status/ongoing - 200 (0.011s)
2025-06-06 10:40:46,250 - INFO - ✅ PASS GET /api/projects/status/completed - 200 (0.010s)
2025-06-06 10:40:46,250 - INFO - 💰 Testing Financial Management...
2025-06-06 10:40:46,270 - INFO - ✅ PASS GET /api/financial/income - 200 (0.020s)
2025-06-06 10:40:46,283 - INFO - ✅ PASS POST /api/financial/income - 201 (0.012s)
2025-06-06 10:40:46,290 - INFO - ✅ PASS GET /api/financial/income/5 - 200 (0.007s)
2025-06-06 10:40:46,304 - INFO - ✅ PASS PUT /api/financial/income/5 - 200 (0.014s)
2025-06-06 10:40:46,315 - INFO - ✅ PASS GET /api/financial/expenses - 200 (0.011s)
2025-06-06 10:40:46,323 - INFO - ✅ PASS GET /api/financial/expenses/categories - 200 (0.007s)
2025-06-06 10:40:46,332 - INFO - ✅ PASS GET /api/financial/expenses/totals - 200 (0.008s)
2025-06-06 10:40:46,343 - INFO - ✅ PASS POST /api/financial/expenses - 201 (0.010s)
2025-06-06 10:40:46,351 - INFO - ✅ PASS GET /api/financial/expenses/5 - 200 (0.009s)
2025-06-06 10:40:46,366 - INFO - ✅ PASS PUT /api/financial/expenses/5 - 200 (0.012s)
2025-06-06 10:40:46,374 - INFO - ✅ PASS GET /api/financial/summary - 200 (0.008s)
2025-06-06 10:40:46,451 - INFO - ✅ PASS GET /api/financial/budget - 200 (0.077s)
2025-06-06 10:40:46,502 - INFO - ✅ PASS GET /api/financial/income/chart-data - 200 (0.051s)
2025-06-06 10:40:46,549 - INFO - ✅ PASS GET /api/financial/income/chart-data?period=12months - 200 (0.046s)
2025-06-06 10:40:46,561 - INFO - ✅ PASS GET /api/financial/expenses/categorized - 200 (0.012s)
2025-06-06 10:40:46,571 - INFO - ✅ PASS GET /api/financial/expenses/categorized?period=12months - 200 (0.010s)
2025-06-06 10:40:46,571 - INFO - 💰 Testing Budget Management...
2025-06-06 10:40:46,609 - INFO - ✅ PASS GET /api/budget/overview - 200 (0.037s)
2025-06-06 10:40:46,619 - INFO - ✅ PASS GET /api/budget/categories - 200 (0.009s)
2025-06-06 10:40:46,625 - INFO - ✅ PASS GET /api/budget/investment-details - 200 (0.005s)
2025-06-06 10:40:46,631 - INFO - ✅ PASS POST /api/budget/categories - 201 (0.005s)
2025-06-06 10:40:46,638 - INFO - ✅ PASS PUT /api/budget/categories/1749186646629 - 200 (0.007s)
2025-06-06 10:40:46,643 - INFO - ✅ PASS DELETE /api/budget/categories/1749186646629 - 200 (0.005s)
2025-06-06 10:40:46,644 - INFO - 💳 Testing Payment Management...
2025-06-06 10:40:46,737 - INFO - ✅ PASS GET /api/payments/stats - 200 (0.092s)
2025-06-06 10:40:46,749 - INFO - ✅ PASS GET /api/payments - 200 (0.012s)
2025-06-06 10:40:46,761 - INFO - ✅ PASS GET /api/payments?page=1&limit=5 - 200 (0.011s)
2025-06-06 10:40:46,772 - INFO - ✅ PASS GET /api/payments/status/pending - 200 (0.010s)
2025-06-06 10:40:46,781 - INFO - ✅ PASS GET /api/payments/status/ongoing - 200 (0.009s)
2025-06-06 10:40:46,788 - INFO - ✅ PASS GET /api/payments/status/completed - 200 (0.006s)
2025-06-06 10:40:46,801 - INFO - ✅ PASS POST /api/payments - 201 (0.012s)
2025-06-06 10:40:46,810 - INFO - ✅ PASS GET /api/payments/6 - 200 (0.009s)
2025-06-06 10:40:46,823 - INFO - ✅ PASS PUT /api/payments/6 - 200 (0.012s)
2025-06-06 10:40:46,835 - INFO - ✅ PASS PUT /api/payments/6/status - 200 (0.012s)
2025-06-06 10:40:46,847 - INFO - ✅ PASS DELETE /api/payments/6 - 200 (0.011s)
2025-06-06 10:40:46,848 - INFO - 🏭 Testing Asset Management...
2025-06-06 10:40:46,861 - INFO - ✅ PASS GET /api/assets - 200 (0.012s)
2025-06-06 10:40:46,872 - INFO - ✅ PASS GET /api/assets/stats - 200 (0.011s)
2025-06-06 10:40:46,880 - INFO - ✅ PASS GET /api/assets/types - 200 (0.008s)
2025-06-06 10:40:46,890 - INFO - ✅ PASS POST /api/assets - 201 (0.011s)
2025-06-06 10:40:46,898 - INFO - ✅ PASS GET /api/assets/5 - 200 (0.008s)
2025-06-06 10:40:46,911 - INFO - ✅ PASS PUT /api/assets/5 - 200 (0.013s)
2025-06-06 10:40:46,911 - INFO - 🎬 Testing Entertainment Management...
2025-06-06 10:40:46,921 - INFO - ✅ PASS GET /api/entertainment - 200 (0.009s)
2025-06-06 10:40:46,931 - INFO - ✅ PASS GET /api/entertainment/stats - 200 (0.010s)
2025-06-06 10:40:46,937 - INFO - ✅ PASS GET /api/entertainment/types - 200 (0.005s)
2025-06-06 10:40:46,943 - INFO - ✅ PASS GET /api/entertainment/languages - 200 (0.005s)
2025-06-06 10:40:46,955 - INFO - ✅ PASS POST /api/entertainment - 201 (0.010s)
2025-06-06 10:40:46,963 - INFO - ✅ PASS GET /api/entertainment/5 - 200 (0.007s)
2025-06-06 10:40:46,975 - INFO - ✅ PASS PUT /api/entertainment/5 - 200 (0.013s)
2025-06-06 10:40:46,976 - INFO - 📅 Testing Calendar Management...
2025-06-06 10:40:46,987 - INFO - ✅ PASS GET /api/calendar/events - 200 (0.011s)
2025-06-06 10:40:46,994 - INFO - ✅ PASS GET /api/calendar/events/upcoming - 200 (0.006s)
2025-06-06 10:40:47,003 - INFO - ✅ PASS GET /api/calendar/events/today - 200 (0.009s)
2025-06-06 10:40:47,010 - INFO - ✅ PASS GET /api/calendar/events/week - 200 (0.007s)
2025-06-06 10:40:47,019 - INFO - ✅ PASS GET /api/calendar/events/month - 200 (0.008s)
2025-06-06 10:40:47,028 - INFO - ✅ PASS GET /api/calendar/events/stats - 200 (0.010s)
2025-06-06 10:40:47,038 - INFO - ✅ PASS POST /api/calendar/events - 201 (0.009s)
2025-06-06 10:40:47,047 - INFO - ✅ PASS GET /api/calendar/events/5 - 200 (0.008s)
2025-06-06 10:40:47,058 - INFO - ✅ PASS PUT /api/calendar/events/5 - 200 (0.009s)
2025-06-06 10:40:47,068 - INFO - ✅ PASS GET /api/calendar/events/range?startDate=2025-06-06&endDate=2025-06-13 - 200 (0.009s)
2025-06-06 10:40:47,068 - INFO - 🗺️ Testing Maps Integration...
2025-06-06 10:40:47,186 - INFO - ✅ PASS POST /api/maps/geocode - 200 (0.119s)
2025-06-06 10:40:47,356 - INFO - ✅ PASS POST /api/maps/reverse-geocode - 200 (0.167s)
2025-06-06 10:40:47,514 - INFO - ✅ PASS POST /api/maps/detailed-geocode - 200 (0.158s)
2025-06-06 10:40:47,689 - INFO - ✅ PASS POST /api/maps/nearby-places - 200 (0.174s)
2025-06-06 10:40:47,859 - INFO - ✅ PASS POST /api/maps/distance - 200 (0.170s)
2025-06-06 10:40:47,872 - INFO - ✅ PASS GET /api/maps/static-map?latitude=40.7128&longitude=-74.0060&zoom=12 - 200 (0.013s)
2025-06-06 10:40:47,879 - INFO - ✅ PASS POST /api/maps/directions - 200 (0.006s)
2025-06-06 10:40:47,885 - INFO - ✅ PASS POST /api/maps/validate-coordinates - 200 (0.005s)
2025-06-06 10:40:47,885 - INFO - 📊 Testing Dashboard...
2025-06-06 10:40:48,062 - INFO - ✅ PASS GET /api/dashboard/stats - 200 (0.175s)
2025-06-06 10:40:48,073 - INFO - ✅ PASS GET /api/dashboard/financial-summary?startDate=2025-05-07&endDate=2025-06-06 - 200 (0.010s)
2025-06-06 10:40:48,083 - INFO - ✅ PASS GET /api/dashboard/today-schedule - 200 (0.009s)
2025-06-06 10:40:48,111 - INFO - ✅ PASS GET /api/dashboard/charts/income-expense - 200 (0.027s)
2025-06-06 10:40:48,157 - INFO - ✅ PASS GET /api/dashboard/charts/income-expense?period=12months - 200 (0.045s)
2025-06-06 10:40:48,165 - INFO - ✅ PASS GET /api/dashboard/charts/project-details - 200 (0.008s)
2025-06-06 10:40:48,175 - INFO - ✅ PASS GET /api/dashboard/charts/expense-breakdown - 200 (0.009s)
2025-06-06 10:40:48,184 - INFO - ✅ PASS GET /api/dashboard/charts/expense-breakdown?period=12months - 200 (0.009s)
2025-06-06 10:40:48,184 - INFO - 🧹 Cleaning up test data...
2025-06-06 10:40:48,198 - INFO - ✅ PASS DELETE /api/calendar/events/5 - 200 (0.014s)
2025-06-06 10:40:48,211 - INFO - ✅ PASS DELETE /api/entertainment/5 - 200 (0.014s)
2025-06-06 10:40:48,224 - INFO - ✅ PASS DELETE /api/assets/5 - 200 (0.013s)
2025-06-06 10:40:48,233 - INFO - ✅ PASS DELETE /api/financial/expenses/5 - 200 (0.009s)
2025-06-06 10:40:48,244 - INFO - ✅ PASS DELETE /api/financial/income/5 - 200 (0.010s)
2025-06-06 10:40:48,263 - INFO - ✅ PASS DELETE /api/projects/5 - 200 (0.019s)
2025-06-06 10:40:48,278 - INFO - ✅ PASS DELETE /api/outclients/5 - 200 (0.014s)
2025-06-06 10:40:48,292 - INFO - ✅ PASS DELETE /api/clients/5 - 200 (0.013s)
2025-06-06 10:40:48,293 - INFO - 🏁 Test suite completed in 2.89 seconds
2025-06-06 10:40:48,295 - INFO - Summary report saved to: logs/test_summary_20250606_104048.txt
