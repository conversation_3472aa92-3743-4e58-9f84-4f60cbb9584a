2025-06-09 14:48:33,862 - INFO - Loaded existing auth token from file
2025-06-09 14:48:33,862 - INFO - 🚀 Starting Cymatics API Test Suite...
2025-06-09 14:48:33,863 - INFO - 🏥 Testing Health Check...
2025-06-09 14:48:35,890 - INFO - ✅ PASS GET /health - 200 (2.027s)
2025-06-09 14:48:35,891 - INFO - ℹ️ Testing API Info...
2025-06-09 14:48:35,934 - INFO - ✅ PASS GET /api - 200 (0.043s)
2025-06-09 14:48:35,935 - INFO - 🔐 Testing Authentication...
2025-06-09 14:48:35,961 - INFO - ✅ PASS GET /api/auth/profile - 200 (0.026s)
2025-06-09 14:48:35,961 - INFO - ✅ Using existing valid authentication token
2025-06-09 14:48:35,962 - INFO - 🎉 Authentication successful! All endpoints will be tested with proper authorization.
2025-06-09 14:48:35,963 - INFO - 👥 Testing Client Management...
2025-06-09 14:48:36,043 - INFO - ✅ PASS GET /api/clients - 200 (0.080s)
2025-06-09 14:48:36,166 - INFO - ✅ PASS GET /api/clients/stats - 200 (0.122s)
2025-06-09 14:48:36,181 - INFO - ✅ PASS GET /api/clients/dropdown - 200 (0.014s)
2025-06-09 14:48:36,236 - INFO - ❌ FAIL POST /api/clients - 409 (0.054s)
2025-06-09 14:48:36,237 - ERROR - Error: {"success":false,"error":{"code":"CONFLICT_ERROR","message":"Client with this email already exists"},"timestamp":"2025-06-09T09:18:36.235Z"}
2025-06-09 14:48:36,257 - INFO - ✅ PASS GET /api/clients/99999 - 404 (0.021s)
2025-06-09 14:48:36,259 - INFO - ✅ Invalid client ID test passed - correctly returned 404
2025-06-09 14:48:36,259 - INFO - 🏢 Testing Outclient Management...
2025-06-09 14:48:36,274 - INFO - ✅ PASS GET /api/outclients - 200 (0.015s)
2025-06-09 14:48:36,286 - INFO - ✅ PASS GET /api/outclients/stats - 200 (0.012s)
2025-06-09 14:48:36,294 - INFO - ✅ PASS GET /api/outclients/dropdown - 200 (0.007s)
2025-06-09 14:48:36,309 - INFO - ✅ PASS POST /api/outclients - 201 (0.013s)
2025-06-09 14:48:36,322 - INFO - ✅ PASS GET /api/outclients/8 - 200 (0.013s)
2025-06-09 14:48:36,357 - INFO - ✅ PASS PUT /api/outclients/8 - 200 (0.034s)
2025-06-09 14:48:36,357 - INFO - 📋 Testing Project Management...
2025-06-09 14:48:36,393 - INFO - ✅ PASS GET /api/projects - 200 (0.035s)
2025-06-09 14:48:36,460 - INFO - ✅ PASS GET /api/projects/stats - 200 (0.067s)
2025-06-09 14:48:36,471 - INFO - ✅ PASS GET /api/projects/codes - 200 (0.010s)
2025-06-09 14:48:36,490 - INFO - ✅ PASS GET /api/clients?limit=1 - 200 (0.019s)
2025-06-09 14:48:36,928 - INFO - ✅ PASS POST /api/projects - 201 (0.438s)
2025-06-09 14:48:36,940 - INFO - ✅ PASS GET /api/projects/24 - 200 (0.011s)
2025-06-09 14:48:36,953 - INFO - ✅ PASS GET /api/projects/code/CYM-24 - 200 (0.012s)
2025-06-09 14:48:36,962 - INFO - ✅ PASS GET /api/projects/CYM-24/data - 200 (0.009s)
2025-06-09 14:48:37,028 - INFO - ✅ PASS PUT /api/projects/24 - 200 (0.066s)
2025-06-09 14:48:37,067 - INFO - ✅ PASS PUT /api/projects/24/status - 200 (0.038s)
2025-06-09 14:48:37,067 - INFO - 📋 Testing Enhanced Project Management...
2025-06-09 14:48:37,086 - INFO - ✅ PASS GET /api/projects/status/pending - 200 (0.018s)
2025-06-09 14:48:37,099 - INFO - ✅ PASS GET /api/projects/status/ongoing - 200 (0.011s)
2025-06-09 14:48:37,114 - INFO - ✅ PASS GET /api/projects/status/completed - 200 (0.015s)
2025-06-09 14:48:37,115 - INFO - 💰 Testing Financial Management...
2025-06-09 14:48:37,138 - INFO - ✅ PASS GET /api/financial/income - 200 (0.023s)
2025-06-09 14:48:37,152 - INFO - ✅ PASS POST /api/financial/income - 201 (0.013s)
2025-06-09 14:48:37,162 - INFO - ✅ PASS GET /api/financial/income/16 - 200 (0.009s)
2025-06-09 14:48:37,178 - INFO - ✅ PASS PUT /api/financial/income/16 - 200 (0.016s)
2025-06-09 14:48:37,192 - INFO - ✅ PASS GET /api/financial/expenses - 200 (0.014s)
2025-06-09 14:48:37,206 - INFO - ✅ PASS GET /api/financial/expenses/categories - 200 (0.014s)
2025-06-09 14:48:37,224 - INFO - ✅ PASS GET /api/financial/expenses/totals - 200 (0.018s)
2025-06-09 14:48:37,240 - INFO - ✅ PASS POST /api/financial/expenses - 201 (0.015s)
2025-06-09 14:48:37,249 - INFO - ✅ PASS GET /api/financial/expenses/13 - 200 (0.007s)
2025-06-09 14:48:37,273 - INFO - ✅ PASS PUT /api/financial/expenses/13 - 200 (0.023s)
2025-06-09 14:48:37,354 - INFO - ✅ PASS GET /api/financial/summary - 200 (0.081s)
2025-06-09 14:48:37,425 - INFO - ✅ PASS GET /api/financial/budget - 200 (0.072s)
2025-06-09 14:48:37,507 - INFO - ✅ PASS GET /api/financial/income/chart-data - 200 (0.081s)
2025-06-09 14:48:37,612 - INFO - ✅ PASS GET /api/financial/income/chart-data?period=12months - 200 (0.105s)
2025-06-09 14:48:37,630 - INFO - ✅ PASS GET /api/financial/expenses/categorized - 200 (0.018s)
2025-06-09 14:48:37,642 - INFO - ✅ PASS GET /api/financial/expenses/categorized?period=12months - 200 (0.012s)
2025-06-09 14:48:37,642 - INFO - 💰 Testing Budget Management...
2025-06-09 14:48:37,712 - INFO - ✅ PASS GET /api/budget/overview - 200 (0.069s)
2025-06-09 14:48:37,718 - INFO - ✅ PASS GET /api/budget/categories - 200 (0.006s)
2025-06-09 14:48:37,724 - INFO - ✅ PASS GET /api/budget/investment-details - 200 (0.006s)
2025-06-09 14:48:37,730 - INFO - ✅ PASS POST /api/budget/categories - 201 (0.006s)
2025-06-09 14:48:37,736 - INFO - ✅ PASS PUT /api/budget/categories/1749460717728 - 200 (0.006s)
2025-06-09 14:48:37,741 - INFO - ✅ PASS DELETE /api/budget/categories/1749460717728 - 200 (0.004s)
2025-06-09 14:48:37,742 - INFO - 💳 Testing Payment Management...
2025-06-09 14:48:37,818 - INFO - ✅ PASS GET /api/payments/stats - 200 (0.076s)
2025-06-09 14:48:37,841 - INFO - ✅ PASS GET /api/payments - 200 (0.023s)
2025-06-09 14:48:37,860 - INFO - ✅ PASS GET /api/payments?page=1&limit=5 - 200 (0.018s)
2025-06-09 14:48:37,871 - INFO - ✅ PASS GET /api/payments/status/pending - 200 (0.010s)
2025-06-09 14:48:37,880 - INFO - ✅ PASS GET /api/payments/status/ongoing - 200 (0.008s)
2025-06-09 14:48:37,890 - INFO - ✅ PASS GET /api/payments/status/completed - 200 (0.010s)
2025-06-09 14:48:37,904 - INFO - ✅ PASS GET /api/clients?limit=1 - 200 (0.014s)
2025-06-09 14:48:37,932 - INFO - ✅ PASS POST /api/payments - 201 (0.028s)
2025-06-09 14:48:37,941 - INFO - ✅ PASS GET /api/payments/25 - 200 (0.008s)
2025-06-09 14:48:37,953 - INFO - ✅ PASS PUT /api/payments/25 - 200 (0.011s)
2025-06-09 14:48:37,966 - INFO - ✅ PASS PUT /api/payments/25/status - 200 (0.013s)
2025-06-09 14:48:37,979 - INFO - ✅ PASS DELETE /api/payments/25 - 200 (0.013s)
2025-06-09 14:48:37,980 - INFO - 🏭 Testing Asset Management...
2025-06-09 14:48:37,995 - INFO - ✅ PASS GET /api/assets - 200 (0.015s)
2025-06-09 14:48:38,008 - INFO - ✅ PASS GET /api/assets/stats - 200 (0.013s)
2025-06-09 14:48:38,024 - INFO - ✅ PASS GET /api/assets/types - 200 (0.016s)
2025-06-09 14:48:38,053 - INFO - ✅ PASS POST /api/assets - 201 (0.028s)
2025-06-09 14:48:38,061 - INFO - ✅ PASS GET /api/assets/8 - 200 (0.008s)
2025-06-09 14:48:38,077 - INFO - ✅ PASS PUT /api/assets/8 - 200 (0.015s)
2025-06-09 14:48:38,077 - INFO - 🎬 Testing Entertainment Management...
2025-06-09 14:48:38,089 - INFO - ✅ PASS GET /api/entertainment - 200 (0.011s)
2025-06-09 14:48:38,104 - INFO - ✅ PASS GET /api/entertainment/stats - 200 (0.013s)
2025-06-09 14:48:38,109 - INFO - ✅ PASS GET /api/entertainment/types - 200 (0.005s)
2025-06-09 14:48:38,116 - INFO - ✅ PASS GET /api/entertainment/languages - 200 (0.005s)
2025-06-09 14:48:38,129 - INFO - ✅ PASS POST /api/entertainment - 201 (0.012s)
2025-06-09 14:48:38,139 - INFO - ✅ PASS GET /api/entertainment/8 - 200 (0.010s)
2025-06-09 14:48:38,153 - INFO - ✅ PASS PUT /api/entertainment/8 - 200 (0.013s)
2025-06-09 14:48:38,153 - INFO - 📅 Testing Calendar Management...
2025-06-09 14:48:38,165 - INFO - ✅ PASS GET /api/calendar/events - 200 (0.011s)
2025-06-09 14:48:38,173 - INFO - ✅ PASS GET /api/calendar/events/upcoming - 200 (0.007s)
2025-06-09 14:48:38,180 - INFO - ✅ PASS GET /api/calendar/events/today - 200 (0.007s)
2025-06-09 14:48:38,189 - INFO - ✅ PASS GET /api/calendar/events/week - 200 (0.008s)
2025-06-09 14:48:38,196 - INFO - ✅ PASS GET /api/calendar/events/month - 200 (0.007s)
2025-06-09 14:48:38,205 - INFO - ✅ PASS GET /api/calendar/events/stats - 200 (0.010s)
2025-06-09 14:48:38,214 - INFO - ✅ PASS POST /api/calendar/events - 201 (0.008s)
2025-06-09 14:48:38,225 - INFO - ✅ PASS GET /api/calendar/events/19 - 200 (0.011s)
2025-06-09 14:48:38,238 - INFO - ✅ PASS PUT /api/calendar/events/19 - 200 (0.011s)
2025-06-09 14:48:38,245 - INFO - ✅ PASS GET /api/calendar/events/range?startDate=2025-06-09&endDate=2025-06-16 - 200 (0.007s)
2025-06-09 14:48:38,245 - INFO - 🗺️ Testing Maps Integration...
2025-06-09 14:48:38,404 - INFO - ✅ PASS POST /api/maps/geocode - 200 (0.158s)
2025-06-09 14:48:38,539 - INFO - ✅ PASS POST /api/maps/reverse-geocode - 200 (0.135s)
2025-06-09 14:48:38,693 - INFO - ✅ PASS POST /api/maps/detailed-geocode - 200 (0.153s)
2025-06-09 14:48:39,304 - INFO - ✅ PASS POST /api/maps/nearby-places - 200 (0.609s)
2025-06-09 14:48:39,493 - INFO - ✅ PASS POST /api/maps/distance - 200 (0.188s)
2025-06-09 14:48:39,498 - INFO - ✅ PASS GET /api/maps/static-map?latitude=40.7128&longitude=-74.0060&zoom=12 - 200 (0.005s)
2025-06-09 14:48:39,504 - INFO - ✅ PASS POST /api/maps/directions - 200 (0.005s)
2025-06-09 14:48:39,510 - INFO - ✅ PASS POST /api/maps/validate-coordinates - 200 (0.007s)
2025-06-09 14:48:39,511 - INFO - 📊 Testing Dashboard...
2025-06-09 14:48:39,525 - INFO - ✅ PASS GET /api/dashboard/stats - 200 (0.014s)
2025-06-09 14:48:39,533 - INFO - ✅ PASS GET /api/dashboard/financial-summary?startDate=2025-05-10&endDate=2025-06-09 - 200 (0.006s)
2025-06-09 14:48:39,541 - INFO - ✅ PASS GET /api/dashboard/today-schedule - 200 (0.007s)
2025-06-09 14:48:39,574 - INFO - ✅ PASS GET /api/dashboard/charts/income-expense - 200 (0.031s)
2025-06-09 14:48:39,621 - INFO - ✅ PASS GET /api/dashboard/charts/income-expense?period=12months - 200 (0.047s)
2025-06-09 14:48:39,648 - INFO - ✅ PASS GET /api/dashboard/charts/project-details - 200 (0.026s)
2025-06-09 14:48:39,674 - INFO - ✅ PASS GET /api/dashboard/charts/expense-breakdown - 200 (0.025s)
2025-06-09 14:48:39,713 - INFO - ✅ PASS GET /api/dashboard/charts/expense-breakdown?period=12months - 200 (0.040s)
2025-06-09 14:48:39,714 - INFO - 🧹 Cleaning up test data...
2025-06-09 14:48:39,726 - INFO - ✅ PASS DELETE /api/calendar/events/19 - 200 (0.012s)
2025-06-09 14:48:39,740 - INFO - ✅ PASS DELETE /api/entertainment/8 - 200 (0.013s)
2025-06-09 14:48:39,766 - INFO - ✅ PASS DELETE /api/assets/8 - 200 (0.026s)
2025-06-09 14:48:39,791 - INFO - ✅ PASS DELETE /api/financial/expenses/13 - 200 (0.024s)
2025-06-09 14:48:39,807 - INFO - ✅ PASS DELETE /api/financial/income/16 - 200 (0.016s)
2025-06-09 14:48:39,876 - INFO - ❌ FAIL DELETE /api/projects/24 - 409 (0.068s)
2025-06-09 14:48:39,877 - ERROR - Error: {"success":false,"error":{"code":"CONFLICT_ERROR","message":"Cannot delete project with existing financial records"},"timestamp":"2025-06-09T09:18:39.874Z"}
2025-06-09 14:48:39,909 - INFO - ✅ PASS DELETE /api/outclients/8 - 200 (0.032s)
2025-06-09 14:48:39,909 - INFO - 🏁 Test suite completed in 6.05 seconds
2025-06-09 14:48:39,912 - INFO - Summary report saved to: logs/test_summary_20250609_144839.txt
