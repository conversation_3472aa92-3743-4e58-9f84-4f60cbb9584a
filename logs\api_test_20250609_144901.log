2025-06-09 14:49:01,779 - INFO - Loaded existing auth token from file
2025-06-09 14:49:01,780 - INFO - 🚀 Starting Cymatics API Test Suite...
2025-06-09 14:49:01,781 - INFO - 🏥 Testing Health Check...
2025-06-09 14:49:03,849 - INFO - ✅ PASS GET /health - 200 (2.068s)
2025-06-09 14:49:03,850 - INFO - ℹ️ Testing API Info...
2025-06-09 14:49:03,858 - INFO - ✅ PASS GET /api - 200 (0.006s)
2025-06-09 14:49:03,860 - INFO - 🔐 Testing Authentication...
2025-06-09 14:49:03,871 - INFO - ✅ PASS GET /api/auth/profile - 200 (0.009s)
2025-06-09 14:49:03,871 - INFO - ✅ Using existing valid authentication token
2025-06-09 14:49:03,872 - INFO - 🎉 Authentication successful! All endpoints will be tested with proper authorization.
2025-06-09 14:49:03,874 - INFO - 👥 Testing Client Management...
2025-06-09 14:49:03,890 - INFO - ✅ PASS GET /api/clients - 200 (0.016s)
2025-06-09 14:49:03,903 - INFO - ✅ PASS GET /api/clients/stats - 200 (0.012s)
2025-06-09 14:49:03,915 - INFO - ✅ PASS GET /api/clients/dropdown - 200 (0.011s)
2025-06-09 14:49:03,941 - INFO - ❌ FAIL POST /api/clients - 409 (0.025s)
2025-06-09 14:49:03,942 - ERROR - Error: {"success":false,"error":{"code":"CONFLICT_ERROR","message":"Client with this email already exists"},"timestamp":"2025-06-09T09:19:03.936Z"}
2025-06-09 14:49:03,973 - INFO - ✅ PASS GET /api/clients/99999 - 404 (0.030s)
2025-06-09 14:49:03,973 - INFO - ✅ Invalid client ID test passed - correctly returned 404
2025-06-09 14:49:03,973 - INFO - 🏢 Testing Outclient Management...
2025-06-09 14:49:03,991 - INFO - ✅ PASS GET /api/outclients - 200 (0.018s)
2025-06-09 14:49:04,004 - INFO - ✅ PASS GET /api/outclients/stats - 200 (0.012s)
2025-06-09 14:49:04,021 - INFO - ✅ PASS GET /api/outclients/dropdown - 200 (0.016s)
2025-06-09 14:49:04,037 - INFO - ✅ PASS POST /api/outclients - 201 (0.016s)
2025-06-09 14:49:04,052 - INFO - ✅ PASS GET /api/outclients/9 - 200 (0.014s)
2025-06-09 14:49:04,089 - INFO - ✅ PASS PUT /api/outclients/9 - 200 (0.036s)
2025-06-09 14:49:04,090 - INFO - 📋 Testing Project Management...
2025-06-09 14:49:04,112 - INFO - ✅ PASS GET /api/projects - 200 (0.021s)
2025-06-09 14:49:04,159 - INFO - ✅ PASS GET /api/projects/stats - 200 (0.046s)
2025-06-09 14:49:04,169 - INFO - ✅ PASS GET /api/projects/codes - 200 (0.010s)
2025-06-09 14:49:04,185 - INFO - ✅ PASS GET /api/clients?limit=1 - 200 (0.016s)
2025-06-09 14:49:04,508 - INFO - ✅ PASS POST /api/projects - 201 (0.322s)
2025-06-09 14:49:04,516 - INFO - ✅ PASS GET /api/projects/26 - 200 (0.008s)
2025-06-09 14:49:04,528 - INFO - ✅ PASS GET /api/projects/code/CYM-26 - 200 (0.011s)
2025-06-09 14:49:04,536 - INFO - ✅ PASS GET /api/projects/CYM-26/data - 200 (0.008s)
2025-06-09 14:49:04,575 - INFO - ✅ PASS PUT /api/projects/26 - 200 (0.039s)
2025-06-09 14:49:04,598 - INFO - ✅ PASS PUT /api/projects/26/status - 200 (0.022s)
2025-06-09 14:49:04,599 - INFO - 📋 Testing Enhanced Project Management...
2025-06-09 14:49:04,609 - INFO - ✅ PASS GET /api/projects/status/pending - 200 (0.010s)
2025-06-09 14:49:04,616 - INFO - ✅ PASS GET /api/projects/status/ongoing - 200 (0.007s)
2025-06-09 14:49:04,625 - INFO - ✅ PASS GET /api/projects/status/completed - 200 (0.008s)
2025-06-09 14:49:04,625 - INFO - 💰 Testing Financial Management...
2025-06-09 14:49:04,634 - INFO - ✅ PASS GET /api/financial/income - 200 (0.008s)
2025-06-09 14:49:04,645 - INFO - ✅ PASS POST /api/financial/income - 201 (0.011s)
2025-06-09 14:49:04,652 - INFO - ✅ PASS GET /api/financial/income/18 - 200 (0.007s)
2025-06-09 14:49:04,668 - INFO - ✅ PASS PUT /api/financial/income/18 - 200 (0.014s)
2025-06-09 14:49:04,677 - INFO - ✅ PASS GET /api/financial/expenses - 200 (0.008s)
2025-06-09 14:49:04,683 - INFO - ✅ PASS GET /api/financial/expenses/categories - 200 (0.005s)
2025-06-09 14:49:04,690 - INFO - ✅ PASS GET /api/financial/expenses/totals - 200 (0.007s)
2025-06-09 14:49:04,699 - INFO - ✅ PASS POST /api/financial/expenses - 201 (0.009s)
2025-06-09 14:49:04,705 - INFO - ✅ PASS GET /api/financial/expenses/14 - 200 (0.006s)
2025-06-09 14:49:04,717 - INFO - ✅ PASS PUT /api/financial/expenses/14 - 200 (0.012s)
2025-06-09 14:49:04,724 - INFO - ✅ PASS GET /api/financial/summary - 200 (0.007s)
2025-06-09 14:49:04,733 - INFO - ✅ PASS GET /api/financial/budget - 200 (0.008s)
2025-06-09 14:49:04,772 - INFO - ✅ PASS GET /api/financial/income/chart-data - 200 (0.037s)
2025-06-09 14:49:04,867 - INFO - ✅ PASS GET /api/financial/income/chart-data?period=12months - 200 (0.095s)
2025-06-09 14:49:04,886 - INFO - ✅ PASS GET /api/financial/expenses/categorized - 200 (0.019s)
2025-06-09 14:49:04,927 - INFO - ✅ PASS GET /api/financial/expenses/categorized?period=12months - 200 (0.040s)
2025-06-09 14:49:04,928 - INFO - 💰 Testing Budget Management...
2025-06-09 14:49:05,075 - INFO - ✅ PASS GET /api/budget/overview - 200 (0.147s)
2025-06-09 14:49:05,086 - INFO - ✅ PASS GET /api/budget/categories - 200 (0.009s)
2025-06-09 14:49:05,094 - INFO - ✅ PASS GET /api/budget/investment-details - 200 (0.008s)
2025-06-09 14:49:05,102 - INFO - ✅ PASS POST /api/budget/categories - 201 (0.007s)
2025-06-09 14:49:05,110 - INFO - ✅ PASS PUT /api/budget/categories/1749460745099 - 200 (0.008s)
2025-06-09 14:49:05,116 - INFO - ✅ PASS DELETE /api/budget/categories/1749460745099 - 200 (0.006s)
2025-06-09 14:49:05,116 - INFO - 💳 Testing Payment Management...
2025-06-09 14:49:05,243 - INFO - ✅ PASS GET /api/payments/stats - 200 (0.126s)
2025-06-09 14:49:05,259 - INFO - ✅ PASS GET /api/payments - 200 (0.016s)
2025-06-09 14:49:05,270 - INFO - ✅ PASS GET /api/payments?page=1&limit=5 - 200 (0.011s)
2025-06-09 14:49:05,281 - INFO - ✅ PASS GET /api/payments/status/pending - 200 (0.011s)
2025-06-09 14:49:05,300 - INFO - ✅ PASS GET /api/payments/status/ongoing - 200 (0.017s)
2025-06-09 14:49:05,348 - INFO - ✅ PASS GET /api/payments/status/completed - 200 (0.047s)
2025-06-09 14:49:05,380 - INFO - ✅ PASS GET /api/clients?limit=1 - 200 (0.031s)
2025-06-09 14:49:05,396 - INFO - ✅ PASS POST /api/payments - 201 (0.016s)
2025-06-09 14:49:05,407 - INFO - ✅ PASS GET /api/payments/27 - 200 (0.010s)
2025-06-09 14:49:05,424 - INFO - ✅ PASS PUT /api/payments/27 - 200 (0.017s)
2025-06-09 14:49:05,440 - INFO - ✅ PASS PUT /api/payments/27/status - 200 (0.015s)
2025-06-09 14:49:05,458 - INFO - ✅ PASS DELETE /api/payments/27 - 200 (0.017s)
2025-06-09 14:49:05,458 - INFO - 🏭 Testing Asset Management...
2025-06-09 14:49:05,478 - INFO - ✅ PASS GET /api/assets - 200 (0.019s)
2025-06-09 14:49:05,496 - INFO - ✅ PASS GET /api/assets/stats - 200 (0.017s)
2025-06-09 14:49:05,507 - INFO - ✅ PASS GET /api/assets/types - 200 (0.012s)
2025-06-09 14:49:05,526 - INFO - ✅ PASS POST /api/assets - 201 (0.018s)
2025-06-09 14:49:05,539 - INFO - ✅ PASS GET /api/assets/9 - 200 (0.013s)
2025-06-09 14:49:05,564 - INFO - ✅ PASS PUT /api/assets/9 - 200 (0.023s)
2025-06-09 14:49:05,564 - INFO - 🎬 Testing Entertainment Management...
2025-06-09 14:49:05,580 - INFO - ✅ PASS GET /api/entertainment - 200 (0.016s)
2025-06-09 14:49:05,598 - INFO - ✅ PASS GET /api/entertainment/stats - 200 (0.018s)
2025-06-09 14:49:05,612 - INFO - ✅ PASS GET /api/entertainment/types - 200 (0.013s)
2025-06-09 14:49:05,627 - INFO - ✅ PASS GET /api/entertainment/languages - 200 (0.015s)
2025-06-09 14:49:05,641 - INFO - ✅ PASS POST /api/entertainment - 201 (0.013s)
2025-06-09 14:49:05,651 - INFO - ✅ PASS GET /api/entertainment/9 - 200 (0.009s)
2025-06-09 14:49:05,673 - INFO - ✅ PASS PUT /api/entertainment/9 - 200 (0.022s)
2025-06-09 14:49:05,674 - INFO - 📅 Testing Calendar Management...
2025-06-09 14:49:05,692 - INFO - ✅ PASS GET /api/calendar/events - 200 (0.018s)
2025-06-09 14:49:05,705 - INFO - ✅ PASS GET /api/calendar/events/upcoming - 200 (0.012s)
2025-06-09 14:49:05,718 - INFO - ✅ PASS GET /api/calendar/events/today - 200 (0.011s)
2025-06-09 14:49:05,731 - INFO - ✅ PASS GET /api/calendar/events/week - 200 (0.012s)
2025-06-09 14:49:05,744 - INFO - ✅ PASS GET /api/calendar/events/month - 200 (0.013s)
2025-06-09 14:49:05,765 - INFO - ✅ PASS GET /api/calendar/events/stats - 200 (0.020s)
2025-06-09 14:49:05,782 - INFO - ✅ PASS POST /api/calendar/events - 201 (0.016s)
2025-06-09 14:49:05,795 - INFO - ✅ PASS GET /api/calendar/events/20 - 200 (0.013s)
2025-06-09 14:49:05,810 - INFO - ✅ PASS PUT /api/calendar/events/20 - 200 (0.015s)
2025-06-09 14:49:05,824 - INFO - ✅ PASS GET /api/calendar/events/range?startDate=2025-06-09&endDate=2025-06-16 - 200 (0.013s)
2025-06-09 14:49:05,824 - INFO - 🗺️ Testing Maps Integration...
2025-06-09 14:49:05,931 - INFO - ✅ PASS POST /api/maps/geocode - 200 (0.106s)
2025-06-09 14:49:06,088 - INFO - ✅ PASS POST /api/maps/reverse-geocode - 200 (0.156s)
2025-06-09 14:49:06,204 - INFO - ✅ PASS POST /api/maps/detailed-geocode - 200 (0.115s)
2025-06-09 14:49:06,423 - INFO - ✅ PASS POST /api/maps/nearby-places - 200 (0.217s)
2025-06-09 14:49:06,560 - INFO - ✅ PASS POST /api/maps/distance - 200 (0.137s)
2025-06-09 14:49:06,568 - INFO - ✅ PASS GET /api/maps/static-map?latitude=40.7128&longitude=-74.0060&zoom=12 - 200 (0.007s)
2025-06-09 14:49:06,584 - INFO - ✅ PASS POST /api/maps/directions - 200 (0.016s)
2025-06-09 14:49:06,592 - INFO - ✅ PASS POST /api/maps/validate-coordinates - 200 (0.008s)
2025-06-09 14:49:06,593 - INFO - 📊 Testing Dashboard...
2025-06-09 14:49:06,607 - INFO - ✅ PASS GET /api/dashboard/stats - 200 (0.014s)
2025-06-09 14:49:06,618 - INFO - ✅ PASS GET /api/dashboard/financial-summary?startDate=2025-05-10&endDate=2025-06-09 - 200 (0.011s)
2025-06-09 14:49:06,630 - INFO - ✅ PASS GET /api/dashboard/today-schedule - 200 (0.012s)
2025-06-09 14:49:06,669 - INFO - ✅ PASS GET /api/dashboard/charts/income-expense - 200 (0.039s)
2025-06-09 14:49:06,736 - INFO - ✅ PASS GET /api/dashboard/charts/income-expense?period=12months - 200 (0.066s)
2025-06-09 14:49:06,784 - INFO - ✅ PASS GET /api/dashboard/charts/project-details - 200 (0.048s)
2025-06-09 14:49:06,818 - INFO - ✅ PASS GET /api/dashboard/charts/expense-breakdown - 200 (0.033s)
