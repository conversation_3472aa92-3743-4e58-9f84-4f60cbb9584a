
================================================================================
🧪 CYMATICS API TEST SUITE SUMMARY
================================================================================

📊 OVERALL RESULTS:
   Total Tests: 51
   ✅ Passed: 2
   ❌ Failed: 49
   📈 Success Rate: 3.9%

📋 RESULTS BY CATEGORY:
   ROOT: 2/2 (100.0%)
   AUTH: 0/1 (0.0%)
   CLIENTS: 0/5 (0.0%)
   OUTCLIENTS: 0/4 (0.0%)
   PROJECTS: 0/4 (0.0%)
   FINANCIAL: 0/8 (0.0%)
   ASSETS: 0/4 (0.0%)
   ENTERTAINMENT: 0/5 (0.0%)
   CALENDAR: 0/8 (0.0%)
   MAPS: 0/8 (0.0%)
   DASHBOARD: 0/2 (0.0%)

❌ FAILED TESTS DETAILS:
   POST /api/auth/send-otp
      Status: 500
      Error: {"success":false,"error":{"code":"DATABASE_ERROR","message":"Database operation failed","details":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database."},"timestamp":"2025-06-05T12:26:22.913Z"}
      Response Time: 0.088s

   GET /api/clients
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:22.919Z"}
      Response Time: 0.004s

   GET /api/clients/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:22.924Z"}
      Response Time: 0.004s

   GET /api/clients/dropdown
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:22.929Z"}
      Response Time: 0.003s

   POST /api/clients
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:22.935Z"}
      Response Time: 0.006s

   GET /api/clients/99999
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:22.942Z"}
      Response Time: 0.005s

   GET /api/outclients
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:22.985Z"}
      Response Time: 0.042s

   GET /api/outclients/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:22.990Z"}
      Response Time: 0.003s

   GET /api/outclients/dropdown
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:22.995Z"}
      Response Time: 0.004s

   POST /api/outclients
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:22.998Z"}
      Response Time: 0.002s

   GET /api/projects
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.002Z"}
      Response Time: 0.003s

   GET /api/projects/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.006Z"}
      Response Time: 0.003s

   GET /api/projects/codes
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.013Z"}
      Response Time: 0.006s

   POST /api/projects
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.017Z"}
      Response Time: 0.003s

   GET /api/financial/income
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.021Z"}
      Response Time: 0.003s

   POST /api/financial/income
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.025Z"}
      Response Time: 0.003s

   GET /api/financial/expenses
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.029Z"}
      Response Time: 0.003s

   GET /api/financial/expenses/categories
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.033Z"}
      Response Time: 0.003s

   GET /api/financial/expenses/totals
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.036Z"}
      Response Time: 0.003s

   POST /api/financial/expenses
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.042Z"}
      Response Time: 0.006s

   GET /api/financial/summary
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.047Z"}
      Response Time: 0.003s

   GET /api/financial/budget
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.050Z"}
      Response Time: 0.002s

   GET /api/assets
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.054Z"}
      Response Time: 0.003s

   GET /api/assets/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.058Z"}
      Response Time: 0.004s

   GET /api/assets/types
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.063Z"}
      Response Time: 0.004s

   POST /api/assets
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.068Z"}
      Response Time: 0.003s

   GET /api/entertainment
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.072Z"}
      Response Time: 0.003s

   GET /api/entertainment/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.076Z"}
      Response Time: 0.003s

   GET /api/entertainment/types
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.080Z"}
      Response Time: 0.003s

   GET /api/entertainment/languages
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.084Z"}
      Response Time: 0.003s

   POST /api/entertainment
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.088Z"}
      Response Time: 0.003s

   GET /api/calendar/events
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.092Z"}
      Response Time: 0.003s

   GET /api/calendar/events/upcoming
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.097Z"}
      Response Time: 0.005s

   GET /api/calendar/events/today
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.102Z"}
      Response Time: 0.002s

   GET /api/calendar/events/week
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.105Z"}
      Response Time: 0.003s

   GET /api/calendar/events/month
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.109Z"}
      Response Time: 0.003s

   GET /api/calendar/events/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.113Z"}
      Response Time: 0.003s

   POST /api/calendar/events
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.117Z"}
      Response Time: 0.003s

   GET /api/calendar/events/range?startDate=2025-06-05&endDate=2025-06-12
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.120Z"}
      Response Time: 0.002s

   POST /api/maps/geocode
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.125Z"}
      Response Time: 0.004s

   POST /api/maps/reverse-geocode
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.130Z"}
      Response Time: 0.003s

   POST /api/maps/detailed-geocode
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.133Z"}
      Response Time: 0.003s

   POST /api/maps/nearby-places
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.137Z"}
      Response Time: 0.003s

   POST /api/maps/distance
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.140Z"}
      Response Time: 0.002s

   GET /api/maps/static-map?lat=40.7128&lng=-74.0060&zoom=12
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.144Z"}
      Response Time: 0.004s

   POST /api/maps/directions
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.148Z"}
      Response Time: 0.003s

   POST /api/maps/validate-coordinates
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.151Z"}
      Response Time: 0.002s

   GET /api/dashboard/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.155Z"}
      Response Time: 0.003s

   GET /api/dashboard/financial-summary?startDate=2025-05-06&endDate=2025-06-05
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:26:23.159Z"}
      Response Time: 0.004s


⚡ PERFORMANCE STATS:
   Average Response Time: 0.006s
   Fastest Response: 0.002s
   Slowest Response: 0.088s

📝 Log File: logs/api_test_20250605_175622.log
================================================================================
