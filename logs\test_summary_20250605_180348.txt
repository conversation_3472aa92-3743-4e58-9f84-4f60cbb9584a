
================================================================================
🧪 CYMATICS API TEST SUITE SUMMARY
================================================================================

📊 OVERALL RESULTS:
   Total Tests: 51
   ✅ Passed: 2
   ❌ Failed: 49
   📈 Success Rate: 3.9%

📋 RESULTS BY CATEGORY:
   ROOT: 2/2 (100.0%)
   AUTH: 0/1 (0.0%)
   CLIENTS: 0/5 (0.0%)
   OUTCLIENTS: 0/4 (0.0%)
   PROJECTS: 0/4 (0.0%)
   FINANCIAL: 0/8 (0.0%)
   ASSETS: 0/4 (0.0%)
   ENTERTAINMENT: 0/5 (0.0%)
   CALENDAR: 0/8 (0.0%)
   MAPS: 0/8 (0.0%)
   DASHBOARD: 0/2 (0.0%)

❌ FAILED TESTS DETAILS:
   POST /api/auth/send-otp
      Status: 500
      Error: {"success":false,"error":{"code":"DATABASE_ERROR","message":"Database operation failed","details":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database."},"timestamp":"2025-06-05T12:33:43.358Z"}
      Response Time: 0.362s

   GET /api/clients
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:43.466Z"}
      Response Time: 0.006s

   GET /api/clients/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:43.570Z"}
      Response Time: 0.002s

   GET /api/clients/dropdown
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:43.674Z"}
      Response Time: 0.002s

   POST /api/clients
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:43.780Z"}
      Response Time: 0.006s

   GET /api/clients/99999
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:43.887Z"}
      Response Time: 0.004s

   GET /api/outclients
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:43.993Z"}
      Response Time: 0.005s

   GET /api/outclients/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:44.100Z"}
      Response Time: 0.004s

   GET /api/outclients/dropdown
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:44.205Z"}
      Response Time: 0.004s

   POST /api/outclients
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:44.310Z"}
      Response Time: 0.004s

   GET /api/projects
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:44.418Z"}
      Response Time: 0.006s

   GET /api/projects/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:44.522Z"}
      Response Time: 0.003s

   GET /api/projects/codes
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:44.627Z"}
      Response Time: 0.003s

   POST /api/projects
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:44.731Z"}
      Response Time: 0.003s

   GET /api/financial/income
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:44.838Z"}
      Response Time: 0.006s

   POST /api/financial/income
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:44.947Z"}
      Response Time: 0.008s

   GET /api/financial/expenses
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:45.052Z"}
      Response Time: 0.003s

   GET /api/financial/expenses/categories
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:45.156Z"}
      Response Time: 0.003s

   GET /api/financial/expenses/totals
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:45.260Z"}
      Response Time: 0.003s

   POST /api/financial/expenses
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:45.364Z"}
      Response Time: 0.002s

   GET /api/financial/summary
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:45.468Z"}
      Response Time: 0.002s

   GET /api/financial/budget
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:45.572Z"}
      Response Time: 0.003s

   GET /api/assets
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:45.676Z"}
      Response Time: 0.003s

   GET /api/assets/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:45.780Z"}
      Response Time: 0.003s

   GET /api/assets/types
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:45.885Z"}
      Response Time: 0.003s

   POST /api/assets
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:45.991Z"}
      Response Time: 0.004s

   GET /api/entertainment
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:46.098Z"}
      Response Time: 0.004s

   GET /api/entertainment/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:46.202Z"}
      Response Time: 0.002s

   GET /api/entertainment/types
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:46.307Z"}
      Response Time: 0.003s

   GET /api/entertainment/languages
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:46.412Z"}
      Response Time: 0.002s

   POST /api/entertainment
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:46.519Z"}
      Response Time: 0.006s

   GET /api/calendar/events
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:46.625Z"}
      Response Time: 0.004s

   GET /api/calendar/events/upcoming
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:46.729Z"}
      Response Time: 0.002s

   GET /api/calendar/events/today
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:46.834Z"}
      Response Time: 0.004s

   GET /api/calendar/events/week
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:46.939Z"}
      Response Time: 0.003s

   GET /api/calendar/events/month
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:47.043Z"}
      Response Time: 0.003s

   GET /api/calendar/events/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:47.149Z"}
      Response Time: 0.005s

   POST /api/calendar/events
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:47.256Z"}
      Response Time: 0.006s

   GET /api/calendar/events/range?startDate=2025-06-05&endDate=2025-06-12
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:47.363Z"}
      Response Time: 0.004s

   POST /api/maps/geocode
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:47.470Z"}
      Response Time: 0.006s

   POST /api/maps/reverse-geocode
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:47.575Z"}
      Response Time: 0.002s

   POST /api/maps/detailed-geocode
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:47.679Z"}
      Response Time: 0.003s

   POST /api/maps/nearby-places
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:47.785Z"}
      Response Time: 0.004s

   POST /api/maps/distance
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:47.889Z"}
      Response Time: 0.002s

   GET /api/maps/static-map?lat=40.7128&lng=-74.0060&zoom=12
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:47.993Z"}
      Response Time: 0.002s

   POST /api/maps/directions
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:48.098Z"}
      Response Time: 0.005s

   POST /api/maps/validate-coordinates
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:48.204Z"}
      Response Time: 0.002s

   GET /api/dashboard/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:48.309Z"}
      Response Time: 0.002s

   GET /api/dashboard/financial-summary?startDate=2025-05-06&endDate=2025-06-05
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:33:48.415Z"}
      Response Time: 0.005s


⚡ PERFORMANCE STATS:
   Average Response Time: 0.011s
   Fastest Response: 0.002s
   Slowest Response: 0.362s

📝 Log File: logs/api_test_20250605_180342.log
================================================================================
