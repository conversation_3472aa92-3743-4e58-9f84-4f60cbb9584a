
================================================================================
🧪 CYMATICS API TEST SUITE SUMMARY
================================================================================

📊 OVERALL RESULTS:
   Total Tests: 51
   ✅ Passed: 2
   ❌ Failed: 49
   📈 Success Rate: 3.9%

📋 RESULTS BY CATEGORY:
   ROOT: 2/2 (100.0%)
   AUTH: 0/1 (0.0%)
   CLIENTS: 0/5 (0.0%)
   OUTCLIENTS: 0/4 (0.0%)
   PROJECTS: 0/4 (0.0%)
   FINANCIAL: 0/8 (0.0%)
   ASSETS: 0/4 (0.0%)
   ENTERTAINMENT: 0/5 (0.0%)
   CALENDAR: 0/8 (0.0%)
   MAPS: 0/8 (0.0%)
   DASHBOARD: 0/2 (0.0%)

❌ FAILED TESTS DETAILS:
   POST /api/auth/send-otp
      Status: 500
      Error: {"success":false,"error":{"code":"DATABASE_ERROR","message":"Database operation failed","details":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database."},"timestamp":"2025-06-05T12:41:55.117Z"}
      Response Time: 0.081s

   GET /api/clients
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.127Z"}
      Response Time: 0.005s

   GET /api/clients/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.132Z"}
      Response Time: 0.004s

   GET /api/clients/dropdown
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.137Z"}
      Response Time: 0.003s

   POST /api/clients
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.141Z"}
      Response Time: 0.003s

   GET /api/clients/99999
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.146Z"}
      Response Time: 0.005s

   GET /api/outclients
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.152Z"}
      Response Time: 0.005s

   GET /api/outclients/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.158Z"}
      Response Time: 0.007s

   GET /api/outclients/dropdown
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.166Z"}
      Response Time: 0.006s

   POST /api/outclients
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.171Z"}
      Response Time: 0.004s

   GET /api/projects
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.177Z"}
      Response Time: 0.004s

   GET /api/projects/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.182Z"}
      Response Time: 0.004s

   GET /api/projects/codes
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.186Z"}
      Response Time: 0.003s

   POST /api/projects
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.191Z"}
      Response Time: 0.004s

   GET /api/financial/income
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.196Z"}
      Response Time: 0.004s

   POST /api/financial/income
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.201Z"}
      Response Time: 0.004s

   GET /api/financial/expenses
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.206Z"}
      Response Time: 0.004s

   GET /api/financial/expenses/categories
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.211Z"}
      Response Time: 0.007s

   GET /api/financial/expenses/totals
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.220Z"}
      Response Time: 0.006s

   POST /api/financial/expenses
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.227Z"}
      Response Time: 0.004s

   GET /api/financial/summary
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.232Z"}
      Response Time: 0.004s

   GET /api/financial/budget
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.236Z"}
      Response Time: 0.004s

   GET /api/assets
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.240Z"}
      Response Time: 0.001s

   GET /api/assets/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.244Z"}
      Response Time: 0.002s

   GET /api/assets/types
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.248Z"}
      Response Time: 0.004s

   POST /api/assets
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.251Z"}
      Response Time: 0.004s

   GET /api/entertainment
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.255Z"}
      Response Time: 0.003s

   GET /api/entertainment/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.259Z"}
      Response Time: 0.002s

   GET /api/entertainment/types
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.261Z"}
      Response Time: 0.001s

   GET /api/entertainment/languages
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.265Z"}
      Response Time: 0.004s

   POST /api/entertainment
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.269Z"}
      Response Time: 0.003s

   GET /api/calendar/events
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.273Z"}
      Response Time: 0.003s

   GET /api/calendar/events/upcoming
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.276Z"}
      Response Time: 0.002s

   GET /api/calendar/events/today
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.279Z"}
      Response Time: 0.002s

   GET /api/calendar/events/week
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.283Z"}
      Response Time: 0.003s

   GET /api/calendar/events/month
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.286Z"}
      Response Time: 0.002s

   GET /api/calendar/events/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.289Z"}
      Response Time: 0.003s

   POST /api/calendar/events
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.293Z"}
      Response Time: 0.003s

   GET /api/calendar/events/range?startDate=2025-06-05&endDate=2025-06-12
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.298Z"}
      Response Time: 0.004s

   POST /api/maps/geocode
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.303Z"}
      Response Time: 0.004s

   POST /api/maps/reverse-geocode
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.306Z"}
      Response Time: 0.003s

   POST /api/maps/detailed-geocode
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.309Z"}
      Response Time: 0.002s

   POST /api/maps/nearby-places
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.314Z"}
      Response Time: 0.004s

   POST /api/maps/distance
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.318Z"}
      Response Time: 0.003s

   GET /api/maps/static-map?lat=40.7128&lng=-74.0060&zoom=12
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.321Z"}
      Response Time: 0.003s

   POST /api/maps/directions
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.325Z"}
      Response Time: 0.003s

   POST /api/maps/validate-coordinates
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.329Z"}
      Response Time: 0.004s

   GET /api/dashboard/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.333Z"}
      Response Time: 0.003s

   GET /api/dashboard/financial-summary?startDate=2025-05-06&endDate=2025-06-05
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:41:55.337Z"}
      Response Time: 0.002s


⚡ PERFORMANCE STATS:
   Average Response Time: 0.005s
   Fastest Response: 0.001s
   Slowest Response: 0.081s

📝 Log File: logs/api_test_20250605_181155.log
================================================================================
