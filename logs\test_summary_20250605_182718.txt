
================================================================================
🧪 CYMATICS API TEST SUITE SUMMARY
================================================================================

📊 OVERALL RESULTS:
   Total Tests: 51
   ✅ Passed: 2
   ❌ Failed: 49
   📈 Success Rate: 3.9%

📋 RESULTS BY CATEGORY:
   ROOT: 2/2 (100.0%)
   AUTH: 0/1 (0.0%)
   CLIENTS: 0/5 (0.0%)
   OUTCLIENTS: 0/4 (0.0%)
   PROJECTS: 0/4 (0.0%)
   FINANCIAL: 0/8 (0.0%)
   ASSETS: 0/4 (0.0%)
   ENTERTAINMENT: 0/5 (0.0%)
   CALENDAR: 0/8 (0.0%)
   MAPS: 0/8 (0.0%)
   DASHBOARD: 0/2 (0.0%)

❌ FAILED TESTS DETAILS:
   POST /api/auth/send-otp
      Status: 500
      Error: {"success":false,"error":{"code":"DATABASE_ERROR","message":"Database operation failed","details":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database."},"timestamp":"2025-06-05T12:57:18.044Z"}
      Response Time: 0.134s

   GET /api/clients
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.053Z"}
      Response Time: 0.007s

   GET /api/clients/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.058Z"}
      Response Time: 0.003s

   GET /api/clients/dropdown
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.062Z"}
      Response Time: 0.003s

   POST /api/clients
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.066Z"}
      Response Time: 0.005s

   GET /api/clients/99999
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.073Z"}
      Response Time: 0.005s

   GET /api/outclients
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.078Z"}
      Response Time: 0.003s

   GET /api/outclients/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.084Z"}
      Response Time: 0.005s

   GET /api/outclients/dropdown
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.089Z"}
      Response Time: 0.004s

   POST /api/outclients
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.095Z"}
      Response Time: 0.005s

   GET /api/projects
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.101Z"}
      Response Time: 0.005s

   GET /api/projects/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.107Z"}
      Response Time: 0.005s

   GET /api/projects/codes
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.112Z"}
      Response Time: 0.003s

   POST /api/projects
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.116Z"}
      Response Time: 0.005s

   GET /api/financial/income
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.122Z"}
      Response Time: 0.003s

   POST /api/financial/income
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.127Z"}
      Response Time: 0.004s

   GET /api/financial/expenses
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.131Z"}
      Response Time: 0.004s

   GET /api/financial/expenses/categories
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.137Z"}
      Response Time: 0.006s

   GET /api/financial/expenses/totals
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.141Z"}
      Response Time: 0.004s

   POST /api/financial/expenses
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.147Z"}
      Response Time: 0.004s

   GET /api/financial/summary
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.153Z"}
      Response Time: 0.004s

   GET /api/financial/budget
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.157Z"}
      Response Time: 0.003s

   GET /api/assets
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.162Z"}
      Response Time: 0.004s

   GET /api/assets/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.168Z"}
      Response Time: 0.006s

   GET /api/assets/types
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.176Z"}
      Response Time: 0.006s

   POST /api/assets
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.189Z"}
      Response Time: 0.010s

   GET /api/entertainment
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.206Z"}
      Response Time: 0.016s

   GET /api/entertainment/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.212Z"}
      Response Time: 0.006s

   GET /api/entertainment/types
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.218Z"}
      Response Time: 0.004s

   GET /api/entertainment/languages
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.224Z"}
      Response Time: 0.006s

   POST /api/entertainment
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.230Z"}
      Response Time: 0.005s

   GET /api/calendar/events
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.236Z"}
      Response Time: 0.005s

   GET /api/calendar/events/upcoming
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.241Z"}
      Response Time: 0.004s

   GET /api/calendar/events/today
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.247Z"}
      Response Time: 0.004s

   GET /api/calendar/events/week
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.252Z"}
      Response Time: 0.005s

   GET /api/calendar/events/month
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.258Z"}
      Response Time: 0.004s

   GET /api/calendar/events/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.263Z"}
      Response Time: 0.004s

   POST /api/calendar/events
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.268Z"}
      Response Time: 0.005s

   GET /api/calendar/events/range?startDate=2025-06-05&endDate=2025-06-12
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.272Z"}
      Response Time: 0.004s

   POST /api/maps/geocode
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.278Z"}
      Response Time: 0.006s

   POST /api/maps/reverse-geocode
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.286Z"}
      Response Time: 0.007s

   POST /api/maps/detailed-geocode
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.294Z"}
      Response Time: 0.004s

   POST /api/maps/nearby-places
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.298Z"}
      Response Time: 0.003s

   POST /api/maps/distance
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.303Z"}
      Response Time: 0.004s

   GET /api/maps/static-map?lat=40.7128&lng=-74.0060&zoom=12
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.307Z"}
      Response Time: 0.003s

   POST /api/maps/directions
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.311Z"}
      Response Time: 0.004s

   POST /api/maps/validate-coordinates
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.317Z"}
      Response Time: 0.005s

   GET /api/dashboard/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.321Z"}
      Response Time: 0.003s

   GET /api/dashboard/financial-summary?startDate=2025-05-06&endDate=2025-06-05
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T12:57:18.325Z"}
      Response Time: 0.003s


⚡ PERFORMANCE STATS:
   Average Response Time: 0.007s
   Fastest Response: 0.003s
   Slowest Response: 0.134s

📝 Log File: logs/api_test_20250605_182717.log
================================================================================
