
================================================================================
🧪 CYMATICS API TEST SUITE SUMMARY
================================================================================

📊 OVERALL RESULTS:
   Total Tests: 51
   ✅ Passed: 2
   ❌ Failed: 49
   📈 Success Rate: 3.9%

📋 RESULTS BY CATEGORY:
   ROOT: 2/2 (100.0%)
   AUTH: 0/1 (0.0%)
   CLIENTS: 0/5 (0.0%)
   OUTCLIENTS: 0/4 (0.0%)
   PROJECTS: 0/4 (0.0%)
   FINANCIAL: 0/8 (0.0%)
   ASSETS: 0/4 (0.0%)
   ENTERTAINMENT: 0/5 (0.0%)
   CALENDAR: 0/8 (0.0%)
   MAPS: 0/8 (0.0%)
   DASHBOARD: 0/2 (0.0%)

❌ FAILED TESTS DETAILS:
   POST /api/auth/send-otp
      Status: 500
      Error: {"success":false,"error":{"code":"DATABASE_ERROR","message":"Database operation failed","details":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database."},"timestamp":"2025-06-05T13:04:23.135Z"}
      Response Time: 0.247s

   GET /api/clients
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.759Z"}
      Response Time: 0.008s

   GET /api/clients/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.765Z"}
      Response Time: 0.004s

   GET /api/clients/dropdown
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.769Z"}
      Response Time: 0.004s

   POST /api/clients
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.775Z"}
      Response Time: 0.009s

   GET /api/clients/99999
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.784Z"}
      Response Time: 0.005s

   GET /api/outclients
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.790Z"}
      Response Time: 0.004s

   GET /api/outclients/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.794Z"}
      Response Time: 0.011s

   GET /api/outclients/dropdown
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.806Z"}
      Response Time: 0.003s

   POST /api/outclients
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.813Z"}
      Response Time: 0.006s

   GET /api/projects
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.819Z"}
      Response Time: 0.003s

   GET /api/projects/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.822Z"}
      Response Time: 0.002s

   GET /api/projects/codes
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.826Z"}
      Response Time: 0.004s

   POST /api/projects
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.835Z"}
      Response Time: 0.007s

   GET /api/financial/income
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.840Z"}
      Response Time: 0.003s

   POST /api/financial/income
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.848Z"}
      Response Time: 0.007s

   GET /api/financial/expenses
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.853Z"}
      Response Time: 0.004s

   GET /api/financial/expenses/categories
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.857Z"}
      Response Time: 0.003s

   GET /api/financial/expenses/totals
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.864Z"}
      Response Time: 0.006s

   POST /api/financial/expenses
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.870Z"}
      Response Time: 0.004s

   GET /api/financial/summary
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.874Z"}
      Response Time: 0.004s

   GET /api/financial/budget
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.878Z"}
      Response Time: 0.004s

   GET /api/assets
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.886Z"}
      Response Time: 0.006s

   GET /api/assets/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.890Z"}
      Response Time: 0.003s

   GET /api/assets/types
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.894Z"}
      Response Time: 0.004s

   POST /api/assets
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.901Z"}
      Response Time: 0.004s

   GET /api/entertainment
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.907Z"}
      Response Time: 0.004s

   GET /api/entertainment/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.911Z"}
      Response Time: 0.005s

   GET /api/entertainment/types
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.917Z"}
      Response Time: 0.003s

   GET /api/entertainment/languages
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.920Z"}
      Response Time: 0.002s

   POST /api/entertainment
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.926Z"}
      Response Time: 0.006s

   GET /api/calendar/events
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.935Z"}
      Response Time: 0.008s

   GET /api/calendar/events/upcoming
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.941Z"}
      Response Time: 0.004s

   GET /api/calendar/events/today
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.945Z"}
      Response Time: 0.004s

   GET /api/calendar/events/week
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.951Z"}
      Response Time: 0.004s

   GET /api/calendar/events/month
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.955Z"}
      Response Time: 0.004s

   GET /api/calendar/events/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.959Z"}
      Response Time: 0.002s

   POST /api/calendar/events
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.966Z"}
      Response Time: 0.005s

   GET /api/calendar/events/range?startDate=2025-06-05&endDate=2025-06-12
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.980Z"}
      Response Time: 0.014s

   POST /api/maps/geocode
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.985Z"}
      Response Time: 0.003s

   POST /api/maps/reverse-geocode
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.990Z"}
      Response Time: 0.004s

   POST /api/maps/detailed-geocode
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:41.994Z"}
      Response Time: 0.004s

   POST /api/maps/nearby-places
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:42.000Z"}
      Response Time: 0.003s

   POST /api/maps/distance
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:42.004Z"}
      Response Time: 0.003s

   GET /api/maps/static-map?lat=40.7128&lng=-74.0060&zoom=12
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:42.008Z"}
      Response Time: 0.004s

   POST /api/maps/directions
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:42.014Z"}
      Response Time: 0.006s

   POST /api/maps/validate-coordinates
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:42.020Z"}
      Response Time: 0.005s

   GET /api/dashboard/stats
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:42.024Z"}
      Response Time: 0.004s

   GET /api/dashboard/financial-summary?startDate=2025-05-06&endDate=2025-06-05
      Status: 401
      Error: {"success":false,"error":{"code":"AUTHENTICATION_ERROR","message":"Access token is required"},"timestamp":"2025-06-05T13:04:42.030Z"}
      Response Time: 0.005s


⚡ PERFORMANCE STATS:
   Average Response Time: 0.010s
   Fastest Response: 0.002s
   Slowest Response: 0.247s

📝 Log File: logs/api_test_20250605_183422.log
================================================================================
