
================================================================================
🧪 CYMATICS API TEST SUITE SUMMARY
================================================================================

📊 OVERALL RESULTS:
   Total Tests: 81
   ✅ Passed: 72
   ❌ Failed: 9
   📈 Success Rate: 88.9%

📋 RESULTS BY CATEGORY:
   ROOT: 2/2 (100.0%)
   AUTH: 3/3 (100.0%)
   CLIENTS: 8/9 (88.9%)
   OUTCLIENTS: 7/7 (100.0%)
   PROJECTS: 10/10 (100.0%)
   FINANCIAL: 12/14 (85.7%)
   ASSETS: 7/7 (100.0%)
   ENTERTAINMENT: 8/8 (100.0%)
   CALENDAR: 11/11 (100.0%)
   MAPS: 2/8 (25.0%)
   DASHBOARD: 2/2 (100.0%)

❌ FAILED TESTS DETAILS:
   GET /api/clients/99999
      Status: 404
      Error: {"success":false,"error":{"code":"NOT_FOUND_ERROR","message":"Client not found"},"timestamp":"2025-06-05T13:07:26.681Z"}
      Response Time: 0.029s

   GET /api/financial/expenses/categories
      Status: 400
      Error: {"success":false,"error":{"code":"PARAMS_VALIDATION_ERROR","message":"Parameters validation failed","details":[{"field":"id","message":"\"id\" must be a number"}]},"timestamp":"2025-06-05T13:07:27.652Z"}
      Response Time: 0.011s

   GET /api/financial/expenses/totals
      Status: 400
      Error: {"success":false,"error":{"code":"PARAMS_VALIDATION_ERROR","message":"Parameters validation failed","details":[{"field":"id","message":"\"id\" must be a number"}]},"timestamp":"2025-06-05T13:07:27.658Z"}
      Response Time: 0.005s

   POST /api/maps/reverse-geocode
      Status: 400
      Error: {"success":false,"error":{"code":"VALIDATION_ERROR","message":"Validation failed","details":[{"field":"latitude","message":"\"latitude\" is required"},{"field":"longitude","message":"\"longitude\" is required"}]},"timestamp":"2025-06-05T13:07:28.300Z"}
      Response Time: 0.006s

   POST /api/maps/nearby-places
      Status: 400
      Error: {"success":false,"error":{"code":"VALIDATION_ERROR","message":"Validation failed","details":[{"field":"latitude","message":"\"latitude\" is required"},{"field":"longitude","message":"\"longitude\" is required"}]},"timestamp":"2025-06-05T13:07:28.427Z"}
      Response Time: 0.005s

   POST /api/maps/distance
      Status: 400
      Error: {"success":false,"error":{"code":"VALIDATION_ERROR","message":"Validation failed","details":[{"field":"origin.latitude","message":"\"origin.latitude\" is required"},{"field":"origin.longitude","message":"\"origin.longitude\" is required"},{"field":"destination.latitude","message":"\"destination.latitude\" is required"},{"field":"destination.longitude","message":"\"destination.longitude\" is required"}]},"timestamp":"2025-06-05T13:07:28.434Z"}
      Response Time: 0.007s

   GET /api/maps/static-map?lat=40.7128&lng=-74.0060&zoom=12
      Status: 400
      Error: {"success":false,"error":{"code":"QUERY_VALIDATION_ERROR","message":"Query validation failed","details":[{"field":"latitude","message":"\"latitude\" is required"},{"field":"longitude","message":"\"longitude\" is required"}]},"timestamp":"2025-06-05T13:07:28.442Z"}
      Response Time: 0.006s

   POST /api/maps/directions
      Status: 400
      Error: {"success":false,"error":{"code":"VALIDATION_ERROR","message":"Validation failed","details":[{"field":"origin.latitude","message":"\"origin.latitude\" is required"},{"field":"origin.longitude","message":"\"origin.longitude\" is required"},{"field":"destination.latitude","message":"\"destination.latitude\" is required"},{"field":"destination.longitude","message":"\"destination.longitude\" is required"}]},"timestamp":"2025-06-05T13:07:28.447Z"}
      Response Time: 0.004s

   POST /api/maps/validate-coordinates
      Status: 400
      Error: {"success":false,"error":{"code":"VALIDATION_ERROR","message":"Validation failed","details":[{"field":"latitude","message":"\"latitude\" is required"},{"field":"longitude","message":"\"longitude\" is required"}]},"timestamp":"2025-06-05T13:07:28.453Z"}
      Response Time: 0.005s


⚡ PERFORMANCE STATS:
   Average Response Time: 0.087s
   Fastest Response: 0.004s
   Slowest Response: 4.470s

📝 Log File: logs/api_test_20250605_183652.log
================================================================================
