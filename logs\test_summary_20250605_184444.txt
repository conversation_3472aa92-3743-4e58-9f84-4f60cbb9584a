
================================================================================
🧪 CYMATICS API TEST SUITE SUMMARY
================================================================================

📊 OVERALL RESULTS:
   Total Tests: 3
   ✅ Passed: 2
   ❌ Failed: 1
   📈 Success Rate: 66.7%

📋 RESULTS BY CATEGORY:
   ROOT: 2/2 (100.0%)
   AUTH: 0/1 (0.0%)

❌ FAILED TESTS DETAILS:
   POST /api/auth/send-otp
      Status: 500
      Error: {"success":false,"error":{"code":"DATABASE_ERROR","message":"Database operation failed","details":"\nInvalid `prisma.user.findUnique()` invocation in\nC:\\Users\\<USER>\\Desktop\\Code\\AIC\\Helper\\Harini\\Cymatics\\cymatics-backend\\src\\services\\auth.service.ts:44:36\n\n  41 const username = extractNameFromEmail(email);\n  42 \n  43 // Find or create user\n→ 44 let user = await prisma.user.findUnique(\nThe table `public.users` does not exist in the current database."},"timestamp":"2025-06-05T13:04:54.217Z"}
      Response Time: 0.032s


⚡ PERFORMANCE STATS:
   Average Response Time: 0.013s
   Fastest Response: 0.002s
   Slowest Response: 0.032s

📝 Log File: logs/api_test_20250605_183454.log
================================================================================
