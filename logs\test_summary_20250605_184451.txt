
================================================================================
🧪 CYMATICS API TEST SUITE SUMMARY
================================================================================

📊 OVERALL RESULTS:
   Total Tests: 73
   ✅ Passed: 69
   ❌ Failed: 4
   📈 Success Rate: 94.5%

📋 RESULTS BY CATEGORY:
   ROOT: 2/2 (100.0%)
   AUTH: 1/1 (100.0%)
   CLIENTS: 8/9 (88.9%)
   OUTCLIENTS: 7/7 (100.0%)
   PROJECTS: 3/4 (75.0%)
   FINANCIAL: 12/14 (85.7%)
   ASSETS: 7/7 (100.0%)
   ENTERTAINMENT: 8/8 (100.0%)
   CALENDAR: 11/11 (100.0%)
   MAPS: 8/8 (100.0%)
   DASHBOARD: 2/2 (100.0%)

❌ FAILED TESTS DETAILS:
   GET /api/clients/99999
      Status: 404
      Error: {"success":false,"error":{"code":"NOT_FOUND_ERROR","message":"Client not found"},"timestamp":"2025-06-05T13:14:48.750Z"}
      Response Time: 0.013s

   POST /api/projects
      Status: 400
      Error: {"success":false,"error":{"code":"VALIDATION_ERROR","message":"Client not found"},"timestamp":"2025-06-05T13:14:48.980Z"}
      Response Time: 0.026s

   GET /api/financial/expenses/categories
      Status: 400
      Error: {"success":false,"error":{"code":"PARAMS_VALIDATION_ERROR","message":"Parameters validation failed","details":[{"field":"id","message":"\"id\" must be a number"}]},"timestamp":"2025-06-05T13:14:49.070Z"}
      Response Time: 0.005s

   GET /api/financial/expenses/totals
      Status: 400
      Error: {"success":false,"error":{"code":"PARAMS_VALIDATION_ERROR","message":"Parameters validation failed","details":[{"field":"id","message":"\"id\" must be a number"}]},"timestamp":"2025-06-05T13:14:49.078Z"}
      Response Time: 0.007s


⚡ PERFORMANCE STATS:
   Average Response Time: 0.042s
   Fastest Response: 0.005s
   Slowest Response: 0.647s

📝 Log File: logs/api_test_20250605_184448.log
================================================================================
