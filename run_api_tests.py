#!/usr/bin/env python3
"""
Quick runner script for Cymatics API tests
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False

def main():
    print("🧪 Cymatics API Test Runner")
    print("=" * 40)
    
    # Check if requirements.txt exists and install dependencies
    if os.path.exists("requirements.txt"):
        print("📦 Installing requirements...")
        if not install_requirements():
            return False
    
    # Run the test suite
    try:
        from api_test_suite import main as run_tests
        return run_tests()
    except ImportError as e:
        print(f"❌ Failed to import test suite: {e}")
        return False
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Test suite completed successfully!")
    else:
        print("\n💥 Test suite failed!")
    exit(0 if success else 1)
